#pragma once

#include "Globals.h"

//---------------------------------------------------------------------
// 🎯	SISTEMA UNIVERSAL DE DETECÇÃO DE MODOS DE CÂMERA
//---------------------------------------------------------------------
// Sistema completo para detectar e adaptar-se a todos os modos de câmera
// possíveis no Marvel Rivals, garantindo compatibilidade universal do aimbot
//---------------------------------------------------------------------

namespace CameraSystemUniversal
{
    // Enumeração de todos os tipos de câmera detectados
    enum class ECameraType : uint8_t
    {
        Standard = 0,           // Câmera padrão do jogador
        ThirdPerson = 1,        // Câmera de terceira pessoa
        FirstPerson = 2,        // Câmera de primeira pessoa
        Ability = 3,            // Câmera de habilidade especial
        Transformation = 4,     // Câmera de transformação
        ObjectPossession = 5,   // Câmera de incorporação de objeto
        Ultimate = 6,           // Câmera de ultimate
        Spectator = 7,          // Câmera de espectador
        Cinematic = 8,          // Câmera cinemática
        Debug = 9,              // Câmera de debug
        Custom = 10,            // Câmera customizada
        Unknown = 255           // Tipo desconhecido
    };

    // Estados de transição de câmera
    enum class ECameraTransitionState : uint8_t
    {
        Stable = 0,             // Câmera estável
        Transitioning = 1,      // Em transição
        Blending = 2,           // Fazendo blend
        Pending = 3             // Aguardando mudança
    };

    // Modos de projeção suportados
    enum class ECameraProjectionMode : uint8_t
    {
        Perspective = 0,        // Perspectiva (padrão)
        Orthographic = 1        // Ortográfica
    };

    // Estrutura para informações detalhadas da câmera
    struct FCameraInfo
    {
        ECameraType Type = ECameraType::Unknown;
        ECameraTransitionState TransitionState = ECameraTransitionState::Stable;
        ECameraProjectionMode ProjectionMode = ECameraProjectionMode::Perspective;
        
        SDK::AActor* ViewTarget = nullptr;
        SDK::AActor* PendingViewTarget = nullptr;
        SDK::UCameraComponent* CameraComponent = nullptr;
        
        SDK::FVector Location = SDK::FVector();
        SDK::FRotator Rotation = SDK::FRotator();
        float FOV = 90.0f;
        float AspectRatio = 1.777f;
        
        bool bIsValid = false;
        bool bHasCustomComponent = false;
        bool bIsBlending = false;
        bool bIsLocalViewTarget = false;
        
        uint64_t LastUpdateTime = 0;
        uint32_t FramesSinceChange = 0;
    };

    // Estrutura para configuração de adaptação
    struct FCameraAdaptationConfig
    {
        bool bEnableUniversalAdaptation = true;
        bool bDetectTransformations = true;
        bool bDetectObjectPossession = true;
        bool bDetectAbilityCameras = true;
        bool bDetectUltimateCameras = true;
        bool bAdaptToProjectionChanges = true;
        bool bAdaptToFOVChanges = true;
        
        float MinFOVThreshold = 30.0f;
        float MaxFOVThreshold = 120.0f;
        float TransitionDetectionThreshold = 0.1f;
        uint32_t StabilityFrameCount = 5;
    };

    // Variáveis globais do sistema
    static FCameraInfo CurrentCameraInfo;
    static FCameraInfo PreviousCameraInfo;
    static FCameraAdaptationConfig AdaptationConfig;
    static bool bSystemInitialized = false;
    static uint64_t LastAnalysisTime = 0;

    //---------------------------------------------------------------------
    // 🔍	FUNÇÕES DE DETECÇÃO DE TIPO DE CÂMERA
    //---------------------------------------------------------------------

    // Detecta o tipo de câmera baseado no ViewTarget
    ECameraType DetectCameraTypeFromViewTarget(SDK::AActor* ViewTarget)
    {
        if (!IsValidObjectPtr(ViewTarget))
            return ECameraType::Unknown;

        // Verificar se é uma câmera de debug
        if (ViewTarget->IsA(SDK::ADebugCameraController::StaticClass()))
            return ECameraType::Debug;

        // Verificar se é uma câmera cinemática
        if (ViewTarget->IsA(SDK::ACameraActor::StaticClass()))
            return ECameraType::Cinematic;

        // Verificar se é o próprio jogador (padrão)
        if (ViewTarget == Variables::AcknowledgedPawn)
            return ECameraType::Standard;

        // Verificar se é outro personagem (espectador)
        if (ViewTarget->IsA(SDK::AMarvelBaseCharacter::StaticClass()))
            return ECameraType::Spectator;

        // Verificar se é um objeto especial (incorporação)
        if (ViewTarget->IsA(SDK::AMarvelSummonerBase::StaticClass()) ||
            ViewTarget->IsA(SDK::AMarvelAbilityTargetActor_Scope::StaticClass()))
            return ECameraType::ObjectPossession;

        return ECameraType::Custom;
    }

    // Detecta transformações baseado em componentes especiais
    bool DetectTransformationMode(SDK::AActor* ViewTarget)
    {
        if (!IsValidObjectPtr(ViewTarget))
            return false;

        // Verificar se tem componentes de transformação
        auto TransformComponent = ViewTarget->GetComponentByClass(SDK::USceneComponent::StaticClass());
        if (IsValidObjectPtr(TransformComponent))
        {
            // Verificar propriedades específicas de transformação
            // (implementação específica baseada nos componentes encontrados)
            return true;
        }

        return false;
    }

    // Detecta câmeras de habilidade baseado no estado do personagem
    bool DetectAbilityCamera(SDK::AMarvelBaseCharacter* Character)
    {
        if (!IsValidObjectPtr(Character))
            return false;

        // Verificar se está executando uma habilidade que muda a câmera
        // (implementação baseada nos estados de habilidade descobertos)
        return false;
    }

    //---------------------------------------------------------------------
    // 🔄	SISTEMA DE ANÁLISE CONTÍNUA
    //---------------------------------------------------------------------

    // Analisa o estado atual da câmera
    void AnalyzeCameraState()
    {
        if (!IsValidObjectPtr(Variables::PlayerCameraManager))
            return;

        // Obter informações básicas da câmera
        CurrentCameraInfo.Location = Variables::PlayerCameraManager->GetCameraLocation();
        CurrentCameraInfo.Rotation = Variables::PlayerCameraManager->GetCameraRotation();
        CurrentCameraInfo.FOV = Variables::PlayerCameraManager->GetFOVAngle();

        // Obter ViewTarget atual
        CurrentCameraInfo.ViewTarget = Variables::PlayerCameraManager->ViewTarget.Target;
        CurrentCameraInfo.PendingViewTarget = Variables::PlayerCameraManager->PendingViewTarget.Target;

        // Detectar tipo de câmera
        CurrentCameraInfo.Type = DetectCameraTypeFromViewTarget(CurrentCameraInfo.ViewTarget);

        // Detectar estado de transição
        if (CurrentCameraInfo.ViewTarget != CurrentCameraInfo.PendingViewTarget)
        {
            CurrentCameraInfo.TransitionState = ECameraTransitionState::Pending;
            CurrentCameraInfo.bIsBlending = true;
        }
        else if (CurrentCameraInfo.bIsBlending)
        {
            CurrentCameraInfo.TransitionState = ECameraTransitionState::Blending;
        }
        else
        {
            CurrentCameraInfo.TransitionState = ECameraTransitionState::Stable;
            CurrentCameraInfo.bIsBlending = false;
        }

        // Detectar mudanças significativas
        bool bSignificantChange = false;
        if (CurrentCameraInfo.Type != PreviousCameraInfo.Type ||
            CurrentCameraInfo.ViewTarget != PreviousCameraInfo.ViewTarget ||
            abs(CurrentCameraInfo.FOV - PreviousCameraInfo.FOV) > AdaptationConfig.TransitionDetectionThreshold)
        {
            bSignificantChange = true;
            CurrentCameraInfo.FramesSinceChange = 0;
        }
        else
        {
            CurrentCameraInfo.FramesSinceChange++;
        }

        // Verificar estabilidade
        if (CurrentCameraInfo.FramesSinceChange >= AdaptationConfig.StabilityFrameCount)
        {
            CurrentCameraInfo.TransitionState = ECameraTransitionState::Stable;
        }

        // Atualizar timestamp
        CurrentCameraInfo.LastUpdateTime = GetTickCount64();
        CurrentCameraInfo.bIsValid = true;

        // Salvar estado anterior
        PreviousCameraInfo = CurrentCameraInfo;
    }

    //---------------------------------------------------------------------
    // 🎯	SISTEMA DE ADAPTAÇÃO DO AIMBOT
    //---------------------------------------------------------------------

    // Adapta o aimbot para o modo de câmera atual
    void AdaptAimbotForCameraMode()
    {
        if (!CurrentCameraInfo.bIsValid || !AdaptationConfig.bEnableUniversalAdaptation)
            return;

        switch (CurrentCameraInfo.Type)
        {
            case ECameraType::Standard:
                // Configuração padrão - sem adaptações especiais
                break;

            case ECameraType::ThirdPerson:
                // Adaptar para câmera de terceira pessoa
                // Pode precisar de offset adicional
                break;

            case ECameraType::Ability:
            case ECameraType::Ultimate:
                // Adaptar para câmeras de habilidade
                // Pode ter FOV ou perspectiva diferentes
                break;

            case ECameraType::ObjectPossession:
                // Adaptar para incorporação de objetos
                // Posição da câmera pode ser muito diferente
                break;

            case ECameraType::Transformation:
                // Adaptar para transformações
                // Tamanho e perspectiva podem mudar drasticamente
                break;

            case ECameraType::Cinematic:
            case ECameraType::Spectator:
                // Desabilitar aimbot em modos não-jogáveis
                return;

            default:
                // Modo desconhecido - usar configuração conservadora
                break;
        }
    }

    //---------------------------------------------------------------------
    // 🔧	FUNÇÕES PÚBLICAS DA API
    //---------------------------------------------------------------------

    // Inicializa o sistema
    void Initialize()
    {
        if (bSystemInitialized)
            return;

        // Configurar valores padrão
        AdaptationConfig = FCameraAdaptationConfig();
        CurrentCameraInfo = FCameraInfo();
        PreviousCameraInfo = FCameraInfo();

        bSystemInitialized = true;
    }

    // Atualiza o sistema (chamado a cada frame)
    void Update()
    {
        if (!bSystemInitialized)
            Initialize();

        // Limitar frequência de análise para performance
        uint64_t currentTime = GetTickCount64();
        if (currentTime - LastAnalysisTime < 16) // ~60 FPS
            return;

        LastAnalysisTime = currentTime;

        // Analisar estado da câmera
        AnalyzeCameraState();

        // Adaptar aimbot se necessário
        AdaptAimbotForCameraMode();
    }

    // Obtém informações da câmera atual
    const FCameraInfo& GetCurrentCameraInfo()
    {
        return CurrentCameraInfo;
    }

    // Verifica se a câmera está em um modo compatível com aimbot
    bool IsCameraCompatibleWithAimbot()
    {
        if (!CurrentCameraInfo.bIsValid)
            return false;

        switch (CurrentCameraInfo.Type)
        {
            case ECameraType::Standard:
            case ECameraType::ThirdPerson:
            case ECameraType::FirstPerson:
            case ECameraType::Ability:
            case ECameraType::Ultimate:
            case ECameraType::Transformation:
            case ECameraType::ObjectPossession:
            case ECameraType::Custom:
                return CurrentCameraInfo.TransitionState == ECameraTransitionState::Stable;

            case ECameraType::Cinematic:
            case ECameraType::Spectator:
            case ECameraType::Debug:
            default:
                return false;
        }
    }

    // Obtém a posição da câmera adaptada para o modo atual
    SDK::FVector GetAdaptedCameraLocation()
    {
        if (!CurrentCameraInfo.bIsValid)
            return Variables::CameraLocation;

        // Retornar posição adaptada baseada no tipo de câmera
        return CurrentCameraInfo.Location;
    }

    // Obtém a rotação da câmera adaptada para o modo atual
    SDK::FRotator GetAdaptedCameraRotation()
    {
        if (!CurrentCameraInfo.bIsValid)
            return Variables::CameraRotation;

        return CurrentCameraInfo.Rotation;
    }

    // Configura o sistema de adaptação
    void SetAdaptationConfig(const FCameraAdaptationConfig& Config)
    {
        AdaptationConfig = Config;
    }

    // Obtém estatísticas do sistema
    void GetSystemStats(int& DetectedModes, int& Transitions, float& Stability)
    {
        DetectedModes = static_cast<int>(CurrentCameraInfo.Type);
        Transitions = CurrentCameraInfo.FramesSinceChange;
        Stability = CurrentCameraInfo.TransitionState == ECameraTransitionState::Stable ? 1.0f : 0.0f;
    }
}
