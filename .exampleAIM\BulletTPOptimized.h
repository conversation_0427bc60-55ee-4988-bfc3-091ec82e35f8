#pragma once
#include "Globals.h"
#include "DebugMenu.h" // Menu de debug
#include <cmath>

namespace BulletTPOptimized
{
    // Estrutura para armazenar informações sobre o alvo atual
    struct TargetInfo
    {
        SDK::AMarvelBaseCharacter *Character;
        SDK::FVector Position;
        float Distance;
        uint64_t LastUpdateTime;
        bool IsValid;
        bool NoTargetsAvailable;     // Flag para indicar que não há alvos disponíveis
        uint64_t LastEmptyCheckTime; // Timestamp da última verificação que resultou em nenhum alvo
        int EmptyCheckCount;         // Contador de verificações vazias consecutivas para backoff exponencial
    };

    // Estrutura para armazenar informações sobre projéteis
    struct ProjectileCache
    {
        SDK::TArray<SDK::AActor *> Projectiles;
        uint64_t LastUpdateTime;
        bool IsValid;
    };

    // Cache do alvo atual para reduzir chamadas a SelectTarget
    static TargetInfo CurrentTarget = {nullptr, SDK::FVector(), 0.0f, 0, false, false, 0, 0};

    // Cache de projéteis para reduzir chamadas a GetAllActorsOfClass
    static ProjectileCache CachedProjectiles = {SDK::TArray<SDK::AActor *>(), 0, false};

    // Intervalo mínimo entre atualizações do alvo (em milissegundos)
    static const uint64_t TARGET_UPDATE_INTERVAL = 100; // 10 atualizações por segundo

    // Intervalo mínimo entre atualizações dos projéteis (em milissegundos)
    static const uint64_t PROJECTILE_UPDATE_INTERVAL = 50; // 20 atualizações por segundo

    // Função para obter o tamanho do cache de projéteis sem atualizá-lo
    int GetProjectileCacheSize()
    {
        return CachedProjectiles.IsValid ? CachedProjectiles.Projectiles.Num() : 0;
    }

    // Intervalo base para verificações quando não há alvos (em milissegundos)
    static const uint64_t EMPTY_CHECK_BASE_INTERVAL = 500; // 2 atualizações por segundo

    // Fator de backoff exponencial para verificações vazias
    static const int BACKOFF_FACTOR = 2;

    // Intervalo máximo para verificações vazias (em milissegundos)
    static const uint64_t MAX_EMPTY_CHECK_INTERVAL = 2000; // No máximo a cada 2 segundos

    // Flag para controlar se apenas balas (não skills) devem ser teleportadas
    static bool OnlyBullets = false;

    // Contadores para debug
    static int BulletsTeleported = 0;
    static int SkillsTeleported = 0;
    static int TotalProjectilesProcessed = 0;
    static uint64_t LastBulletTPTime = 0;
    static float LastBulletTPDistance = 0.0f;

    // Função para verificar se um projétil é uma bala regular ou uma skill
    bool IsRegularBullet(SDK::AActor *Bullet)
    {
        if (!IsValidPtr(Bullet))
            return false;

        // Verificar se a opção "apenas balas" está ativada
        if (!mods::bulletTPOnlyBullets)
            return true; // Se a opção estiver desativada, permitir todos os projéteis

        // Verificar o tamanho do projétil - skills geralmente são maiores
        if (IsValidPtr(Bullet->RootComponent))
        {
            // Verificar o tamanho do componente
            SDK::FVector BulletExtent = Bullet->GetTransform().Scale3D;

            // Balas regulares geralmente têm tamanho menor
            if (BulletExtent.X <= 1.5f && BulletExtent.Y <= 1.5f && BulletExtent.Z <= 1.5f)
            {
                // Verificar a velocidade do projétil
                SDK::FVector Velocity = Bullet->GetVelocity();
                float Speed = Velocity.Magnitude();

                // Balas regulares geralmente têm velocidades mais altas e consistentes
                if (Speed > 800.0f && Speed < 8000.0f)
                    return true;
            }

            // Se o tamanho for muito grande, provavelmente é uma skill
            if (BulletExtent.X > 4.0f || BulletExtent.Y > 4.0f || BulletExtent.Z > 4.0f)
                return false;
        }

        // Verificar a velocidade do projétil
        SDK::FVector Velocity = Bullet->GetVelocity();
        float Speed = Velocity.Magnitude();

        // Balas regulares geralmente têm velocidades mais altas e consistentes
        if (Speed > 1000.0f && Speed < 5000.0f)
            return true;

        // Se não conseguimos determinar com certeza, assumimos que é uma bala regular
        // para manter a compatibilidade com o comportamento anterior
        return true;
    }

    // Função para obter a velocidade do projétil usando UShootingLogic_AbilityProjectile
    float GetProjectileSpeedFromWeapon(SDK::AMarvelBaseCharacter *LocalCharacter)
    {
        if (!IsValidPtr(LocalCharacter))
            return 400.0f; // Valor padrão

        // Obter o componente de equipamento
        auto *EquipComp = LocalCharacter->EquipComponent;
        if (!IsValidPtr(EquipComp))
            return 400.0f;

        // Obter a arma atual
        auto *Weapon = EquipComp->GetCurrentWeapon();
        if (!IsValidPtr(Weapon))
            return 400.0f;

        // Obter a lógica de tiro para o modo atual
        auto *ShootLogic = Weapon->GetShootingLogic(Weapon->CurrentModeIndex);
        if (!IsValidPtr(ShootLogic))
            return 400.0f;

        // Verificar se é um projétil de habilidade
        if (!ShootLogic->IsA(SDK::UShootingLogic_AbilityProjectile::StaticClass()))
            return 400.0f;

        // Converter para o tipo específico
        auto *ProjLogic = static_cast<SDK::UShootingLogic_AbilityProjectile *>(ShootLogic);

        // Obter os dados do projétil
        const auto &Params = ProjLogic->GetProjectileData().ProjectileAgent;

        // Obter a velocidade de voo
        float FlySpeed = Params.FlySpeed;

        // Verificar se a velocidade é válida
        if (FlySpeed <= 1.0f)
            return 400.0f; // Usar valor padrão se a velocidade for muito baixa

        return FlySpeed;
    }

    // Função para obter a velocidade do projétil do jogador local
    // Usa apenas o SDK para obter a velocidade em tempo real
    float GetLocalPlayerProjectileSpeed()
    {
        return CheatFeatures::GetLocalPlayerProjectileSpeed();
    }

    // Função para obter projéteis com cache para reduzir chamadas a GetAllActorsOfClass
    SDK::TArray<SDK::AActor *> GetProjectiles(SDK::UWorld *World)
    {
        // Obter o tempo atual
        uint64_t CurrentTime = GetTickCount64();

        // Verificar se o cache de projéteis ainda é válido
        if (CachedProjectiles.IsValid &&
            CurrentTime - CachedProjectiles.LastUpdateTime < PROJECTILE_UPDATE_INTERVAL)
        {
            // O cache ainda é válido, retornar os projéteis em cache
            return CachedProjectiles.Projectiles;
        }

        // Precisamos atualizar o cache de projéteis
        SDK::TArray<SDK::AActor *> Projectiles;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(SDK::UGameplayStatics::StaticClass()) &&
            IsValidObjectPtr(SDK::AGameplayAbilityTargetActor::StaticClass()))
        {
            SDK::UGameplayStatics::GetAllActorsOfClass(World, SDK::AGameplayAbilityTargetActor::StaticClass(), &Projectiles);
        }

        // Atualizar o cache
        CachedProjectiles.Projectiles = Projectiles;
        CachedProjectiles.LastUpdateTime = CurrentTime;
        CachedProjectiles.IsValid = true;

        return Projectiles;
    }

    // Função para atualizar o alvo atual com menos frequência
    SDK::AMarvelBaseCharacter *UpdateTarget(SDK::TArray<SDK::AActor *> &ActorList,
                                            SDK::APlayerController *PlayerController,
                                            SDK::FName HeadSocketName,
                                            float ActualFovCircle,
                                            float MaxProcessDistance)
    {
        // Obter o tempo atual
        uint64_t CurrentTime = GetTickCount64();

        // Verificar se o alvo atual ainda é válido
        if (CurrentTarget.IsValid &&
            IsValidObjectPtr(CurrentTarget.Character) &&
            CurrentTarget.Character->GetCurrentHealth() > 0.0f &&
            CurrentTime - CurrentTarget.LastUpdateTime < TARGET_UPDATE_INTERVAL)
        {
            // O alvo ainda é válido e não passou tempo suficiente para atualizar
            return CurrentTarget.Character;
        }

        // Verificar se sabemos que não há alvos disponíveis (cache negativo)
        if (CurrentTarget.NoTargetsAvailable)
        {
            // Calcular o intervalo de verificação com backoff exponencial
            uint64_t currentInterval = EMPTY_CHECK_BASE_INTERVAL;

            // Aplicar backoff exponencial com base no número de verificações vazias consecutivas
            for (int i = 0; i < CurrentTarget.EmptyCheckCount && i < 3; i++) // Limitar a 3 duplicações
            {
                currentInterval *= BACKOFF_FACTOR;
            }

            // Limitar ao intervalo máximo
            currentInterval = (currentInterval < MAX_EMPTY_CHECK_INTERVAL) ? currentInterval : MAX_EMPTY_CHECK_INTERVAL;

            // Verificar se passou tempo suficiente desde a última verificação vazia
            if (CurrentTime - CurrentTarget.LastEmptyCheckTime < currentInterval)
            {
                // Não passou tempo suficiente, continuar usando o cache negativo

                // Atualizar informações de debug
                DebugMenu::UpdateNegativeCacheInfo(CurrentTarget.NoTargetsAvailable,
                                                   CurrentTarget.EmptyCheckCount,
                                                   CurrentTarget.LastEmptyCheckTime,
                                                   currentInterval);

                return nullptr;
            }
        }

        // Precisamos selecionar um novo alvo
        SDK::AMarvelBaseCharacter *NewTarget = CheatFeatures::SelectTarget(
            ActorList, PlayerController, HeadSocketName, ActualFovCircle, MaxProcessDistance);

        // Atualizar as informações do alvo atual
        if (IsValidObjectPtr(NewTarget))
        {
            // Encontramos um alvo válido
            CurrentTarget.Character = NewTarget;

            // Obter a posição da cabeça do alvo
            SDK::USkeletalMeshComponent *TargetMesh = NewTarget->GetMesh();
            if (IsValidPtr(TargetMesh) && TargetMesh->SkeletalMesh)
            {
                CurrentTarget.Position = TargetMesh->GetSocketLocation(HeadSocketName);

                // Calcular a distância até o alvo
                if (IsValidObjectPtr(PlayerController) && IsValidObjectPtr(PlayerController->PlayerCameraManager))
                {
                    SDK::FVector CameraLocation = PlayerController->PlayerCameraManager->GetCameraLocation();
                    CurrentTarget.Distance = (CurrentTarget.Position - CameraLocation).Magnitude();
                }
            }

            // Atualizar o estado do cache
            CurrentTarget.LastUpdateTime = CurrentTime;
            CurrentTarget.IsValid = true;
            CurrentTarget.NoTargetsAvailable = false;
            CurrentTarget.EmptyCheckCount = 0; // Resetar o contador de verificações vazias

            // Atualizar informações de debug
            DebugMenu::UpdateNegativeCacheInfo(false, 0, 0, 0);
        }
        else
        {
            // Nenhum alvo válido encontrado
            CurrentTarget.Character = nullptr;
            CurrentTarget.Position = SDK::FVector();
            CurrentTarget.Distance = 0.0f;
            CurrentTarget.LastUpdateTime = CurrentTime;
            CurrentTarget.IsValid = false;

            // Atualizar o cache negativo
            CurrentTarget.NoTargetsAvailable = true;
            CurrentTarget.LastEmptyCheckTime = CurrentTime;
            CurrentTarget.EmptyCheckCount++; // Incrementar o contador de verificações vazias

            // Calcular o intervalo atual para o debug
            uint64_t currentInterval = EMPTY_CHECK_BASE_INTERVAL;
            for (int i = 0; i < CurrentTarget.EmptyCheckCount && i < 3; i++)
            {
                currentInterval *= BACKOFF_FACTOR;
            }
            currentInterval = (currentInterval < MAX_EMPTY_CHECK_INTERVAL) ? currentInterval : MAX_EMPTY_CHECK_INTERVAL;

            // Atualizar informações de debug
            DebugMenu::UpdateNegativeCacheInfo(true, CurrentTarget.EmptyCheckCount, CurrentTime, currentInterval);
        }

        return CurrentTarget.Character;
    }

    // Função otimizada para teleportar projéteis
    void RunBulletTP(SDK::UWorld *World,
                     SDK::APlayerController *PlayerController,
                     SDK::APlayerCameraManager *PlayerCameraManager,
                     SDK::AMarvelBaseCharacter *TargetPlayer,
                     SDK::FName HeadSocketName,
                     SDK::FName NeckSocketName,
                     float ActualBulletTPFovCircle)
    {
        if (!mods::bullet_tp || !IsValidObjectPtr(TargetPlayer))
            return;

        // Atualizar a flag OnlyBullets com base na configuração do usuário
        OnlyBullets = mods::bulletTPOnlyBullets;

        SDK::USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
        if (!IsValidPtr(TargetMesh) || !TargetMesh->SkeletalMesh)
            return;

        SDK::FVector2D TargetHead2D;
        PlayerController->ProjectWorldLocationToScreen(TargetMesh->GetSocketLocation(HeadSocketName), &TargetHead2D, true);

        if (!InCircle(ActualBulletTPFovCircle, TargetHead2D))
            return;

        // Verificar se alguma das teclas configuradas para o BulletTP está pressionada
        bool IsBulletTPKeyPressed = Keys::IsBulletTPKeyPressed(PlayerController);

        // Atualizar informações de debug do BulletTP
        DebugMenu::UpdateBulletTPInfo(
            mods::bullet_tp,            // enabled
            IsBulletTPKeyPressed,       // active
            BulletsTeleported,          // bullets
            SkillsTeleported,           // skills
            TotalProjectilesProcessed,  // total
            LastBulletTPDistance,       // distance
            mods::bulletTPEnableHoming, // homing
            OnlyBullets,                // onlyBullets
            mods::bullet_tp_target,     // socket
            LastBulletTPTime            // lastTime
        );

        if (!IsBulletTPKeyPressed)
        {
            Variables::BulletTPTimer = 0.0f;
            return;
        }

        // Escolher o socket alvo (cabeça, pescoço ou aleatório)
        SDK::FName SocketName = (mods::bullet_tp_target == 0) ? HeadSocketName : (mods::bullet_tp_target == 1) ? NeckSocketName
                                                                             : (rand() % 2 == 0)               ? HeadSocketName
                                                                                                               : NeckSocketName;

        if (SocketName == NeckSocketName && !TargetMesh->DoesSocketExist(NeckSocketName))
        {
            SocketName = HeadSocketName;
        }

        SDK::FVector TargetHead3D = TargetMesh->GetSocketLocation(SocketName);
        if (TargetHead3D.IsZero() || !IsValidObjectPtr(Variables::AcknowledgedPawn))
            return;

        // Obter a posição do alvo
        SDK::FVector TargetPosition = TargetHead3D;

        // Obter a posição da câmera (origem do tiro)
        SDK::FVector CameraLocation = PlayerCameraManager->GetCameraLocation();

        // Obter a velocidade do projétil usando a função melhorada
        float ProjectileSpeed = GetLocalPlayerProjectileSpeed();

        // Obter todos os projéteis no mundo usando o cache
        SDK::TArray<SDK::AActor *> Bullets = GetProjectiles(World);

        for (int i = 0; i < Bullets.Num(); i++)
        {
            if (!Bullets.IsValidIndex(i))
                continue;

            auto Bullet = Bullets[i];
            if (!IsValidPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
                continue;

            // Se a opção "apenas balas" estiver ativada, verificar se é uma bala regular
            if (OnlyBullets && !IsRegularBullet(Bullet))
                continue;

            // Verificar se o projétil já passou do alvo
            SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

            // Calcular direções normalizadas
            SDK::FVector DirectionToTarget = (TargetHead3D - CameraLocation);
            DirectionToTarget = DirectionToTarget.GetNormalized();

            SDK::FVector DirectionToBullet = (BulletLocation - CameraLocation);
            DirectionToBullet = DirectionToBullet.GetNormalized();

            // Calcular distâncias
            float BulletDistance = (BulletLocation - CameraLocation).Magnitude();
            float TargetDistance = (TargetHead3D - CameraLocation).Magnitude();

            // Calcular o produto escalar para verificar se o projétil está indo na direção do alvo
            float DotProduct = DirectionToTarget.Dot(DirectionToBullet);

            // Só teleportar se o projétil estiver indo na direção do alvo e não tiver passado dele
            if (DotProduct > 0.7f && BulletDistance < TargetDistance)
            {
                // Obter a velocidade atual do projétil
                SDK::FVector BulletVelocity = Bullet->GetVelocity();
                float CurrentSpeed = BulletVelocity.Magnitude();

                // Se a velocidade atual for muito baixa, usar a velocidade da arma
                if (CurrentSpeed < 100.0f)
                    CurrentSpeed = ProjectileSpeed;

                // Calcular o tempo que o projétil levaria para chegar ao alvo com base na velocidade
                float TimeToTarget = TargetDistance / CurrentSpeed;

                // Calcular a distância que o projétil já percorreu
                float DistanceTraveled = BulletDistance;

                // Calcular o tempo que o projétil já viajou
                float TimeTraveled = DistanceTraveled / CurrentSpeed;

                // Calcular a porcentagem do tempo total que já passou
                float TimeRatio = TimeTraveled / TimeToTarget;

                // Limitar a porcentagem para evitar teleportes muito próximos do alvo
                TimeRatio = mods::Clamp(TimeRatio, 0.0f, 0.9f);

                // Calcular a posição final para o teleporte
                SDK::FVector FinalPosition;
                SDK::FRotator FinalRotation;

                // Usar a mesma função de predição do Aimbot para maior precisão
                SDK::FVector PredictedTargetPosition;

                // Verificar se o personagem local é válido
                if (IsValidObjectPtr(Variables::AcknowledgedPawn))
                {
                    // Converter o pawn local para AMarvelBaseCharacter
                    auto *LocalCharacter = static_cast<SDK::AMarvelBaseCharacter *>(Variables::AcknowledgedPawn);
                    if (IsValidObjectPtr(LocalCharacter))
                    {
                        // Prever a posição do alvo usando a função híbrida de predição
                        // Importante: Para o BulletTP, não queremos considerar o smoothing, pois os projéteis
                        // não são afetados pelo atraso da câmera. Portanto, vamos temporariamente desativar
                        // o smoothing para o cálculo da predição.

                        // Salvar os valores atuais de smoothing
                        float originalSmoothAmount = mods::smoothAmount;
                        float originalSmoothAmountPitch = mods::smoothAmountPitch;
                        float originalSmoothAmountYaw = mods::smoothAmountYaw;

                        // Definir temporariamente o smoothing para zero
                        mods::smoothAmount = 0.0f;
                        mods::smoothAmountPitch = 0.0f;
                        mods::smoothAmountYaw = 0.0f;

                        // Prever a posição do alvo sem considerar o smoothing
                        PredictedTargetPosition = CheatFeatures::PredictProjectileHybrid(LocalCharacter, TargetPlayer, CameraLocation);

                        // Restaurar os valores originais de smoothing
                        mods::smoothAmount = originalSmoothAmount;
                        mods::smoothAmountPitch = originalSmoothAmountPitch;
                        mods::smoothAmountYaw = originalSmoothAmountYaw;

                        // Se a posição prevista for inválida, usar o método antigo
                        if (PredictedTargetPosition.IsZero())
                        {
                            // Obter a velocidade do alvo para prever sua posição futura
                            SDK::FVector TargetVelocity = TargetPlayer->GetVelocity();

                            // Calcular o tempo restante para o projétil atingir o alvo
                            float RemainingTime = TimeToTarget - TimeTraveled;

                            // Prever a posição futura do alvo
                            PredictedTargetPosition = TargetPosition + (TargetVelocity * RemainingTime);
                        }
                    }
                    else
                    {
                        // Fallback para o método antigo se o personagem local não for válido
                        SDK::FVector TargetVelocity = TargetPlayer->GetVelocity();
                        float RemainingTime = TimeToTarget - TimeTraveled;
                        PredictedTargetPosition = TargetPosition + (TargetVelocity * RemainingTime);
                    }
                }
                else
                {
                    // Fallback para o método antigo se o personagem local não for válido
                    SDK::FVector TargetVelocity = TargetPlayer->GetVelocity();
                    float RemainingTime = TimeToTarget - TimeTraveled;
                    PredictedTargetPosition = TargetPosition + (TargetVelocity * RemainingTime);
                }

                // Calcular a nova direção para o alvo previsto
                SDK::FVector DirectionToPredictedTarget = (PredictedTargetPosition - BulletLocation).GetNormalized();

                // Calcular a posição de teleporte para garantir que o projétil acerte o alvo
                // Implementação melhorada para garantir o acerto

                // Calcular a distância até o alvo
                float DistanceToTarget = (PredictedTargetPosition - BulletLocation).Magnitude();

                // Calcular a posição final para o teleporte
                // Usar uma distância muito pequena para garantir o acerto
                float OffsetDistance = 2.0f; // Distância mínima para garantir o acerto

                // Se a distância for muito pequena, usar uma distância ainda menor
                if (DistanceToTarget < 20.0f)
                {
                    OffsetDistance = 0.5f; // Distância mínima para alvos muito próximos
                }

                // Calcular a posição final
                FinalPosition = PredictedTargetPosition - (DirectionToPredictedTarget * OffsetDistance);

                // Calcular a rotação para apontar para o alvo
                FinalRotation = SDK::UKismetMathLibrary::FindLookAtRotation(FinalPosition, PredictedTargetPosition);

                // Calcular a velocidade necessária para atingir o alvo
                SDK::FVector TargetDirection = (PredictedTargetPosition - FinalPosition).GetNormalized();
                SDK::FVector NewVelocity = TargetDirection * CurrentSpeed;

                // Teleportar o projétil para a posição calculada
                SDK::FHitResult HitResult;
                bool TeleportSuccess = Bullet->K2_SetActorLocationAndRotation(FinalPosition, FinalRotation, false, &HitResult, false);

                // Verificar se o teleporte foi bem-sucedido
                if (TeleportSuccess)
                {
                    // Tentar definir a velocidade do projétil diretamente
                    if (IsValidPtr(Bullet->RootComponent))
                    {
                        // Tentar obter o componente de movimento do projétil
                        SDK::UPrimitiveComponent *PrimComp = static_cast<SDK::UPrimitiveComponent *>(Bullet->RootComponent);
                        if (IsValidPtr(PrimComp))
                        {
                            // Definir a velocidade linear do componente
                            // Converter a string "None" para FName usando UKismetStringLibrary
                            SDK::FName NoneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(L"None"));
                            PrimComp->SetPhysicsLinearVelocity(NewVelocity, false, NoneName);

                            // Registrar o acerto para as estatísticas de debug
                            DebugMenu::RegisterHit();

                            // Incrementar o contador apropriado
                            bool isBullet = IsRegularBullet(Bullet);
                            if (isBullet)
                                BulletsTeleported++;
                            else
                                SkillsTeleported++;

                            TotalProjectilesProcessed++;
                            LastBulletTPTime = GetTickCount64();
                            LastBulletTPDistance = DistanceToTarget;

                            // Usar a função de incremento do DebugMenu
                            DebugMenu::IncrementBulletsTeleported(isBullet);
                        }
                    }
                }
                else
                {
                    // Registrar o erro para as estatísticas de debug
                    DebugMenu::RegisterMiss();
                }

                // Implementar funcionalidade de homing apenas se estiver ativada no menu
                if (mods::bulletTPEnableHoming)
                {
                    // Como o SDK não fornece acesso direto ao ProjectileMovementComponent,
                    // simulamos o efeito de homing usando rotação e velocidade

                    // Definir a rotação para apontar para o alvo
                    // Já feito acima com FinalRotation = SDK::UKismetMathLibrary::FindLookAtRotation(FinalPosition, PredictedTargetPosition);

                    // Definir a velocidade para ir em direção ao alvo
                    // Já feito acima com NewVelocity = TargetDirection * CurrentSpeed;

                    // Adicionar um pequeno ajuste na velocidade para garantir que o projétil acerte o alvo
                    // Aumentar ligeiramente a velocidade para compensar a resistência do ar e outros fatores
                    NewVelocity = NewVelocity * 1.05f;

                    // Registrar que estamos usando homing para as estatísticas de debug
                    DebugMenu::RegisterHomingUsed();
                }
                else
                {
                    // Se o homing estiver desativado, usar uma lógica mais simples de teleporte
                    // Apenas definir a velocidade na direção do alvo sem ajustes adicionais
                    NewVelocity = TargetDirection * CurrentSpeed;
                }
            }
        }

        Bullets.Clear();
        Variables::BulletTPTimer = 0.0f;
    }
}
