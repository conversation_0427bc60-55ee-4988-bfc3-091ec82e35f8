#pragma once

#include "CameraSystemUniversal.h"
#include "Modules.h"

//---------------------------------------------------------------------
// 🎯	INTEGRAÇÃO DO SISTEMA DE CÂMERA COM O AIMBOT
//---------------------------------------------------------------------
// Integra o sistema universal de câmera com o aimbot existente,
// garantindo compatibilidade com todos os modos de câmera
//---------------------------------------------------------------------

namespace CameraAimbotIntegration
{
    // Estrutura para configurações específicas do aimbot por modo de câmera
    struct FAimbotCameraConfig
    {
        float FOVMultiplier = 1.0f;         // Multiplicador do FOV para este modo
        float SensitivityMultiplier = 1.0f;  // Multiplicador da sensibilidade
        float SmoothingMultiplier = 1.0f;    // Multiplicador da suavização
        SDK::FVector LocationOffset = SDK::FVector(); // Offset da posição da câmera
        bool bRequiresSpecialHandling = false; // Se requer tratamento especial
        bool bUseAlternativeProjection = false; // Se usa projeção alternativa
    };

    // Configurações por tipo de câmera
    static std::unordered_map<CameraSystemUniversal::ECameraType, FAimbotCameraConfig> CameraConfigs = {
        {CameraSystemUniversal::ECameraType::Standard, {1.0f, 1.0f, 1.0f, SDK::FVector(), false, false}},
        {CameraSystemUniversal::ECameraType::ThirdPerson, {1.0f, 0.9f, 1.1f, SDK::FVector(), false, false}},
        {CameraSystemUniversal::ECameraType::FirstPerson, {1.0f, 1.1f, 0.9f, SDK::FVector(), false, false}},
        {CameraSystemUniversal::ECameraType::Ability, {0.8f, 0.8f, 1.2f, SDK::FVector(), true, false}},
        {CameraSystemUniversal::ECameraType::Ultimate, {0.7f, 0.7f, 1.3f, SDK::FVector(), true, false}},
        {CameraSystemUniversal::ECameraType::Transformation, {0.6f, 0.6f, 1.5f, SDK::FVector(), true, true}},
        {CameraSystemUniversal::ECameraType::ObjectPossession, {0.9f, 0.5f, 1.8f, SDK::FVector(), true, true}},
        {CameraSystemUniversal::ECameraType::Custom, {0.8f, 0.8f, 1.2f, SDK::FVector(), true, false}}
    };

    //---------------------------------------------------------------------
    // 🔧	DECLARAÇÕES DE FUNÇÕES
    //---------------------------------------------------------------------

    // Declarações antecipadas das funções especializadas
    void RunSpecializedAimbot(SDK::UWorld* World,
                             SDK::APlayerController* PlayerController,
                             SDK::APlayerCameraManager* PlayerCameraManager,
                             SDK::AMarvelBaseCharacter* TargetPlayer,
                             SDK::FName HeadSocketName,
                             float adaptedFovCircle,
                             CameraSystemUniversal::ECameraType cameraType);

    void RunStandardAimbotWithAdaptation(SDK::UWorld* World,
                                        SDK::APlayerController* PlayerController,
                                        SDK::APlayerCameraManager* PlayerCameraManager,
                                        SDK::AMarvelBaseCharacter* TargetPlayer,
                                        SDK::FName HeadSocketName,
                                        float adaptedFovCircle);

    void RunTransformationAimbot(SDK::UWorld* World,
                                SDK::APlayerController* PlayerController,
                                SDK::APlayerCameraManager* PlayerCameraManager,
                                SDK::AMarvelBaseCharacter* TargetPlayer,
                                SDK::FName HeadSocketName,
                                float adaptedFovCircle);

    void RunObjectPossessionAimbot(SDK::UWorld* World,
                                  SDK::APlayerController* PlayerController,
                                  SDK::APlayerCameraManager* PlayerCameraManager,
                                  SDK::AMarvelBaseCharacter* TargetPlayer,
                                  SDK::FName HeadSocketName,
                                  float adaptedFovCircle);

    void RunAbilityAimbot(SDK::UWorld* World,
                         SDK::APlayerController* PlayerController,
                         SDK::APlayerCameraManager* PlayerCameraManager,
                         SDK::AMarvelBaseCharacter* TargetPlayer,
                         SDK::FName HeadSocketName,
                         float adaptedFovCircle);

    void RunAlternativeProjectionAimbot(SDK::UWorld* World,
                                       SDK::APlayerController* PlayerController,
                                       SDK::APlayerCameraManager* PlayerCameraManager,
                                       SDK::AMarvelBaseCharacter* TargetPlayer,
                                       SDK::FName HeadSocketName,
                                       float adaptedFovCircle);

    void ApplyAimbotRotation(SDK::APlayerController* PlayerController,
                            const SDK::FRotator& targetRotation,
                            float smoothing);

    //---------------------------------------------------------------------
    // 🔧	FUNÇÕES DE ADAPTAÇÃO
    //---------------------------------------------------------------------

    // Obtém a configuração para o modo de câmera atual
    FAimbotCameraConfig GetConfigForCurrentCamera()
    {
        auto currentInfo = CameraSystemUniversal::GetCurrentCameraInfo();
        auto it = CameraConfigs.find(currentInfo.Type);
        
        if (it != CameraConfigs.end())
            return it->second;
        
        // Retornar configuração padrão se não encontrar
        return CameraConfigs[CameraSystemUniversal::ECameraType::Standard];
    }

    // Adapta o FOV do aimbot para o modo de câmera atual
    float GetAdaptedFOV(float baseFOV)
    {
        auto config = GetConfigForCurrentCamera();
        auto currentInfo = CameraSystemUniversal::GetCurrentCameraInfo();
        
        // Aplicar multiplicador da configuração
        float adaptedFOV = baseFOV * config.FOVMultiplier;
        
        // Considerar FOV real da câmera se disponível
        if (currentInfo.bIsValid && currentInfo.FOV > 0)
        {
            float fovRatio = currentInfo.FOV / 90.0f; // 90 é o FOV padrão
            adaptedFOV *= fovRatio;
        }
        
        return adaptedFOV;
    }

    // Adapta a sensibilidade do aimbot
    float GetAdaptedSensitivity(float baseSensitivity)
    {
        auto config = GetConfigForCurrentCamera();
        return baseSensitivity * config.SensitivityMultiplier;
    }

    // Adapta a suavização do aimbot
    float GetAdaptedSmoothing(float baseSmoothing)
    {
        auto config = GetConfigForCurrentCamera();
        return baseSmoothing * config.SmoothingMultiplier;
    }

    // Obtém a posição da câmera com offset adaptado
    SDK::FVector GetAdaptedCameraPosition()
    {
        auto config = GetConfigForCurrentCamera();
        auto basePosition = CameraSystemUniversal::GetAdaptedCameraLocation();
        
        return basePosition + config.LocationOffset;
    }

    // Verifica se a projeção de tela precisa de tratamento especial
    bool RequiresSpecialProjection()
    {
        auto config = GetConfigForCurrentCamera();
        return config.bUseAlternativeProjection;
    }

    //---------------------------------------------------------------------
    // 🎯	FUNÇÃO PRINCIPAL DE AIMBOT ADAPTADO
    //---------------------------------------------------------------------

    // Versão adaptada da função RunAimbot que funciona com todos os modos de câmera
    void RunAdaptiveAimbot(SDK::UWorld* World, 
                          SDK::APlayerController* PlayerController, 
                          SDK::APlayerCameraManager* PlayerCameraManager, 
                          SDK::AMarvelBaseCharacter* TargetPlayer, 
                          SDK::FName HeadSocketName, 
                          float baseFovCircle)
    {
        // Atualizar sistema de câmera
        CameraSystemUniversal::Update();
        
        // Verificar se a câmera é compatível com aimbot
        if (!CameraSystemUniversal::IsCameraCompatibleWithAimbot())
            return;
        
        // Obter configuração adaptada
        auto config = GetConfigForCurrentCamera();
        auto currentInfo = CameraSystemUniversal::GetCurrentCameraInfo();
        
        // Adaptar parâmetros do aimbot
        float adaptedFovCircle = GetAdaptedFOV(baseFovCircle);
        
        // Se requer tratamento especial, usar lógica customizada
        if (config.bRequiresSpecialHandling)
        {
            RunSpecializedAimbot(World, PlayerController, PlayerCameraManager, 
                               TargetPlayer, HeadSocketName, adaptedFovCircle, currentInfo.Type);
        }
        else
        {
            // Usar aimbot padrão com parâmetros adaptados
            RunStandardAimbotWithAdaptation(World, PlayerController, PlayerCameraManager, 
                                          TargetPlayer, HeadSocketName, adaptedFovCircle);
        }
    }

    //---------------------------------------------------------------------
    // 🔧	IMPLEMENTAÇÕES ESPECIALIZADAS
    //---------------------------------------------------------------------

    // Aimbot especializado para modos que requerem tratamento especial
    void RunSpecializedAimbot(SDK::UWorld* World,
                             SDK::APlayerController* PlayerController,
                             SDK::APlayerCameraManager* PlayerCameraManager,
                             SDK::AMarvelBaseCharacter* TargetPlayer,
                             SDK::FName HeadSocketName,
                             float adaptedFovCircle,
                             CameraSystemUniversal::ECameraType cameraType)
    {
        switch (cameraType)
        {
            case CameraSystemUniversal::ECameraType::Transformation:
                RunTransformationAimbot(World, PlayerController, PlayerCameraManager, 
                                      TargetPlayer, HeadSocketName, adaptedFovCircle);
                break;
                
            case CameraSystemUniversal::ECameraType::ObjectPossession:
                RunObjectPossessionAimbot(World, PlayerController, PlayerCameraManager, 
                                        TargetPlayer, HeadSocketName, adaptedFovCircle);
                break;
                
            case CameraSystemUniversal::ECameraType::Ability:
            case CameraSystemUniversal::ECameraType::Ultimate:
                RunAbilityAimbot(World, PlayerController, PlayerCameraManager, 
                               TargetPlayer, HeadSocketName, adaptedFovCircle);
                break;
                
            default:
                RunStandardAimbotWithAdaptation(World, PlayerController, PlayerCameraManager, 
                                              TargetPlayer, HeadSocketName, adaptedFovCircle);
                break;
        }
    }

    // Aimbot para transformações (ex: Hulk, Venom)
    void RunTransformationAimbot(SDK::UWorld* World, 
                                SDK::APlayerController* PlayerController, 
                                SDK::APlayerCameraManager* PlayerCameraManager, 
                                SDK::AMarvelBaseCharacter* TargetPlayer, 
                                SDK::FName HeadSocketName, 
                                float adaptedFovCircle)
    {
        // Usar posição de câmera adaptada para transformações
        auto adaptedCameraPos = GetAdaptedCameraPosition();
        
        // Calcular rotação usando posição adaptada
        SDK::FVector targetPos = TargetPlayer->GetMesh()->GetSocketLocation(HeadSocketName);
        SDK::FRotator targetRotation = SDK::UKismetMathLibrary::FindLookAtRotation(adaptedCameraPos, targetPos);
        
        // Aplicar suavização adaptada
        float adaptedSmoothing = GetAdaptedSmoothing(mods::smoothAmount);
        
        // Aplicar rotação com parâmetros adaptados
        ApplyAimbotRotation(PlayerController, targetRotation, adaptedSmoothing);
    }

    // Aimbot para incorporação de objetos
    void RunObjectPossessionAimbot(SDK::UWorld* World, 
                                  SDK::APlayerController* PlayerController, 
                                  SDK::APlayerCameraManager* PlayerCameraManager, 
                                  SDK::AMarvelBaseCharacter* TargetPlayer, 
                                  SDK::FName HeadSocketName, 
                                  float adaptedFovCircle)
    {
        // Para incorporação de objetos, pode ser necessário usar projeção alternativa
        if (RequiresSpecialProjection())
        {
            RunAlternativeProjectionAimbot(World, PlayerController, PlayerCameraManager, 
                                         TargetPlayer, HeadSocketName, adaptedFovCircle);
        }
        else
        {
            RunStandardAimbotWithAdaptation(World, PlayerController, PlayerCameraManager, 
                                          TargetPlayer, HeadSocketName, adaptedFovCircle);
        }
    }

    // Aimbot para habilidades especiais
    void RunAbilityAimbot(SDK::UWorld* World, 
                         SDK::APlayerController* PlayerController, 
                         SDK::APlayerCameraManager* PlayerCameraManager, 
                         SDK::AMarvelBaseCharacter* TargetPlayer, 
                         SDK::FName HeadSocketName, 
                         float adaptedFovCircle)
    {
        // Para habilidades, usar sensibilidade reduzida
        float adaptedSensitivity = GetAdaptedSensitivity(1.0f);
        
        RunStandardAimbotWithAdaptation(World, PlayerController, PlayerCameraManager, 
                                      TargetPlayer, HeadSocketName, adaptedFovCircle);
    }

    // Aimbot padrão com adaptações
    void RunStandardAimbotWithAdaptation(SDK::UWorld* World, 
                                        SDK::APlayerController* PlayerController, 
                                        SDK::APlayerCameraManager* PlayerCameraManager, 
                                        SDK::AMarvelBaseCharacter* TargetPlayer, 
                                        SDK::FName HeadSocketName, 
                                        float adaptedFovCircle)
    {
        // Usar a função de aimbot existente com parâmetros adaptados
        // Substituir Variables::CameraLocation por posição adaptada temporariamente
        auto originalCameraLocation = Variables::CameraLocation;
        auto originalCameraRotation = Variables::CameraRotation;
        
        Variables::CameraLocation = GetAdaptedCameraPosition();
        Variables::CameraRotation = CameraSystemUniversal::GetAdaptedCameraRotation();
        
        // Chamar aimbot original
        Modules::RunAimbot(World, PlayerController, PlayerCameraManager, TargetPlayer, HeadSocketName, adaptedFovCircle);
        
        // Restaurar valores originais
        Variables::CameraLocation = originalCameraLocation;
        Variables::CameraRotation = originalCameraRotation;
    }

    // Aimbot com projeção alternativa
    void RunAlternativeProjectionAimbot(SDK::UWorld* World, 
                                       SDK::APlayerController* PlayerController, 
                                       SDK::APlayerCameraManager* PlayerCameraManager, 
                                       SDK::AMarvelBaseCharacter* TargetPlayer, 
                                       SDK::FName HeadSocketName, 
                                       float adaptedFovCircle)
    {
        // Implementar lógica de projeção alternativa se necessário
        // Por enquanto, usar método padrão
        RunStandardAimbotWithAdaptation(World, PlayerController, PlayerCameraManager, 
                                      TargetPlayer, HeadSocketName, adaptedFovCircle);
    }

    // Aplica rotação do aimbot com parâmetros adaptados
    void ApplyAimbotRotation(SDK::APlayerController* PlayerController, 
                            const SDK::FRotator& targetRotation, 
                            float smoothing)
    {
        // Implementar aplicação de rotação suavizada
        // (usar lógica similar à do aimbot original)
        if (IsValidObjectPtr(PlayerController))
        {
            PlayerController->SetControlRotation(targetRotation);
        }
    }

    //---------------------------------------------------------------------
    // 🔧	FUNÇÕES DE CONFIGURAÇÃO
    //---------------------------------------------------------------------

    // Configura parâmetros específicos para um tipo de câmera
    void SetCameraConfig(CameraSystemUniversal::ECameraType cameraType, const FAimbotCameraConfig& config)
    {
        CameraConfigs[cameraType] = config;
    }

    // Obtém configuração atual para debug
    FAimbotCameraConfig GetCurrentConfig()
    {
        return GetConfigForCurrentCamera();
    }

    // Inicializa o sistema de integração
    void Initialize()
    {
        CameraSystemUniversal::Initialize();
        
        // Configurar adaptação automática
        CameraSystemUniversal::FCameraAdaptationConfig config;
        config.bEnableUniversalAdaptation = true;
        config.bDetectTransformations = true;
        config.bDetectObjectPossession = true;
        config.bDetectAbilityCameras = true;
        config.bDetectUltimateCameras = true;
        
        CameraSystemUniversal::SetAdaptationConfig(config);
    }
}
