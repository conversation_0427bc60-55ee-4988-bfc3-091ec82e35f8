#pragma once
// #include "../Custom/Utils.h"
#include "Globals.h"
#include <cmath> // Certifique-se de incluir a biblioteca cmath
#include "Modules.h"
#include "CameraSystemUniversal.h"
#include "CameraAimbotIntegration.h"
#include "CameraSystemDebug.h"
#include "CameraSystemConfig.h"

// Constantes para evitar strings literais
const FString HEAD_SOCKET = L"head";
const FString NECK_SOCKET = L"neck_01";

void DrawTransition(UWorld *aWorld, UEngine *Engine, ImDrawList *BackgroundList, ImDrawList *ForegroundList)
{
    // Usar o sistema de segurança para executar a função principal
    SafetySystem::SafeExecute("DrawTransitionCritical", [&]()
                              {
        // Verificação robusta de ponteiros (logs reduzidos)
        if (!IsValidObjectPtr(aWorld) || !IsValidObjectPtr(Engine))
        {
            SafetySystem::LogInfo("DrawTransitionCritical", "Ponteiros de World ou Engine inválidos no início da execução.");
            return;
        }

        UWorld *World = aWorld;
        Variables::World = World;

        //---------------------------------------------------------------------
        // 		🎯	INICIALIZAÇÃO DO SISTEMA UNIVERSAL DE CÂMERA
        //---------------------------------------------------------------------
        // Inicializar o sistema de detecção e adaptação de câmera
        static bool bCameraSystemInitialized = false;
        if (!bCameraSystemInitialized)
        {
            CameraAimbotIntegration::Initialize();
            CameraSystemConfig::InitializeAllConfigurations();
            // Ativar debug em modo de desenvolvimento (opcional)
            #ifdef _DEBUG
            CameraSystemDebug::SetDebugEnabled(true);
            CameraSystemDebug::SetVerboseLogging(false); // Mudar para true para logs detalhados
            #endif
            bCameraSystemInitialized = true;
        }

        // Atualizar sistema de debug se ativado
        CameraSystemDebug::Update();

        //---------------------------------------------------------------------
        // 		🛡️	INTERRUPTOR GLOBAL: Verificação de Game State
        //---------------------------------------------------------------------
        // Verificar o estado do jogo SEMPRE (logs reduzidos)
        static SDK::EMatchState lastMatchState = SDK::EMatchState::Pending;
        SDK::EMatchState currentMatchState = SDK::EMatchState::Pending;

        // Obter o estado atual do jogo usando métodos do SDK
        try
        {
            SDK::EMatchState outState;
            if (SDK::UMarvelBlueprintLibrary::GetMatchState(World, &outState))
            {
                currentMatchState = outState;
            }
        }
        catch (...)
        {
            // Se falhar, assumir estado não-Fighting para segurança
            SafetySystem::LogError("DrawTransitionCritical", "EXCEÇÃO em GetMatchState - assumindo Pending");
            currentMatchState = SDK::EMatchState::Pending;
        }

        // 🚨 INTERRUPTOR GLOBAL: APENAS Fighting permite execução dos cheats
        bool allowCheatExecution = (currentMatchState == SDK::EMatchState::Fighting);

        // Detectar mudança de estado e limpar dados do BulletTP se necessário
        if (currentMatchState != lastMatchState)
        {
            if (lastMatchState == SDK::EMatchState::Fighting && currentMatchState != SDK::EMatchState::Fighting)
            {
                // Saindo do estado Fighting - limpar dados do BulletTP
                SafetySystem::SafeExecute("CleanupBulletTPOnStateChange", [&]()
                                          { Modules::CleanupBulletTPOnStateChange(); });

                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("StateMonitor", "Mudança de estado detectada: Fighting -> Não-Fighting - dados limpos");
                }
            }
            lastMatchState = currentMatchState;
        }

        // ❌ SE NÃO FOR Fighting, BLOQUEAR TODA EXECUÇÃO DE CHEATS
        if (!allowCheatExecution)
        {
            return;
        }

        //---------------------------------------------------------------------
        // 		💀	VERIFICAÇÃO ADICIONAL: Local Player Alive
        //---------------------------------------------------------------------
        // Verificar se o jogador local está vivo (proteção adicional)
        bool isLocalPlayerAlive = false;
        try
        {
            // Obter o PlayerController local usando método correto do SDK
            SDK::AMarvelPlayerController* PlayerController = SDK::UMarvelBlueprintLibrary::GetLocalMarvelPlayerController(World);
            if (IsValidObjectPtr(PlayerController))
            {
                // Método 1: Verificar através do Pawn usando método estático
                SDK::APlayerState* PlayerState = SDK::UMarvelBlueprintLibrary::GetPlayerState(PlayerController);
                SDK::AMarvelPlayerState* MarvelPlayerState = static_cast<SDK::AMarvelPlayerState*>(PlayerState);
                if (IsValidObjectPtr(MarvelPlayerState))
                {
                    isLocalPlayerAlive = MarvelPlayerState->bIsAlive && !MarvelPlayerState->IsDead();
                }
                else
                {
                    isLocalPlayerAlive = false; // Segurança: se o state for inválido, assuma que não está vivo
                }

                // Método 2: Verificar através do Pawn (backup)
                if (!isLocalPlayerAlive)
                {
                    // Usar método estático para obter o character
                    SDK::AMarvelBaseCharacter* LocalCharacter = SDK::UMarvelBlueprintLibrary::GetMarvelBaseCharacter(World, 0);
                    if (IsValidObjectPtr(LocalCharacter))
                    {
                        isLocalPlayerAlive = LocalCharacter->IsAlive() && (LocalCharacter->GetCurrentHealth() > 0.0f);
                    }
                }
            }
        }
        catch (...)
        {
            // Se falhar, assumir morto para segurança
            isLocalPlayerAlive = false;
        }

        // 🚨 INTERRUPTOR ADICIONAL: APENAS se jogador estiver vivo
        if (!isLocalPlayerAlive)
        {
            // Se jogador estiver morto, não executar cheats
            return;
        }

        // ✅ APENAS AQUI (Fighting state + Player Alive) os cheats podem ser executados

        // Inicializar o ThreatValueAdmin para a funcionalidade de Ultimate Charge
        if (!Variables::ThreatValueAdmin && IsValidObjectPtr(Variables::World))
        {
            // Usar try-catch para evitar crashes ao acessar GetThreatValueAdmin
            try
            {
                Variables::ThreatValueAdmin = SDK::UMarvelAudioLibrary::GetThreatValueAdmin(Variables::World);
            }
            catch (...)
            {
                SafetySystem::LogError("DrawTransition", "Falha ao obter ThreatValueAdmin");
            }
        }

        // Obter o PlayerController com verificação de segurança
        APlayerController *PlayerController = nullptr;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(UGameplayStatics::StaticClass()))
        {
            PlayerController = UGameplayStatics::GetPlayerController(World, 0);
        }

        if (!IsValidObjectPtr(PlayerController))
            return;

        Variables::PlayerController = PlayerController;

        // Obter o PlayerCameraManager com verificação de segurança
        APlayerCameraManager *PlayerCameraManager = nullptr;
        if (IsValidObjectPtr(World) && IsValidObjectPtr(UGameplayStatics::StaticClass()))
        {
            PlayerCameraManager = UGameplayStatics::GetPlayerCameraManager(World, 0);
        }

        if (!IsValidObjectPtr(PlayerCameraManager))
            return;

        Variables::PlayerCameraManager = PlayerCameraManager;

        // Verificar o AcknowledgedPawn com segurança
        APawn *AcknowledgedPawn = nullptr;
        if (IsValidObjectPtr(PlayerController))
        {
            AcknowledgedPawn = PlayerController->AcknowledgedPawn;
        }

        if (!IsValidObjectPtr(AcknowledgedPawn))
        {
            // Se o pawn atual for inválido, limpa a variável global e interrompe
            Variables::AcknowledgedPawn = nullptr;
            return;
        }
        // Atualiza a variável global apenas se o novo pawn for diferente
        if (Variables::AcknowledgedPawn != AcknowledgedPawn)
        {
            Variables::AcknowledgedPawn = AcknowledgedPawn;
        }

        // Obter informações da câmera usando PlayerCameraManager diretamente (correção para modos alternativos)
        if (IsValidObjectPtr(PlayerCameraManager))
        {
            Variables::CameraLocation = PlayerCameraManager->GetCameraLocation();
            Variables::CameraRotation = PlayerCameraManager->GetCameraRotation();
        }
        else
        {
            // Se não houver câmera, interromper para evitar cálculos baseados em posições inválidas
            return;
        }

        // Alterar FOV se necessário, com verificação reforçada
        if (mods::fov_changer && IsValidObjectPtr(PlayerController))
        {
            PlayerController->FOV(mods::fov_changer_amount);
        }

        // Verificar se estamos realmente em jogo
        bool inGame = false;
        SafetySystem::SafeExecute("IsInGameCheck", [&]()
                                  { inGame = Modules::IsInGame(); });

        // Calcular círculos de FOV com segurança
        const float actualFovCircle = mods::actualfovcircle = (mods::fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
        if (mods::aimbotFovCircle && mods::actualfovcircle > 0 && ImGui::GetForegroundDrawList())
        {
            ImGui::GetForegroundDrawList()->AddCircle(
                ImVec2(Variables::ScreenCenter.X, Variables::ScreenCenter.Y),
                actualFovCircle,
                ImColor(255, 255, 255));
        }

        const float actualBulletTPFovCircle = mods::actual_bullet_tp_fovcircle = (mods::bullet_tp_fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
        if (mods::bullet_tp_fov_circle && mods::actual_bullet_tp_fovcircle > 0 && ImGui::GetForegroundDrawList())
        {
            ImGui::GetForegroundDrawList()->AddCircle(
                ImVec2(Variables::ScreenCenter.X, Variables::ScreenCenter.Y),
                actualBulletTPFovCircle,
                ImColor(255, 255, 0));
        }

        // Converter nomes de sockets com segurança
        FName HeadSocketName, NeckSocketName;
        if (IsValidObjectPtr(UKismetStringLibrary::StaticClass()))
        {
            HeadSocketName = UKismetStringLibrary::Conv_StringToName(HEAD_SOCKET);
            NeckSocketName = UKismetStringLibrary::Conv_StringToName(NECK_SOCKET);
        }

        // Limitador de frame rate removido para máxima fluidez

        // Usar PersistentLevel->Actors - DIRETO E SEGURO (baseado no projeto exemplo)
        if (!IsValidObjectPtr(World) || !IsValidObjectPtr(World->PersistentLevel))
            return;

        TArray<AActor*> ActorList = World->PersistentLevel->Actors;
        if (!ActorList.IsValid())
            return;

        if (ActorList.Num() <= 1)
        {
            ActorList.Clear();
            return;
        }

        // Não precisamos mais detectar novos heróis

        // ESP e Glow podem ser executados sempre que houver jogadores, independentemente do estado do jogo
        if (!SafetySystem::IsSafeMode())
        {
            // Executar ESP se estiver ativado
            if (mods::esp)
            {
                // Verificar se o BackgroundList é válido usando safe_memory_check
                if (!IsValidPtr(BackgroundList))
                {
                    SafetySystem::LogError("OverlayRenderer", "BackgroundList is invalid");
                }
                else
                {
                    // Adicionar um texto de debug para verificar se o BackgroundList está funcionando
                    BackgroundList->AddText(ImVec2(100, 100), IM_COL32(255, 0, 0, 255), "ESP DEBUG TEXT");

                    SafetySystem::SafeExecute("DrawESP", [&]()
                                              { Modules::DrawESP(ActorList, PlayerController, BackgroundList, HeadSocketName); });
                }
            }

            // Aplicar o efeito de Glow nos jogadores
            if (mods::enableGlow)
            {
                SafetySystem::SafeExecute("GlowRoutine", [&]()
                                          { Modules::GlowRoutine(PlayerController, ActorList); });
            }
        }

        // Só executar funções que dependem do estado do jogo se estiver em jogo
        if (inGame && !SafetySystem::IsSafeMode())
        {
            // Verificar a tecla de Team Target Mode com segurança
            if (IsValidObjectPtr(PlayerController))
            {
                SafetySystem::SafeExecute("CheckTeamTargetKey", [&]()
                                          { Modules::CheckTeamTargetKey(PlayerController); });
            }

            // Selecionar alvo para aimbot/bulletTP
            AMarvelBaseCharacter *TargetPlayer = nullptr;
            if (mods::aimbot || mods::bullet_tp)
            {
                SafetySystem::SafeExecute("SelectTarget", [&]()
                                          { TargetPlayer = Modules::SelectTarget(ActorList, PlayerController, HeadSocketName, actualFovCircle); });
            }

            // Executar Aimbot Adaptativo se ativado e o alvo for válido
            if (mods::aimbot && IsValidObjectPtr(TargetPlayer))
            {
                SafetySystem::SafeExecute("RunAdaptiveAimbot", [&]()
                                          { CameraAimbotIntegration::RunAdaptiveAimbot(World, PlayerController, PlayerCameraManager, TargetPlayer, HeadSocketName, actualFovCircle); });
            }

            // Executar BulletTP se ativado e o alvo for válido
            if (mods::bullet_tp && IsValidObjectPtr(TargetPlayer))
            {
                SafetySystem::SafeExecute("RunBulletTP", [&]()
                                          { Modules::RunBulletTP(World, PlayerController, PlayerCameraManager, TargetPlayer, HeadSocketName, NeckSocketName, mods::actual_bullet_tp_fovcircle); });
            }
        }

        // Limpar a lista de atores
        ActorList.Clear();

        // Atualizar o último pawn reconhecido
        static APawn *LastAcknowledgedPawn = nullptr;
        if (LastAcknowledgedPawn != AcknowledgedPawn)
        {
        LastAcknowledgedPawn = AcknowledgedPawn;
        } });
}