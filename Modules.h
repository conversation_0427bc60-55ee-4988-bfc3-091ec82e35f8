#ifndef MODULES_H
#define MODULES_H

#include "Globals.h"
#include "KeyCaptureSystem.h"

// Função removida - usando o novo sistema de suavização
#include <limits>
#include <cfloat>
#include <cstdint>
#include <windows.h>
#include <algorithm> // Para std::min
#include <vector>    // Para std::vector
#include <direct.h>  // Para _mkdir
#include <sstream>   // Para std::stringstream
#include <unordered_map> // Para std::unordered_map

// Incluir o arquivo que contém a definição do enum EHeroRole
#include "Game/SDK/SDK/Marvel_structs.hpp"

namespace Modules
{
    // Constantes para velocidade de projétil
    constexpr float DEFAULT_PROJECTILE_SPEED = 18000.0f;  // Velocidade padrão de fallback
    constexpr float MIN_VALID_PROJECTILE_SPEED = 100.0f;  // Velocidade mínima considerada válida
    constexpr float MIN_TABLE_PROJECTILE_SPEED = 10.0f;   // Velocidade mínima da tabela

    // Função auxiliar para validar velocidade
    inline bool IsValidProjectileSpeed(float speed, float minSpeed = MIN_VALID_PROJECTILE_SPEED) {
        return speed >= minSpeed;
    }

    // Declarações antecipadas de funções
    float GetProjectileSpeedFromTable(int ProjectileID, bool bGetSource = true);
    float GetProjectileSpeedFromRootComponent(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter);
    float GetProjectileSpeedFromMarvelAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter);
    float GetCurrentHeroProjectileSpeed(const char* FunctionName = "GetCurrentHeroProjectileSpeed");

    // Função para verificar se ator é um AMarvelBaseCharacter (baseado no projeto exemplo)
    bool CheckIfPlayer(SDK::AActor* Player, SDK::UClass* PlayerClass);

    // Declarações antecipadas para funções de summons/construtos
    bool CheckIfSummonBase(SDK::AActor* Actor);
    bool CheckIfConstruct(SDK::AActor* Actor);
    bool CheckIfSummonedTarget(SDK::AActor* Actor);
    SDK::FVector GetSummonTargetLocation(SDK::AActor* Actor);

    // Função para verificar se ator é um summon/construto atacável
    bool CheckIfSummonedTarget(SDK::AActor* Actor);

    // Declarações antecipadas para sistema de correção de trajetória
    void CorrectProjectileTrajectory(SDK::AMarvelAbilityTargetActor_Projectile* Projectile,
                                   SDK::FVector TargetLocation,
                                   SDK::AActor* TargetActor);
    bool ValidateTrajectoryCorrection(SDK::AMarvelAbilityTargetActor_Projectile* Projectile,
                                    const SDK::FVector& TargetLocation,
                                    float ToleranceDistance = 100.0f);

// Declarações para sistema de detecção de obstáculos e trajetória otimizada
bool CheckTrajectoryObstacles(const SDK::FVector& StartLocation,
                            const SDK::FVector& EndLocation,
                            SDK::AActor* IgnoreActor = nullptr);
SDK::FVector CalculateArcsTrajectory(const SDK::FVector& StartLocation,
                                   const SDK::FVector& TargetLocation,
                                   float ProjectileSpeed,
                                   float ArcHeight = 200.0f);
SDK::FVector FindOptimalTrajectoryPath(const SDK::FVector& StartLocation,
                                     const SDK::FVector& TargetLocation,
                                     float ProjectileSpeed,
                                     SDK::AActor* IgnoreActor = nullptr);

    // Função para testar e debugar detecção de summons/construtos
    void TestSummonConstructDetection(SDK::TArray<SDK::AActor*>& ActorList);

    // Função para limpar dados do BulletTP quando sair do estado de combate
    void CleanupBulletTPOnStateChange();

    // 🎨 Declarações das funções do ESP Moderno
    void RenderCrosshair(ImDrawList* drawList);
    void RenderSummonESP(SDK::AActor* actor, SDK::APlayerController* PlayerController, ImDrawList* drawList);
    bool ShouldRenderPlayer(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller);
    void RenderPlayerESP(SDK::AMarvelBaseCharacter* Player, SDK::APlayerController* PlayerController,
                        ImDrawList* BackgroundList, ImDrawList* ForegroundList, SDK::FName HeadSocketName);
    void RenderPlayerBox(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, ImU32 color);
    void RenderCorneredBox(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, ImU32 color);
    void RenderHealthBar(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, float healthPercent);
    void RenderPlayerSkeleton(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller,
                              ImDrawList* drawList, ImU32 color);
    void RenderPlayerInfo(SDK::AMarvelBaseCharacter* Player, SDK::APlayerController* PlayerController,
                         ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax,
                         SDK::FVector2D footPos, float distance, float health, float maxHealth);
    void RenderUltimateInfo(SDK::AMarvelBaseCharacter* Player, ImDrawList* drawList,
                           SDK::FVector2D boxMin, SDK::FVector2D boxMax, float yOffset);
    void AddTextWithOutline(ImDrawList* drawList, ImVec2 pos, ImU32 textColor, ImU32 outlineColor,
                           const char* text);

    // Função para verificar se um jogador está visível usando LineOfSight com tratamento de exceção
    bool IsPlayerVisible(APlayerController *PlayerController, AMarvelBaseCharacter *TargetPlayer, FVector TargetLocation)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(PlayerController) || !IsValidObjectPtr(TargetPlayer))
            return false;

        // Verificar se a posição da câmera é válida
        if (Variables::CameraLocation.IsZero())
            return false;

        // Usar SEH para evitar crashes durante a verificação de visibilidade
        __try
        {
            // Verificar se o jogador está visível usando LineOfSight
            // LineOfSight verifica se há uma linha de visão direta entre dois pontos
            // Retorna true se o alvo estiver visível, false caso contrário
            return PlayerController->LineOfSightTo(TargetPlayer, Variables::CameraLocation, true);
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Em caso de exceção, registrar o erro e retornar false
            SafetySystem::LogError("IsPlayerVisible", "Exceção ao verificar visibilidade");
            return false;
        }
    }

    // Função avançada para detectar invisibilidade real do personagem
    bool IsPlayerTrulyInvisible(AMarvelBaseCharacter *TargetPlayer)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(TargetPlayer))
            return true; // Se não conseguimos verificar, assumir invisível por segurança

        __try
        {
            // Método 1: Verificar se o mesh está visível
            if (IsValidObjectPtr(TargetPlayer->GetMesh()))
            {
                // Verificar se o mesh está visível
                if (!TargetPlayer->GetMesh()->IsVisible())
                {
                    return true; // Mesh não está visível = invisível
                }

                // Verificar se o mesh foi renderizado recentemente com verificação específica de visibilidade
                bool wasRenderedRecently = TargetPlayer->GetMesh()->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.1f);
                if (!wasRenderedRecently)
                {
                    return true; // Não foi renderizado recentemente = possivelmente invisível
                }
            }

            // Método 2: Verificar através do método IsRecentlyRendered do próprio personagem
            bool playerRendered = TargetPlayer->IsRecentlyRendered(true); // true = apenas verificar mesh visibility
            if (!playerRendered)
            {
                return true; // Personagem não foi renderizado = invisível
            }

            // Método 3: Verificar componente HiddenComponent se existir
            if (IsValidObjectPtr(TargetPlayer->HiddenComponent))
            {
                // Se o componente existe, pode indicar que o personagem tem capacidade de invisibilidade
                // Por enquanto, apenas verificamos se existe - implementação futura pode verificar estado
            }

            // Método 4: Verificar através de propriedades específicas do Marvel
            // Verificar se o personagem está em estado de morte (pode parecer invisível)
            if (TargetPlayer->GetCurrentHealth() <= 0.0f)
            {
                return true; // Personagem morto = não deve ser alvo
            }

            // Se chegou até aqui sem detectar invisibilidade, assumir visível
            return false;
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Em caso de exceção, assumir invisível por segurança
            SafetySystem::LogError("IsPlayerTrulyInvisible", "Exceção ao verificar invisibilidade");
            return true;
        }
    }

    // Função para verificar se está em jogo (in-game) com tratamento de exceção
    bool IsInGame()
    {
        // Usar cache para evitar chamadas repetidas
        static float lastCheckTime = 0.0f;
        static bool lastResult = false;
        static float cacheValidityTime = 1.0f; // Cache válido por 1 segundo

        // Obter o tempo atual
        float currentTime = 0.0f;

        // Verificar se podemos usar o cache
        __try
        {
            if (IsValidObjectPtr(Variables::World) && IsValidObjectPtr(SDK::UGameplayStatics::StaticClass()))
            {
                currentTime = SDK::UGameplayStatics::GetTimeSeconds(Variables::World);

                // Se o cache ainda for válido, retornar o resultado em cache
                if (currentTime - lastCheckTime < cacheValidityTime)
                    return lastResult;
            }
            else
            {
                return false;
            }
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            SafetySystem::LogError("IsInGame", "Exceção ao verificar tempo atual");
            return false;
        }

        // Verificar se temos acesso às classes necessárias
        auto gameplayStatics = (SDK::UGameplayStatics *)SDK::UGameplayStatics::StaticClass();
        if (!IsValidObjectPtr(gameplayStatics) || !IsValidObjectPtr(Variables::World))
            return false;

        // Obter o tempo de jogo em segundos com tratamento de exceção
        float gameTime = 0.0f;
        __try
        {
            gameTime = gameplayStatics->GetTimeSeconds(Variables::World);
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            SafetySystem::LogError("IsInGame", "Exceção ao obter tempo de jogo");
            return false;
        }

        // Atualizar o cache
        lastCheckTime = currentTime;
        lastResult = (gameTime > 10.0f);

        // Considerar "em jogo" se o tempo for maior que 10 segundos
        return lastResult;
    }

    //---------------------------------------------------------------------
    // 		⚔️	Verificar se jogador é inimigo usando SDK direto - CORRIGIDO
    //---------------------------------------------------------------------
    bool IsEnemy(SDK::APlayerState *EnemyPlayerState)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(EnemyPlayerState))
            return false;

        // Verificar se o jogador local é válido
        if (!IsValidObjectPtr(Variables::AcknowledgedPawn))
            return false;

        // Obter o personagem do inimigo
        SDK::AMarvelBaseCharacter *EnemyCharacter = static_cast<SDK::AMarvelBaseCharacter *>(EnemyPlayerState->GetPawn());

        // Se não conseguir obter o personagem, retornar false
        if (!IsValidObjectPtr(EnemyCharacter))
            return false;

        // VERIFICAÇÃO CRÍTICA: Verificar se é um jogador real antes de chamar IsAlly()
        // NPCs não têm PlayerState válido e causam crash ao chamar IsAlly()
        if (!IsValidObjectPtr(EnemyCharacter->PlayerState))
            return false; // NPC não é considerado inimigo

        // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
        // Agora é seguro chamar pois sabemos que é um jogador real
        bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, EnemyCharacter, true);

        // Retornar true se NÃO for aliado (ou seja, é inimigo)
        return !bIsAlly;
    }

    //---------------------------------------------------------------------
    // 		🚀	Obter velocidade usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    SDK::FVector GetVelocity(SDK::AMarvelBaseCharacter *Character)
    {
        // Verificação robusta de ponteiros
        if (!IsValidObjectPtr(Character))
            return SDK::FVector();

        // Usar o SDK diretamente para obter o componente de movimento
        SDK::UCharacterMovementComponent* MovementComponent = Character->CharacterMovement;
        if (!IsValidObjectPtr(MovementComponent))
            return SDK::FVector();

        // Obter a velocidade diretamente da propriedade pública Velocity
        SDK::FVector Velocity = MovementComponent->Velocity;

        // Verificar se a velocidade é válida (não contém NaN ou valores extremos)
        if (isnan(Velocity.X) || isnan(Velocity.Y) || isnan(Velocity.Z) ||
            isinf(Velocity.X) || isinf(Velocity.Y) || isinf(Velocity.Z) ||
            fabs(Velocity.X) > 10000.0f || fabs(Velocity.Y) > 10000.0f || fabs(Velocity.Z) > 10000.0f)
        {
            return SDK::FVector(); // Retornar vetor zero se a velocidade for inválida
        }

        return Velocity;
    }

    //---------------------------------------------------------------------
    // 		📋	Obter nome do herói usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    std::string GetActorNameFromID(uint32_t heroID)
    {
        // Verificar se o ID é válido
        if (heroID == 0)
            return "Unknown";

        // Usar o SDK diretamente para obter os dados do herói
        SDK::FMarvelHeroTable HeroData = SDK::UHeroTableFuncLibrary::GetHeroData(heroID);

        // Verificar se conseguimos obter dados válidos e se TName não está vazio
        if (HeroData.HeroID == heroID)
        {
            // Tentar usar TName primeiro
            std::string heroName = HeroData.TName.ToString();
            if (!heroName.empty())
                return heroName;

            // Se TName estiver vazio, tentar usar EnName
            heroName = HeroData.EnName.ToString();
            if (!heroName.empty())
                return heroName;
        }

        return "Unknown";
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter ID do herói usando SDK direto (sem offsets hardcoded)
    //---------------------------------------------------------------------
    uint32_t GetHeroID(SDK::APlayerState *PlayerState)
    {
        if (!IsValidObjectPtr(PlayerState))
            return 0; // ID inválido

        // Fazer cast para AMarvelPlayerState para acessar SelectedHeroID
        SDK::AMarvelPlayerState* MarvelPlayerState = static_cast<SDK::AMarvelPlayerState*>(PlayerState);
        if (!IsValidObjectPtr(MarvelPlayerState))
            return 0;

        // Usar o SDK diretamente para obter o SelectedHeroID
        return MarvelPlayerState->SelectedHeroID;
    }

    // Função para obter a role do personagem
    SDK::EHeroRole GetRole(SDK::AMarvelBaseCharacter *Player)
    {
        return Player ? Player->GetHeroRole() : SDK::EHeroRole::Unknown;
    }



    // Função para obter o nome do herói com base no ID
    const char *GetHeroName(uint32_t HeroID)
    {
        // Tentar obter o nome usando o sistema de nomes do jogo
        static std::string dynamicName;
        dynamicName = GetActorNameFromID(HeroID);
        if (dynamicName != "Unknown")
        {
            return dynamicName.c_str();
        }

        // Se não encontrar, retornar "Unknown"
        static char unknownHero[64];
        sprintf_s(unknownHero, "Unknown (ID: %u)", HeroID);
        return unknownHero;
    }











    // Função para obter informações detalhadas do herói atual
    void GetCurrentHeroInfo(char *buffer, size_t bufferSize)
    {
        if (!IsValidPtr(Variables::AcknowledgedPawn))
        {
            sprintf_s(buffer, bufferSize, "No hero selected");
            return;
        }

        APlayerState *LocalPlayerState = Variables::AcknowledgedPawn->PlayerState;
        if (!IsValidPtr(LocalPlayerState))
        {
            sprintf_s(buffer, bufferSize, "Invalid player state");
            return;
        }

        uint32_t HeroID = GetHeroID(LocalPlayerState);
        const char *HeroName = GetHeroName(HeroID);
        float ProjectileSpeed = GetCurrentHeroProjectileSpeed("GetCurrentHeroInfo");

        // Tentar obter o nome do herói usando o sistema de nomes do jogo
        std::string dynamicName = GetActorNameFromID(HeroID);

        // Se conseguiu obter o nome dinâmico
        if (dynamicName != "Unknown")
        {
            sprintf_s(buffer, bufferSize, "Hero: %s (ID: %d)\nProjectile Speed: %.1f",
                      dynamicName.c_str(), HeroID, ProjectileSpeed);
        }
        else
        {
            sprintf_s(buffer, bufferSize, "Hero: %s (ID: %d)\nProjectile Speed: %.1f (Default)",
                      HeroName, HeroID, ProjectileSpeed);
        }
    }

    //---------------------------------------------------------------------
    // 		🔍	Obter a velocidade do projétil usando o ProjectileTableFuncLibrary
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromTable(int ProjectileID, bool bGetSource)
    {
        try {
            // Obter a tabela de dados do projétil
            SDK::FMarvelProjectileAgentTable ProjectileTable = SDK::UProjectileTableFuncLibrary::GetProjectileData(ProjectileID, bGetSource);

            // Obter a velocidade do projétil
            float FlySpeed = ProjectileTable.ProjectileAgent.FlySpeed;

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(FlySpeed, MIN_TABLE_PROJECTILE_SPEED)) {
                return FlySpeed;
            }

            return 0.0f;
        }
        catch (...) {
            SafetySystem::LogError("GetProjectileSpeedFromTable", "Exceção ao obter velocidade do projétil da tabela");
            return 0.0f;
        }
    }

    //---------------------------------------------------------------------
    // 		🚀	Obter velocidade do projétil do componente raiz
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromRootComponent(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        // Verificar parâmetros de entrada
        if (!IsValidPtr(World) || !IsValidObjectPtr(LocalCharacter)) {
            return 0.0f;
        }

        // Obter todos os atores do mundo usando PersistentLevel->Actors
        if (!IsValidObjectPtr(World) || !IsValidObjectPtr(World->PersistentLevel))
            return 0.0f;

        SDK::TArray<SDK::AActor*> AllActors = World->PersistentLevel->Actors;
        if (!AllActors.IsValid() || AllActors.Num() <= 0) {
            return 0.0f;
        }

        // Filtrar apenas projéteis do tipo AGameplayAbilityTargetActor
        SDK::TArray<SDK::AActor*> Bullets;
        for (int i = 0; i < AllActors.Num(); i++) {
            if (!AllActors.IsValidIndex(i))
                continue;

            SDK::AActor* Actor = AllActors[i];
            if (!IsValidObjectPtr(Actor))
                continue;

            if (Actor->IsA(SDK::AGameplayAbilityTargetActor::StaticClass())) {
                Bullets.Add(Actor);
            }
        }

        if (Bullets.Num() <= 0) {
            return 0.0f;
        }

        // Procurar por projéteis do jogador local
        for (int i = 0; i < Bullets.Num(); i++) {
            if (!Bullets.IsValidIndex(i))
                continue;

            auto Bullet = Bullets[i];
            if (!IsValidPtr(Bullet) || Bullet->GetOwner() != LocalCharacter)
                continue;

            // Verificar se o projétil tem um componente raiz válido
            if (!IsValidPtr(Bullet->RootComponent))
                continue;

            // Obter a velocidade do projétil
            SDK::FVector BulletVelocity = Bullet->RootComponent->GetComponentVelocity();
            float speed = BulletVelocity.Magnitude();

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(speed)) {
                Bullets.Clear();
                return speed;
            }
        }

        Bullets.Clear();
        return 0.0f;
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter velocidade do projétil do MarvelAbilityTargetActor
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromMarvelAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        try {
            // Verificar se temos um mundo válido
            if (!IsValidPtr(World)) {
                return 0.0f;
            }

            // Verificar se temos um personagem local válido
            if (!IsValidObjectPtr(LocalCharacter)) {
                return 0.0f;
            }

            // Obter todos os atores do mundo usando PersistentLevel->Actors
            if (!IsValidObjectPtr(World->PersistentLevel))
                return 0.0f;

            SDK::TArray<SDK::AActor*> AllActors = World->PersistentLevel->Actors;
            if (!AllActors.IsValid() || AllActors.Num() <= 0) {
                return 0.0f;
            }

            // Filtrar apenas projéteis do tipo AMarvelAbilityTargetActor_Projectile
            SDK::TArray<SDK::AActor*> Projectiles;
            for (int i = 0; i < AllActors.Num(); i++) {
                if (!AllActors.IsValidIndex(i))
                    continue;

                SDK::AActor* Actor = AllActors[i];
                if (!IsValidObjectPtr(Actor))
                    continue;

                if (Actor->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass())) {
                    Projectiles.Add(Actor);
                }
            }

            for (int i = 0; i < Projectiles.Num(); i++) {
                SDK::AActor* Projectile = Projectiles[i];
                if (!IsValidPtr(Projectile))
                    continue;

                // Verificar se o projétil pertence ao jogador local
                SDK::AActor* Owner = nullptr;
                try {
                    Owner = Projectile->GetOwner();
                }
                catch (...) {
                    continue;
                }

                if (Owner != LocalCharacter) {
                    continue;
                }

                // Verificar explicitamente se o projétil é do tipo correto
                if (!Projectile->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass())) {
                    // Projétil não é do tipo correto
                    continue;
                }

                // Converter para o tipo correto com segurança
                SDK::AMarvelAbilityTargetActor_Projectile* ProjectileActor = nullptr;
                try {
                    ProjectileActor = reinterpret_cast<SDK::AMarvelAbilityTargetActor_Projectile*>(Projectile);
                    if (!IsValidPtr(ProjectileActor)) {
                        // Falha na verificação após o cast
                        continue;
                    }
                } catch (...) {
                    SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Exception during cast to AMarvelAbilityTargetActor_Projectile");
                    continue;
                }

                // Obter os dados do projétil
                try {
                    SDK::FMarvelProjectileAgentTable ProjectileData = ProjectileActor->K2_GetProjectileData();
                    float FlySpeed = ProjectileData.ProjectileAgent.FlySpeed;

                    if (IsValidProjectileSpeed(FlySpeed)) {
                        Projectiles.Clear();
                        return FlySpeed;
                    }
                }
                catch (...) {
                    SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Exception when getting ProjectileData");
                    // Continuar para tentar outros métodos
                }

                // Verificar se o componente de movimento é válido
                if (IsValidPtr(ProjectileActor->MovementComponent)) {
                    float Speed = ProjectileActor->MovementComponent->InitialSpeed;

                    if (Speed < 100.0f) {
                        SDK::FVector BulletVelocity = ProjectileActor->MovementComponent->Velocity;
                        Speed = BulletVelocity.Magnitude();
                    }

                    if (IsValidProjectileSpeed(Speed)) {
                        Projectiles.Clear();
                        return Speed;
                    }
                }
            }

            Projectiles.Clear();
            return 0.0f;
        }
        catch (...) {
            SafetySystem::LogError("GetProjectileSpeedFromMarvelAbilityTargetActor", "Unknown exception");
            return 0.0f;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter velocidade do projétil do GameplayAbilityTargetActor
    //---------------------------------------------------------------------
    float GetProjectileSpeedFromGameplayAbilityTargetActor(SDK::UWorld* World, SDK::AMarvelBaseCharacter* LocalCharacter)
    {
        // Verificar parâmetros de entrada
        if (!IsValidPtr(World) || !IsValidObjectPtr(LocalCharacter)) {
            return 0.0f;
        }

        // Obter todos os atores do mundo usando PersistentLevel->Actors
        if (!IsValidObjectPtr(World) || !IsValidObjectPtr(World->PersistentLevel))
            return 0.0f;

        SDK::TArray<SDK::AActor*> AllActors = World->PersistentLevel->Actors;
        if (!AllActors.IsValid() || AllActors.Num() <= 0) {
            return 0.0f;
        }

        // Filtrar apenas projéteis do tipo AGameplayAbilityTargetActor
        SDK::TArray<SDK::AActor*> Bullets;
        for (int i = 0; i < AllActors.Num(); i++) {
            if (!AllActors.IsValidIndex(i))
                continue;

            SDK::AActor* Actor = AllActors[i];
            if (!IsValidObjectPtr(Actor))
                continue;

            if (Actor->IsA(SDK::AGameplayAbilityTargetActor::StaticClass())) {
                Bullets.Add(Actor);
            }
        }

        if (Bullets.Num() <= 0) {
            return 0.0f;
        }

        // Procurar por projéteis do jogador local
        for (int i = 0; i < Bullets.Num(); i++) {
            if (!Bullets.IsValidIndex(i))
                continue;

            auto Bullet = Bullets[i];
            if (!IsValidPtr(Bullet) || Bullet->GetOwner() != LocalCharacter)
                continue;

            // Verificar se o projétil tem um componente raiz válido
            if (!IsValidPtr(Bullet->RootComponent))
                continue;

            // Obter a velocidade do projétil
            SDK::FVector BulletVelocity = Bullet->RootComponent->GetComponentVelocity();
            float speed = BulletVelocity.Magnitude();

            // Verificar se a velocidade é válida
            if (IsValidProjectileSpeed(speed)) {
                Bullets.Clear();
                return speed;
            }
        }

        Bullets.Clear();
        return 0.0f;
    }

    //---------------------------------------------------------------------
    // 		🔍	Obter velocidade do projétil do herói atual
    //---------------------------------------------------------------------
    float GetCurrentHeroProjectileSpeed(const char* FunctionName)
    {
        try {
            // Verificar se temos um personagem local válido
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Converter para AMarvelBaseCharacter
            SDK::AMarvelBaseCharacter* LocalCharacter = static_cast<SDK::AMarvelBaseCharacter*>(Variables::AcknowledgedPawn);
            if (!IsValidObjectPtr(LocalCharacter)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Verificar se temos um mundo válido
            if (!IsValidPtr(Variables::World)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Verificar se o PlayerState é válido
            if (!IsValidPtr(LocalCharacter->PlayerState)) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // Obter o ID do herói
            uint32_t HeroID = GetHeroID(LocalCharacter->PlayerState);
            if (HeroID == 0) {
                return DEFAULT_PROJECTILE_SPEED;
            }

            // 1. Tentar obter da tabela de dados do projétil
            float speed = GetProjectileSpeedFromTable(HeroID);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 2. Tentar obter de projéteis específicos do Marvel
            speed = GetProjectileSpeedFromMarvelAbilityTargetActor(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 3. Velocidade do componente raiz de qualquer projétil
            speed = GetProjectileSpeedFromRootComponent(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // 4. Projéteis do tipo GameplayAbilityTargetActor
            speed = GetProjectileSpeedFromGameplayAbilityTargetActor(Variables::World, LocalCharacter);
            if (IsValidProjectileSpeed(speed)) {
                return speed;
            }

            // Se nenhum método funcionar, retornar o valor padrão
            return DEFAULT_PROJECTILE_SPEED;
        }
        catch (const std::exception&) {
            SafetySystem::LogError(FunctionName, "Exception during projectile speed detection");
            return DEFAULT_PROJECTILE_SPEED;
        }
        catch (...) {
            SafetySystem::LogError(FunctionName, "Unknown exception");
            return DEFAULT_PROJECTILE_SPEED;
        }
    }

    // Função para obter a velocidade do projétil do jogador local
    float GetLocalPlayerProjectileSpeed()
    {
        if (!IsValidPtr(Variables::AcknowledgedPawn))
            return DEFAULT_PROJECTILE_SPEED;

        return GetCurrentHeroProjectileSpeed();
    }

    //---------------------------------------------------------------------
    // 		🛡️	Verificar se ator é jogador real (não NPC) - SOLUÇÃO PARA CRASH
    //---------------------------------------------------------------------
    bool IsValidPlayerCharacterOnly(SDK::AActor* actor)
    {
        if (!IsValidObjectPtr(actor))
            return false;

        // Cast para AMarvelBaseCharacter
        auto Player = static_cast<SDK::AMarvelBaseCharacter*>(actor);
        if (!IsValidObjectPtr(Player))
            return false;

        // VERIFICAÇÃO CRÍTICA: NPCs não têm PlayerState válido
        // Esta é a diferença fundamental entre jogadores reais e NPCs
        if (!IsValidObjectPtr(Player->PlayerState))
            return false; // É um NPC usando modelo de personagem jogável

        // Verificação adicional: PlayerState deve ser do tipo correto
        auto MarvelPlayerState = static_cast<SDK::AMarvelPlayerState*>(Player->PlayerState);
        if (!IsValidObjectPtr(MarvelPlayerState))
            return false;

        // Verificação final: Deve ter um PlayerController válido
        if (!IsValidObjectPtr(Player->PlayerState->GetOwner()))
            return false;

        return true; // É um jogador real, não um NPC
    }

    //---------------------------------------------------------------------
    // 		🎨	Sistema ESP Moderno - Função Principal
    //---------------------------------------------------------------------
    void DrawESP(TArray<AActor *> &ActorList, APlayerController *PlayerController, ImDrawList *BackgroundList, FName HeadSocketName)
    {
        // Verificar se o BackgroundList é válido
        if (!IsValidPtr(BackgroundList))
        {
            SafetySystem::LogError("DrawESP", "BackgroundList is invalid");
            return;
        }

        // Verificar se o PlayerController é válido
        if (!IsValidPtr(PlayerController))
        {
            SafetySystem::LogError("DrawESP", "PlayerController is null");
            return;
        }

        // Verificar se a lista de atores é válida
        if (ActorList.Num() <= 0)
        {
            SafetySystem::LogError("DrawESP", "ActorList is empty");
            return;
        }

        // Obter a classe para filtrar apenas AMarvelBaseCharacter
        SDK::UClass* ClassToFind = SDK::AMarvelBaseCharacter::StaticClass();
        if (!IsValidObjectPtr(ClassToFind))
            return;

        // Obter ForegroundList para elementos que devem ficar na frente
        ImDrawList* ForegroundList = ImGui::GetForegroundDrawList();

        // Renderizar crosshair se ativado
        if (mods::showCrosshair && IsValidPtr(ForegroundList))
        {
            RenderCrosshair(ForegroundList);
        }

        // Loop através de todos os atores usando SafeArrayLoop
        SafeArrayLoop("DrawESP_ModernPlayers", ActorList, [&](SDK::AActor* actor, int i) -> bool {
            // Verificar se é um jogador ou summon
            bool isPlayer = CheckIfPlayer(actor, ClassToFind);
            bool isSummon = CheckIfSummonedTarget(actor);

            if (!isPlayer && !isSummon)
                return true; // Continuar loop

            // Se for summon, renderizar ESP básico (se ativado)
            if (isSummon && !isPlayer && mods::showSummonESP)
            {
                RenderSummonESP(actor, PlayerController, BackgroundList);
                return true;
            }

            // Cast para AMarvelBaseCharacter
            SDK::AMarvelBaseCharacter* Player = static_cast<SDK::AMarvelBaseCharacter*>(actor);
            if (!IsValidObjectPtr(Player))
                return true;

            // Verificações básicas
            if (!ShouldRenderPlayer(Player, PlayerController))
                return true;

            // Renderizar ESP do jogador
            RenderPlayerESP(Player, PlayerController, BackgroundList, ForegroundList, HeadSocketName);

            return true; // Continuar processando próximo elemento
        });
    }

    //---------------------------------------------------------------------
    // 		🎯	Renderizar Crosshair Moderno
    //---------------------------------------------------------------------
    void RenderCrosshair(ImDrawList* drawList)
    {
        if (!IsValidPtr(drawList)) return;

        ImVec2 displaySize = ImGui::GetIO().DisplaySize;
        ImVec2 center = ImVec2(displaySize.x / 2, displaySize.y / 2);

        switch (mods::crosshairType)
        {
            case 0: // Dot
                drawList->AddCircleFilled(center, mods::crosshairSize, mods::crosshairColor);
                break;
            case 1: // Cross
            {
                float halfSize = mods::crosshairSize / 2;
                drawList->AddLine(ImVec2(center.x - halfSize, center.y),
                                ImVec2(center.x + halfSize, center.y),
                                mods::crosshairColor, mods::crosshairThickness);
                drawList->AddLine(ImVec2(center.x, center.y - halfSize),
                                ImVec2(center.x, center.y + halfSize),
                                mods::crosshairColor, mods::crosshairThickness);
                break;
            }
            case 2: // Circle
                drawList->AddCircle(center, mods::crosshairSize, mods::crosshairColor,
                                  0, mods::crosshairThickness);
                break;
        }
    }

    //---------------------------------------------------------------------
    // 		🔮	Renderizar ESP de Summons/Construtos
    //---------------------------------------------------------------------
    void RenderSummonESP(SDK::AActor* actor, SDK::APlayerController* PlayerController, ImDrawList* drawList)
    {
        if (!IsValidObjectPtr(actor) || !IsValidPtr(PlayerController) || !IsValidPtr(drawList))
            return;

        SDK::FVector WorldLocation = actor->K2_GetActorLocation();
        SDK::FVector2D ScreenLocation;
        if (PlayerController->ProjectWorldLocationToScreen(WorldLocation, &ScreenLocation, true))
        {
            // Determinar o tipo específico para o texto
            const char* targetType = "SUMMON";
            ImU32 color = IM_COL32(255, 165, 0, 255); // Laranja padrão

            // Verificar se é um clone/phantom especificamente
            if (SDK::UActionLogFunctionLibrary::IsPhantom(actor) ||
                actor->IsA(SDK::AMarvelPhantomActor::StaticClass()))
            {
                targetType = "CLONE";
                color = IM_COL32(255, 100, 255, 255); // Magenta para clones
            }
            else if (actor->IsA(SDK::AMarvelSummonerBase::StaticClass()))
            {
                targetType = "SUMMON";
                color = IM_COL32(255, 165, 0, 255); // Laranja para summons
            }
            else if (CheckIfConstruct(actor))
            {
                targetType = "CONSTRUCT";
                color = IM_COL32(0, 255, 255, 255); // Ciano para construtos
            }

            // Renderizar indicador visual
            drawList->AddCircle(ImVec2(ScreenLocation.X, ScreenLocation.Y), 8.0f, color, 12, 2.0f);

            // Adicionar texto identificando o tipo
            AddTextWithOutline(drawList, ImVec2(ScreenLocation.X + 15, ScreenLocation.Y - 10),
                             color, IM_COL32(0, 0, 0, 255), targetType);
        }
    }

    //---------------------------------------------------------------------
    // 		✅	Verificar se Deve Renderizar Jogador
    //---------------------------------------------------------------------
    bool ShouldRenderPlayer(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller)
    {
        if (!IsValidObjectPtr(player) || !IsValidPtr(controller))
            return false;

        // Verificar se tem saúde
        if (player->GetCurrentHealth() <= 0.0f)
            return false;

        // Verificar se é jogador local
        if (mods::LocalCheck && player->IsLocallyControlled())
            return false;

        // Verificar distância
        float distance = player->GetDistanceTo(controller) / 100.0f;
        if (distance > mods::maxESPDistance)
            return false;

        // Verificar team check
        if (mods::TeamCheck)
        {
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || !IsValidObjectPtr(player->PlayerState))
                return false;

            bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, player, true);
            if (bIsAlly)
                return false;
        }

        return true;
    }

    //---------------------------------------------------------------------
    // 		🎨	Renderizar ESP Completo do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerESP(SDK::AMarvelBaseCharacter* Player, SDK::APlayerController* PlayerController,
                        ImDrawList* BackgroundList, ImDrawList* ForegroundList, SDK::FName HeadSocketName)
    {
        if (!IsValidObjectPtr(Player) || !IsValidPtr(PlayerController) || !IsValidPtr(BackgroundList))
            return;

        SDK::USkeletalMeshComponent* Mesh = Player->GetMesh();
        if (!IsValidPtr(Mesh) || !Mesh->SkeletalMesh)
            return;

        // Obter posições 3D
        SDK::FVector TargetHead3D = Mesh->GetSocketLocation(HeadSocketName);
        SDK::FVector TargetFoot3D = Mesh->GetSocketLocation(SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(L"Root")));

        if (TargetHead3D.IsZero())
            return;

        // Projetar para tela
        SDK::FVector2D TargetHead2D, TargetFoot2D;
        if (!PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true) ||
            !PlayerController->ProjectWorldLocationToScreen(TargetFoot3D, &TargetFoot2D, true))
            return;

        // Calcular informações do jogador
        float health = Player->GetCurrentHealth();
        float maxHealth = Player->GetMaxHealth();
        float healthPercent = (maxHealth > 0.0f) ? (health / maxHealth) : 0.0f;
        float distance = Player->GetDistanceTo(PlayerController) / 100.0f;

        // Verificar visibilidade
        bool isVisible = Mesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.0f);
        if (mods::UseLineOfSight)
        {
            isVisible = IsPlayerVisible(PlayerController, Player, TargetHead3D);
        }

        // Determinar cor baseada na visibilidade
        ImU32 playerColor = isVisible ? IM_COL32(0, 255, 0, 255) : IM_COL32(255, 0, 0, 255);

        // Calcular bounding box
        SDK::FVector2D boxMin;
        boxMin.X = (TargetHead2D.X < TargetFoot2D.X ? TargetHead2D.X : TargetFoot2D.X) - 50.0f;
        boxMin.Y = (TargetHead2D.Y < TargetFoot2D.Y ? TargetHead2D.Y : TargetFoot2D.Y) - 10.0f;

        SDK::FVector2D boxMax;
        boxMax.X = (TargetHead2D.X > TargetFoot2D.X ? TargetHead2D.X : TargetFoot2D.X) + 50.0f;
        boxMax.Y = (TargetHead2D.Y > TargetFoot2D.Y ? TargetHead2D.Y : TargetFoot2D.Y) + 10.0f;

        // Renderizar box se ativado
        if (mods::showBoxes)
        {
            RenderPlayerBox(BackgroundList, boxMin, boxMax, playerColor);
        }

        // Renderizar skeleton se ativado
        if (mods::showSkeleton)
        {
            RenderPlayerSkeleton(Player, PlayerController, BackgroundList, playerColor);
        }

        // Renderizar barra de saúde se ativada
        if (mods::showHealthBar && maxHealth > 0.0f)
        {
            RenderHealthBar(BackgroundList, boxMin, boxMax, healthPercent);
        }

        // Renderizar informações de texto
        RenderPlayerInfo(Player, PlayerController, BackgroundList, boxMin, boxMax,
                        TargetFoot2D, distance, health, maxHealth);

        // Renderizar tracers se ativados
        if (mods::TracerLines)
        {
            BackgroundList->AddLine(ImVec2(Variables::ScreenCenter.X, 120),
                                  ImVec2(TargetHead2D.X, TargetHead2D.Y), playerColor, 2.0f);
        }

        // Renderizar snap line para aimbot se ativado
        if (mods::showSnapLines && Variables::LastTarget == Player && IsValidPtr(ForegroundList))
        {
            ImVec2 displaySize = ImGui::GetIO().DisplaySize;
            ImVec2 screenCenter = ImVec2(displaySize.x / 2, displaySize.y / 2);
            ForegroundList->AddLine(screenCenter, ImVec2(TargetHead2D.X, TargetHead2D.Y),
                                  IM_COL32(255, 0, 0, 255), 2.0f);
        }
    }

    //---------------------------------------------------------------------
    // 		📦	Renderizar Box do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerBox(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, ImU32 color)
    {
        if (!IsValidPtr(drawList)) return;

        switch (mods::boxType)
        {
            case 0: // 2D Box
                if (mods::boxOutline)
                {
                    drawList->AddRect(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMax.X, boxMax.Y),
                                    IM_COL32(0, 0, 0, 255), 0.0f, ImDrawFlags_None,
                                    mods::boxThickness + 2.0f);
                }
                drawList->AddRect(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMax.X, boxMax.Y),
                                color, 0.0f, ImDrawFlags_None, mods::boxThickness);
                break;
            case 1: // Cornered Box
                RenderCorneredBox(drawList, boxMin, boxMax, color);
                break;
            case 2: // Filled Box
                drawList->AddRectFilled(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMax.X, boxMax.Y),
                                      IM_COL32(color & 0xFF, (color >> 8) & 0xFF,
                                              (color >> 16) & 0xFF, 50)); // Semi-transparente
                drawList->AddRect(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMax.X, boxMax.Y),
                                color, 0.0f, ImDrawFlags_None, mods::boxThickness);
                break;
        }
    }

    //---------------------------------------------------------------------
    // 		📐	Renderizar Box com Cantos
    //---------------------------------------------------------------------
    void RenderCorneredBox(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, ImU32 color)
    {
        if (!IsValidPtr(drawList)) return;

        float cornerSize = 15.0f;
        float thickness = mods::boxThickness;

        // Cantos superiores
        drawList->AddLine(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMin.X + cornerSize, boxMin.Y), color, thickness);
        drawList->AddLine(ImVec2(boxMin.X, boxMin.Y), ImVec2(boxMin.X, boxMin.Y + cornerSize), color, thickness);

        drawList->AddLine(ImVec2(boxMax.X, boxMin.Y), ImVec2(boxMax.X - cornerSize, boxMin.Y), color, thickness);
        drawList->AddLine(ImVec2(boxMax.X, boxMin.Y), ImVec2(boxMax.X, boxMin.Y + cornerSize), color, thickness);

        // Cantos inferiores
        drawList->AddLine(ImVec2(boxMin.X, boxMax.Y), ImVec2(boxMin.X + cornerSize, boxMax.Y), color, thickness);
        drawList->AddLine(ImVec2(boxMin.X, boxMax.Y), ImVec2(boxMin.X, boxMax.Y - cornerSize), color, thickness);

        drawList->AddLine(ImVec2(boxMax.X, boxMax.Y), ImVec2(boxMax.X - cornerSize, boxMax.Y), color, thickness);
        drawList->AddLine(ImVec2(boxMax.X, boxMax.Y), ImVec2(boxMax.X, boxMax.Y - cornerSize), color, thickness);
    }

    //---------------------------------------------------------------------
    // 		💚	Renderizar Barra de Saúde Moderna
    //---------------------------------------------------------------------
    void RenderHealthBar(ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax, float healthPercent)
    {
        if (!IsValidPtr(drawList)) return;

        // Posição da barra baseada na configuração
        ImVec2 barStart, barEnd;
        switch (mods::healthBarPosition)
        {
            case 0: // Esquerda
                barStart = ImVec2(boxMin.X - 10.0f, boxMin.Y);
                barEnd = ImVec2(boxMin.X - 5.0f, boxMax.Y);
                break;
            case 1: // Direita
                barStart = ImVec2(boxMax.X + 5.0f, boxMin.Y);
                barEnd = ImVec2(boxMax.X + 10.0f, boxMax.Y);
                break;
            case 2: // Topo
                barStart = ImVec2(boxMin.X, boxMin.Y - 10.0f);
                barEnd = ImVec2(boxMax.X, boxMin.Y - 5.0f);
                break;
            case 3: // Baixo
                barStart = ImVec2(boxMin.X, boxMax.Y + 5.0f);
                barEnd = ImVec2(boxMax.X, boxMax.Y + 10.0f);
                break;
        }

        // Determinar cor da saúde
        ImU32 healthColor;
        if (healthPercent >= 0.7f)
            healthColor = IM_COL32(0, 255, 0, 250);
        else if (healthPercent >= 0.3f)
            healthColor = IM_COL32(255, 255, 0, 250);
        else
            healthColor = IM_COL32(255, 0, 0, 250);

        // Renderizar barra
        if (mods::healthBarPosition <= 1) // Vertical
        {
            float barHeight = barEnd.y - barStart.y;
            ImVec2 fillStart(barStart.x, barEnd.y - (healthPercent * barHeight));

            drawList->AddRectFilled(barStart, barEnd, IM_COL32(0, 0, 0, 200)); // Background
            drawList->AddRectFilled(fillStart, barEnd, healthColor); // Health
            drawList->AddRect(barStart, barEnd, IM_COL32(0, 0, 0, 255)); // Outline
        }
        else // Horizontal
        {
            float barWidth = barEnd.x - barStart.x;
            ImVec2 fillEnd(barStart.x + (healthPercent * barWidth), barEnd.y);

            drawList->AddRectFilled(barStart, barEnd, IM_COL32(0, 0, 0, 200)); // Background
            drawList->AddRectFilled(barStart, fillEnd, healthColor); // Health
            drawList->AddRect(barStart, barEnd, IM_COL32(0, 0, 0, 255)); // Outline
        }
    }

    //---------------------------------------------------------------------
    // 		🦴	Renderizar Skeleton do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerSkeleton(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller,
                              ImDrawList* drawList, ImU32 color)
    {
        if (!IsValidObjectPtr(player) || !IsValidPtr(controller) || !IsValidPtr(drawList))
            return;

        SDK::USkeletalMeshComponent* mesh = player->GetMesh();
        if (!IsValidObjectPtr(mesh))
            return;

        // Conexões de ossos para skeleton
        std::vector<std::pair<std::wstring, std::wstring>> boneConnections = {
            { L"spine_01", L"pelvis" },
            { L"neck_01", L"spine_01" },
            { L"head", L"neck_01" },
            { L"upperarm_l", L"spine_01" },
            { L"lowerarm_l", L"upperarm_l" },
            { L"hand_l", L"lowerarm_l" },
            { L"upperarm_r", L"spine_01" },
            { L"lowerarm_r", L"upperarm_r" },
            { L"hand_r", L"lowerarm_r" },
            { L"thigh_l", L"pelvis" },
            { L"calf_l", L"thigh_l" },
            { L"foot_l", L"calf_l" },
            { L"thigh_r", L"pelvis" },
            { L"calf_r", L"thigh_r" },
            { L"foot_r", L"calf_r" }
        };

        for (const auto& [boneNameStr, parentBoneNameStr] : boneConnections)
        {
            SDK::FName boneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(boneNameStr.c_str()));
            if (!mesh->DoesSocketExist(boneName)) continue;

            SDK::FVector boneWorldPos = mesh->GetSocketLocation(boneName);
            SDK::FVector2D boneScreenPos;
            if (!controller->ProjectWorldLocationToScreen(boneWorldPos, &boneScreenPos, true)) continue;

            if (!parentBoneNameStr.empty())
            {
                SDK::FName parentBoneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(parentBoneNameStr.c_str()));
                if (mesh->DoesSocketExist(parentBoneName))
                {
                    SDK::FVector parentBoneWorldPos = mesh->GetSocketLocation(parentBoneName);
                    SDK::FVector2D parentBoneScreenPos;

                    if (controller->ProjectWorldLocationToScreen(parentBoneWorldPos, &parentBoneScreenPos, true))
                    {
                        drawList->AddLine(
                            ImVec2(boneScreenPos.X, boneScreenPos.Y),
                            ImVec2(parentBoneScreenPos.X, parentBoneScreenPos.Y),
                            color, mods::skeletonThickness);
                    }
                }
            }
        }
    }

    //---------------------------------------------------------------------
    // 		📝	Renderizar Informações do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerInfo(SDK::AMarvelBaseCharacter* Player, SDK::APlayerController* PlayerController,
                         ImDrawList* drawList, SDK::FVector2D boxMin, SDK::FVector2D boxMax,
                         SDK::FVector2D footPos, float distance, float health, float maxHealth)
    {
        if (!IsValidObjectPtr(Player) || !IsValidPtr(drawList))
            return;

        float yOffset = 0.0f;

        // Renderizar nome do jogador se ativado
        if (mods::showPlayerNames)
        {
            try
            {
                std::string playerName = Player->GetPlayerName(true).ToString();
                if (!playerName.empty())
                {
                    ImVec2 textPos = ImVec2((boxMin.X + boxMax.X) / 2.0f - 30, boxMin.Y - 20 - yOffset);
                    AddTextWithOutline(drawList, textPos, IM_COL32(255, 255, 255, 255),
                                     IM_COL32(0, 0, 0, 255), playerName.c_str());
                    yOffset += 15.0f;
                }
            }
            catch (...) {}
        }

        // Renderizar distância se ativado
        if (mods::ShowDistance)
        {
            std::string distanceStr = std::to_string(static_cast<int>(distance)) + "m";
            ImVec2 textPos = ImVec2((boxMin.X + boxMax.X) / 2.0f - 15, boxMax.Y + 5 + yOffset);
            AddTextWithOutline(drawList, textPos, mods::DistanceColor, IM_COL32(0, 0, 0, 255),
                             distanceStr.c_str());
            yOffset += 15.0f;
        }

        // Renderizar saúde se ativado
        if (mods::ShowHealth)
        {
            std::string healthStr = std::to_string(static_cast<int>(health)) + " / " +
                                  std::to_string(static_cast<int>(maxHealth));
            ImVec2 textPos = ImVec2((boxMin.X + boxMax.X) / 2.0f - 25, boxMax.Y + 5 + yOffset);
            AddTextWithOutline(drawList, textPos, mods::HealthColor, IM_COL32(0, 0, 0, 255),
                             healthStr.c_str());
            yOffset += 15.0f;
        }

        // Renderizar porcentagem de ultimate se ativado
        if (mods::showUltimatePercentage)
        {
            RenderUltimateInfo(Player, drawList, boxMin, boxMax, yOffset);
        }
    }

    //---------------------------------------------------------------------
    // 		⚡	Renderizar Informações de Ultimate
    //---------------------------------------------------------------------
    void RenderUltimateInfo(SDK::AMarvelBaseCharacter* Player, ImDrawList* drawList,
                           SDK::FVector2D boxMin, SDK::FVector2D boxMax, float yOffset)
    {
        if (!IsValidObjectPtr(Player) || !IsValidPtr(drawList))
            return;

        // Obter o ThreatValueAdmin para acessar as informações de ultimate
        if (!Variables::ThreatValueAdmin && Variables::World)
        {
            Variables::ThreatValueAdmin = SDK::UMarvelAudioLibrary::GetThreatValueAdmin(Variables::World);
        }

        if (Variables::ThreatValueAdmin)
        {
            auto ThreatInfoArray = Variables::ThreatValueAdmin->GetPlayerThreatInfo();

            for (int j = 0; j < ThreatInfoArray.Num(); j++)
            {
                if (ThreatInfoArray[j].Character == Player)
                {
                    char ultimateStr[64];
                    sprintf_s(ultimateStr, "Ult: %.0f%%", ThreatInfoArray[j].UltimatePercentage);

                    ImVec2 textPos = ImVec2((boxMin.X + boxMax.X) / 2.0f - 20, boxMax.Y + 5 + yOffset);
                    AddTextWithOutline(drawList, textPos, IM_COL32(0, 255, 255, 255),
                                     IM_COL32(0, 0, 0, 255), ultimateStr);
                    break;
                }
            }
        }
    }

    //---------------------------------------------------------------------
    // 		✏️	Adicionar Texto com Contorno
    //---------------------------------------------------------------------
    void AddTextWithOutline(ImDrawList* drawList, ImVec2 pos, ImU32 textColor, ImU32 outlineColor,
                           const char* text)
    {
        if (!IsValidPtr(drawList) || !text) return;

        // Renderizar contorno
        drawList->AddText(ImVec2(pos.x - 1, pos.y), outlineColor, text);
        drawList->AddText(ImVec2(pos.x + 1, pos.y), outlineColor, text);
        drawList->AddText(ImVec2(pos.x, pos.y - 1), outlineColor, text);
        drawList->AddText(ImVec2(pos.x, pos.y + 1), outlineColor, text);

        // Renderizar texto principal
        drawList->AddText(pos, textColor, text);
    }

    //---------------------------------------------------------------------
    // 		🎮	Verificar se a tecla de aimbot está pressionada
    //---------------------------------------------------------------------
    bool IsAimbotKeyPressed(SDK::APlayerController* PlayerController)
    {
        if (!IsValidObjectPtr(PlayerController))
            return false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentAimbotKey);

        // Verificar gamepad
        SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
        bool isGamepadPressed = false;
        if (IsValidPtr(aimbotGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*aimbotGamepadButton);
        }

        return isKeyboardMousePressed || isGamepadPressed;
    }

    //---------------------------------------------------------------------
    // 		🔄	Detectar nova ativação do FirstEnemyLock para seleção limpa
    //---------------------------------------------------------------------
    bool IsFirstEnemyLockNewActivation(SDK::APlayerController* PlayerController)
    {
        static bool wasAimbotKeyPressedLastFrame = false;
        bool isCurrentlyPressed = IsAimbotKeyPressed(PlayerController);

        // Detectar transição de não-pressionado para pressionado (nova ativação)
        bool isNewActivation = isCurrentlyPressed && !wasAimbotKeyPressedLastFrame;

        // Atualizar estado para próximo frame
        wasAimbotKeyPressedLastFrame = isCurrentlyPressed;

        return isNewActivation;
    }

    //---------------------------------------------------------------------
    // 		🔍	Verificar se o primeiro alvo travado ainda é válido
    //---------------------------------------------------------------------
    bool IsFirstLockedTargetStillValid(SDK::AMarvelBaseCharacter *LockedTarget, SDK::APlayerController *PlayerController, SDK::FName HeadSocketName, float actualFovCircle)
    {
        if (!IsValidObjectPtr(LockedTarget))
            return false;

        // Verificar se está vivo
        if (LockedTarget->GetCurrentHealth() <= 0.0f)
            return false;

        // Verificar se é um jogador real (tem PlayerState)
        if (!IsValidObjectPtr(LockedTarget->PlayerState))
            return false;

        // Verificar Team Check se ativado
        if (mods::TeamCheck && IsValidObjectPtr(Variables::AcknowledgedPawn))
        {
            bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, LockedTarget, true);
            if ((mods::teamTargetMode && !bIsAlly) || (!mods::teamTargetMode && bIsAlly))
                return false;
        }

        USkeletalMeshComponent *LockedMesh = LockedTarget->GetMesh();
        if (!IsValidPtr(LockedMesh) || !LockedMesh->SkeletalMesh)
            return false;

        SDK::FVector LockedTargetHead = LockedMesh->GetSocketLocation(HeadSocketName);
        if (LockedTargetHead.IsZero())
            return false;

        // Verificar visibilidade se ativado
        if (mods::VisCheck)
        {
            bool isVisibleOld = LockedMesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
            bool isVisibleNew = IsPlayerVisible(PlayerController, LockedTarget, LockedTargetHead);
            bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

            if (!isVisible)
                return false;
        }

        // Verificar se deve mirar em alvos invisíveis
        if (!mods::targetInvisibleEnemies)
        {
            // Verificação tradicional de visibilidade (linha de visão/renderização)
            bool isVisibleOld = LockedMesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
            bool isVisibleNew = IsPlayerVisible(PlayerController, LockedTarget, LockedTargetHead);
            bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

            // NOVA VERIFICAÇÃO: Detectar invisibilidade real do personagem
            bool isTrulyInvisible = IsPlayerTrulyInvisible(LockedTarget);

            // Rejeitar alvo se não estiver visível OU se estiver realmente invisível
            if (!isVisible || isTrulyInvisible)
                return false;
        }

        // Verificar FOV se não estivermos em modo Support Vision
        if (!mods::isSupportVisionEnabled)
        {
            SDK::FVector2D LockedScreenPos;
            if (!PlayerController->ProjectWorldLocationToScreen(LockedTargetHead, &LockedScreenPos, true))
                return false;

            // Calcular distância do centro da tela
            SDK::FVector2D ScreenCenter = SDK::FVector2D(ImGui::GetIO().DisplaySize.x / 2.0f, ImGui::GetIO().DisplaySize.y / 2.0f);
            double LockedDistanceToCenter = sqrt(pow(LockedScreenPos.X - ScreenCenter.X, 2) + pow(LockedScreenPos.Y - ScreenCenter.Y, 2));

            if (LockedDistanceToCenter > actualFovCircle)
                return false;
        }

        return true;
    }

    //---------------------------------------------------------------------
    // 		🎯	Selecionar alvo usando SDK direto
    //---------------------------------------------------------------------
    SDK::AMarvelBaseCharacter *SelectTarget(SDK::TArray<SDK::AActor *> &ActorList, SDK::APlayerController *PlayerController, SDK::FName HeadSocketName, float actualFovCircle)
    {
        // Verificações de segurança básicas
            if (!IsValidObjectPtr(PlayerController))
            {
                SafetySystem::LogError("SelectTarget", "PlayerController inválido");
                return nullptr;
            }

            if (!ActorList.IsValid() || ActorList.Num() <= 0)
            {
                SafetySystem::LogError("SelectTarget", "ActorList inválido ou vazio");
                return nullptr;
            }

            // 🔄 CORREÇÃO: Detectar nova ativação para seleção limpa
            if (mods::firstEnemyLock && IsFirstEnemyLockNewActivation(PlayerController))
            {
                // Nova ativação detectada - resetar alvo para seleção limpa
                Variables::FirstLockedTarget = nullptr;

                static int activationCounter = 0;
                activationCounter++;
                std::string logMessage = "Nova ativação #" + std::to_string(activationCounter) + " detectada - seleção limpa iniciada!";
                SafetySystem::LogInfo("FirstEnemyLock", logMessage.c_str());
            }

            // 🎮 CORREÇÃO: First Enemy Lock só funciona quando a tecla de aimbot está pressionada
            if (mods::firstEnemyLock && IsValidObjectPtr(Variables::FirstLockedTarget) && IsAimbotKeyPressed(PlayerController))
            {
                // Aplicar todas as verificações ao alvo travado
                if (IsFirstLockedTargetStillValid(Variables::FirstLockedTarget, PlayerController, HeadSocketName, actualFovCircle))
                {
                    // DEBUG: Log quando retorna alvo travado
                    static int lockReturnCounter = 0;
                    lockReturnCounter++;
                    if (lockReturnCounter % 60 == 0) { // Log a cada 60 frames (~1 segundo)
                        SafetySystem::LogInfo("FirstEnemyLock", "✅ Mantendo alvo travado - tecla pressionada");
                    }
                    return Variables::FirstLockedTarget;
                }
                else
                {
                    // Se o alvo travado não é mais válido, resetar
                    Variables::FirstLockedTarget = nullptr;
                    SafetySystem::LogInfo("FirstEnemyLock", "❌ Alvo travado não é mais válido - resetando");
                }
            }
            // CORREÇÃO: Não resetar FirstLockedTarget aqui - será resetado apenas na RunAimbot
            // para evitar condição de corrida entre SelectTarget e RunAimbot

        double ClosestDistanceToCenter = DBL_MAX;
        float LowestHealth = FLT_MAX;
        SDK::AMarvelBaseCharacter *SelectedTarget = nullptr;

        // Estruturas para armazenar alvos de fallback por role
        struct TargetInfo
        {
            SDK::AMarvelBaseCharacter *Player;
            double DistanceToCenter;
            float Health;
            SDK::EHeroRole Role;
        };

        TargetInfo SupportTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        TargetInfo DamageTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        TargetInfo TankTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};

        // Estrutura para armazenar o melhor alvo voador
        TargetInfo FlyingTarget = {nullptr, DBL_MAX, FLT_MAX, SDK::EHeroRole::Unknown};
        bool foundFlyingTarget = false;

        // Flag para indicar se encontramos um alvo da role selecionada
        bool foundTargetWithSelectedRole = false;

        // TEMPORARIAMENTE DESABILITADO: 🤖 PRIORIDADE MÁXIMA: Priorizar summons se a opção estiver ativada
        if (false && mods::shouldPrioritizeSummons)
        {
            SDK::AMarvelBaseCharacter* BestSummon = nullptr;
            double ClosestSummonDistance = DBL_MAX;

            for (int i = 0; i < static_cast<int>(ActorList.Num()); i++)
            {
                if (!ActorList.IsValidIndex(i))
                    continue;

                SDK::AActor* actor = ActorList[i];
                if (!IsValidObjectPtr(actor))
                    continue;

                // Verificar se é um summon base
                if (CheckIfSummonBase(actor))
                {
                    // Verificar se é um AMarvelBaseCharacter (nem todos os summons são)
                    if (actor->IsA(SDK::AMarvelBaseCharacter::StaticClass()))
                    {
                        auto SummonChar = static_cast<SDK::AMarvelBaseCharacter*>(actor);
                        if (IsValidObjectPtr(SummonChar))
                        {
                            // Verificar se tem vida
                            if (SummonChar->GetCurrentHealth() <= 0.0f)
                                continue;

                            // Calcular distância ao centro da tela
                            SDK::FVector SummonLocation = SummonChar->K2_GetActorLocation();
                            SDK::FVector2D SummonScreenPos;
                            if (PlayerController->ProjectWorldLocationToScreen(SummonLocation, &SummonScreenPos, true))
                            {
                                double DistanceToCenter = sqrt(pow(SummonScreenPos.X - (ImGui::GetIO().DisplaySize.x / 2), 2) +
                                                             pow(SummonScreenPos.Y - (ImGui::GetIO().DisplaySize.y / 2), 2));

                                // Verificar se está dentro do FOV
                                if (DistanceToCenter <= actualFovCircle && DistanceToCenter < ClosestSummonDistance)
                                {
                                    ClosestSummonDistance = DistanceToCenter;
                                    BestSummon = SummonChar;
                                }
                            }
                        }
                    }
                }
            }

            if (IsValidObjectPtr(BestSummon))
            {
                SafetySystem::LogInfo("SummonPriority", "Summon priorizado encontrado!");
                return BestSummon;
            }
        }

        // TEMPORARIAMENTE DESABILITADO: 🏗️ PRIORIDADE MÁXIMA: Priorizar construtos se a opção estiver ativada
        if (false && mods::shouldPrioritizeConstructs)
        {
            SDK::AActor* BestConstruct = nullptr;
            double ClosestConstructDistance = DBL_MAX;

            for (int i = 0; i < static_cast<int>(ActorList.Num()); i++)
            {
                if (!ActorList.IsValidIndex(i))
                    continue;

                SDK::AActor* actor = ActorList[i];
                if (!IsValidObjectPtr(actor))
                    continue;

                // Verificar se é um construto real (squids, beacons, barriers, etc.)
                if (CheckIfConstruct(actor))
                {
                    // Calcular distância ao centro da tela
                    SDK::FVector ConstructLocation = actor->K2_GetActorLocation();
                    SDK::FVector2D ConstructScreenPos;
                    if (PlayerController->ProjectWorldLocationToScreen(ConstructLocation, &ConstructScreenPos, true))
                    {
                        double DistanceToCenter = sqrt(pow(ConstructScreenPos.X - (ImGui::GetIO().DisplaySize.x / 2), 2) +
                                                     pow(ConstructScreenPos.Y - (ImGui::GetIO().DisplaySize.y / 2), 2));

                        // Verificar se está dentro do FOV
                        if (DistanceToCenter <= actualFovCircle && DistanceToCenter < ClosestConstructDistance)
                        {
                            ClosestConstructDistance = DistanceToCenter;
                            BestConstruct = actor;
                        }
                    }
                }
            }

            if (IsValidObjectPtr(BestConstruct))
            {
                SafetySystem::LogInfo("ConstructPriority", "Construto priorizado encontrado!");

                // Se o construto é um personagem, retornar como AMarvelBaseCharacter
                if (BestConstruct->IsA(SDK::AMarvelBaseCharacter::StaticClass()))
                {
                    return static_cast<SDK::AMarvelBaseCharacter*>(BestConstruct);
                }
                else
                {
                    // Para construtos que não são personagens, usar GetSummonTargetLocation para mira
                    // Retornar nullptr para que o sistema continue procurando jogadores normais
                    // mas registrar que encontrou um construto para debug
                    SafetySystem::LogInfo("ConstructPriority", "Construto não-personagem encontrado, continuando busca por alvos válidos");
                }
            }
        }

        // Obter a classe para filtrar apenas AMarvelBaseCharacter
        SDK::UClass* ClassToFind = SDK::AMarvelBaseCharacter::StaticClass();
        if (!IsValidObjectPtr(ClassToFind))
            return nullptr;

        for (int i = 0; i < static_cast<int>(ActorList.Num()); i++)
        {
            // Verificação de segurança adicional para o índice
            if (!ActorList.IsValidIndex(i))
                continue;

            SDK::AActor* actor = ActorList[i];
            if (!IsValidObjectPtr(actor))
                continue;

            // FILTRO BASEADO NO PROJETO EXEMPLO: Verificar se é AMarvelBaseCharacter ou summon atacável
            bool isPlayer = Modules::CheckIfPlayer(actor, ClassToFind);
            bool isSummon = Modules::CheckIfSummonedTarget(actor);

            if (!isPlayer && !isSummon)
                continue; // Não é um personagem nem summon

            // Se for um summon, processar como alvo válido mas com prioridade menor
            if (isSummon && !isPlayer)
            {
                // Calcular distância para summons
                float DistanceToSummon = actor->GetDistanceTo(PlayerController);

                // Verificar se está dentro do FOV
                SDK::FVector SummonLocation = actor->K2_GetActorLocation();
                SDK::FVector2D SummonScreenPos;
                if (!PlayerController->ProjectWorldLocationToScreen(SummonLocation, &SummonScreenPos, true))
                    continue;

                double DistanceToCenter = sqrt(pow(SummonScreenPos.X - (ImGui::GetIO().DisplaySize.x / 2), 2) +
                                              pow(SummonScreenPos.Y - (ImGui::GetIO().DisplaySize.y / 2), 2));

                if (DistanceToCenter > actualFovCircle)
                    continue;

                // Summons têm prioridade menor, então só selecionar se não houver jogadores próximos
                if (DistanceToCenter < ClosestDistanceToCenter && SelectedTarget == nullptr)
                {
                    ClosestDistanceToCenter = DistanceToCenter;
                    // Não podemos retornar summon como AMarvelBaseCharacter, então pular por enquanto
                    // TODO: Implementar sistema de alvo misto se necessário
                }
                continue;
            }

            auto Player = static_cast<SDK::AMarvelBaseCharacter *>(actor);
            if (!IsValidObjectPtr(Player)) // IsValidObjectPtr já inclui IsValidPtr
                continue;

            if (mods::LocalCheck && Player->IsLocallyControlled())
                continue;

            if (Player->GetCurrentHealth() <= 0.0f)
                continue;

            if (mods::TeamCheck)
            {
                // Verificar se os personagens são válidos
                if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || !IsValidObjectPtr(Player))
                    continue;

                // VERIFICAÇÃO CRÍTICA: Verificar se é um jogador real antes de chamar IsAlly()
                // NPCs não têm PlayerState válido e causam crash ao chamar IsAlly()
                if (!IsValidObjectPtr(Player->PlayerState))
                    continue; // Pular NPCs - não são jogadores válidos

                // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
                // Agora é seguro chamar pois sabemos que é um jogador real
                bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, Player, true);

                // Se estiver no modo TeamTarget (modo suporte), queremos mirar em aliados
                // Se não estiver no modo TeamTarget (modo normal), queremos mirar em inimigos
                if ((mods::teamTargetMode && !bIsAlly) || (!mods::teamTargetMode && bIsAlly))
                {
                    continue; // Pular jogadores que não correspondem ao modo atual
                }
            }

            float DistanceToPlayer = Player->GetDistanceTo(PlayerController);

            USkeletalMeshComponent *Mesh = Player->GetMesh();
            if (!IsValidPtr(Mesh) || !Mesh->SkeletalMesh)
                continue;

            // Obter a posição da cabeça do jogador
            FVector TargetHead3D = Mesh->GetSocketLocation(HeadSocketName);
            if (TargetHead3D.IsZero())
                continue;

            // Verificar visibilidade usando o método antigo e o novo
            bool isVisibleOld = Mesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
            bool isVisibleNew = IsPlayerVisible(PlayerController, Player, TargetHead3D);

            // Escolher o método de verificação de visibilidade com base na opção do usuário
            bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

            if (mods::VisCheck && !isVisible)
                continue;

            // Verificar se deve mirar em alvos invisíveis
            if (!mods::targetInvisibleEnemies)
            {
                // NOVA VERIFICAÇÃO: Detectar invisibilidade real do personagem
                bool isTrulyInvisible = IsPlayerTrulyInvisible(Player);

                // Rejeitar alvo se não estiver visível OU se estiver realmente invisível
                if (!isVisible || isTrulyInvisible)
                    continue;
            }

            FVector2D TargetHead2D;
            if (!PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true))
                continue;

            // Verificar se o alvo está dentro do FOV ou se a Visão de Suporte está ativada
            if (InCircle(actualFovCircle, TargetHead2D) || mods::isSupportVisionEnabled)
            {
                double DistanceToCenter = UKismetMathLibrary::Vector_Distance2D(
                    FVector(Variables::ScreenCenter.X, Variables::ScreenCenter.Y, 0),
                    FVector(TargetHead2D.X, TargetHead2D.Y, 0));
                float CurrentHealth = Player->GetCurrentHealth();

                // Verificar se o alvo está voando (para priorização de alvos voadores)
                SDK::FVector velocity = GetVelocity(Player);
                bool isFlying = false;

                if (mods::shouldPrioritizeFlyingTargets && abs(velocity.Z) > mods::flyingVelocityThreshold)
                {
                    isFlying = true;

                    // Armazenar como alvo voador se for melhor que o atual
                    if ((mods::focusLowestHealth && CurrentHealth < FlyingTarget.Health) ||
                        (!mods::focusLowestHealth && DistanceToCenter < FlyingTarget.DistanceToCenter))
                    {
                        FlyingTarget.Player = Player;
                        FlyingTarget.DistanceToCenter = DistanceToCenter;
                        FlyingTarget.Health = CurrentHealth;
                        FlyingTarget.Role = GetRole(Player);
                        foundFlyingTarget = true;
                    }
                }

                // Verificar se o filtro de role está ativado
                if (mods::enableRoleTarget && mods::targetRole > 0)
                {
                    // Obter a role do jogador
                    SDK::EHeroRole playerRole = GetRole(Player);
                    SDK::EHeroRole targetRole;

                    // Converter o valor do targetRole para o enum EHeroRole
                    switch (mods::targetRole)
                    {
                    case 1:
                        targetRole = SDK::EHeroRole::Tank;
                        break;
                    case 2:
                        targetRole = SDK::EHeroRole::Damage;
                        break;
                    case 3:
                        targetRole = SDK::EHeroRole::Support;
                        break;
                    default:
                        targetRole = SDK::EHeroRole::Unknown;
                        break;
                    }

                    // Se a role do jogador corresponder à role alvo, selecionar como alvo principal
                    if (playerRole == targetRole)
                    {
                        foundTargetWithSelectedRole = true;

                        if (mods::focusLowestHealth)
                        {
                            if (CurrentHealth < LowestHealth)
                            {
                                LowestHealth = CurrentHealth;
                                SelectedTarget = Player;
                                ClosestDistanceToCenter = DistanceToCenter;
                            }
                        }
                        else
                        {
                            if (DistanceToCenter < ClosestDistanceToCenter)
                            {
                                ClosestDistanceToCenter = DistanceToCenter;
                                SelectedTarget = Player;
                                LowestHealth = CurrentHealth;
                            }
                        }
                    }
                    // Caso contrário, armazenar como possível fallback
                    else
                    {
                        // Armazenar o jogador como fallback com base em sua role
                        switch (playerRole)
                        {
                        case SDK::EHeroRole::Support:
                            if ((mods::focusLowestHealth && CurrentHealth < SupportTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < SupportTarget.DistanceToCenter))
                            {
                                SupportTarget.Player = Player;
                                SupportTarget.DistanceToCenter = DistanceToCenter;
                                SupportTarget.Health = CurrentHealth;
                            }
                            break;

                        case SDK::EHeroRole::Damage:
                            if ((mods::focusLowestHealth && CurrentHealth < DamageTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < DamageTarget.DistanceToCenter))
                            {
                                DamageTarget.Player = Player;
                                DamageTarget.DistanceToCenter = DistanceToCenter;
                                DamageTarget.Health = CurrentHealth;
                            }
                            break;

                        case SDK::EHeroRole::Tank:
                            if ((mods::focusLowestHealth && CurrentHealth < TankTarget.Health) ||
                                (!mods::focusLowestHealth && DistanceToCenter < TankTarget.DistanceToCenter))
                            {
                                TankTarget.Player = Player;
                                TankTarget.DistanceToCenter = DistanceToCenter;
                                TankTarget.Health = CurrentHealth;
                            }
                            break;

                        default:
                            break;
                        }
                    }
                }
                // Se o filtro de role não estiver ativado, usar a lógica normal
                else
                {
                    if (mods::focusLowestHealth)
                    {
                        if (CurrentHealth < LowestHealth)
                        {
                            LowestHealth = CurrentHealth;
                            SelectedTarget = Player;
                            ClosestDistanceToCenter = DistanceToCenter;
                        }
                    }
                    else
                    {
                        if (DistanceToCenter < ClosestDistanceToCenter)
                        {
                            ClosestDistanceToCenter = DistanceToCenter;
                            SelectedTarget = Player;
                            LowestHealth = CurrentHealth;
                        }
                    }
                }
            }
        }

        // 🧪 Executar teste de detecção de summons/construtos (apenas para debug)
        static int testCounter = 0;
        testCounter++;
        if (testCounter % 300 == 0) // Executar a cada 300 frames (~5 segundos)
        {
            TestSummonConstructDetection(ActorList);
        }

        // 🎮 Debug do First Enemy Lock
        static int firstEnemyTestCounter = 0;
        firstEnemyTestCounter++;
        if (firstEnemyTestCounter % 180 == 0) // Executar a cada 180 frames (~3 segundos)
        {
            bool keyPressed = IsAimbotKeyPressed(PlayerController);
            bool hasLockedTarget = IsValidObjectPtr(Variables::FirstLockedTarget);
            bool isNewActivation = IsFirstEnemyLockNewActivation(PlayerController);

            std::string debugMessage = "FirstEnemyLock: " + std::string(mods::firstEnemyLock ? "ON" : "OFF") +
                " | Key: " + std::string(keyPressed ? "PRESSED" : "NOT_PRESSED") +
                " | LockedTarget: " + std::string(hasLockedTarget ? "YES" : "NO") +
                " | NewActivation: " + std::string(isNewActivation ? "YES" : "NO");
            SafetySystem::LogInfo("FirstEnemyLockDebug", debugMessage.c_str());
        }

        // Priorizar alvos voadores se a opção estiver ativada
        if (mods::shouldPrioritizeFlyingTargets && foundFlyingTarget && IsValidObjectPtr(FlyingTarget.Player))
        {
            return FlyingTarget.Player;
        }



        // Se não encontramos um alvo da role selecionada, usar o sistema de fallback
        if (mods::enableRoleTarget && mods::targetRole > 0 && !foundTargetWithSelectedRole)
        {
            // Ordem de prioridade: Support > Damage > Tank
            if (IsValidObjectPtr(SupportTarget.Player))
            {
                return SupportTarget.Player;
            }
            else if (IsValidObjectPtr(DamageTarget.Player))
            {
                return DamageTarget.Player;
            }
            else if (IsValidObjectPtr(TankTarget.Player))
            {
                return TankTarget.Player;
            }
        }

        // 🎮 CORREÇÃO: Se First Enemy Lock está ativo, tecla pressionada e encontramos um alvo válido, definir como primeiro alvo travado
            if (mods::firstEnemyLock && IsValidObjectPtr(SelectedTarget) && !IsValidObjectPtr(Variables::FirstLockedTarget) && IsAimbotKeyPressed(PlayerController))
            {
                Variables::FirstLockedTarget = SelectedTarget;

                // DEBUG: Log detalhado quando define novo alvo
                std::string debugMsg = "🎯 Primeiro alvo travado definido! Alvo: " +
                                     std::to_string(reinterpret_cast<uintptr_t>(SelectedTarget));
                SafetySystem::LogInfo("FirstEnemyLock", debugMsg.c_str());
            }

            return SelectedTarget;
    }

    //---------------------------------------------------------------------
    // 		🔄	Verificação de Tecla para Team Target Mode
    //---------------------------------------------------------------------
    void CheckTeamTargetKey(SDK::APlayerController *PlayerController)
    {
        if (!IsValidObjectPtr(PlayerController))
            return;

        // Verificar se a tecla de Team Target Mode está pressionada
        bool isTeamTargetKeyPressed = false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentTeamTargetKey);

        // Verificar gamepad
        SDK::FKey *teamTargetGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepadTeamTargetButton);
        bool isGamepadPressed = false;
        if (IsValidPtr(teamTargetGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*teamTargetGamepadButton);
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isTeamTargetKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        // Verificar se a tecla foi pressionada (toggle)
        static bool wasTeamTargetKeyPressed = false;
        if (isTeamTargetKeyPressed && !wasTeamTargetKeyPressed)
        {
            // Alternar o modo de alvo
            mods::teamTargetMode = !mods::teamTargetMode;

            // Exibir mensagem no console
            printf("Team Target Mode: %s\n", mods::teamTargetMode ? "Aliados (Suporte)" : "Inimigos (Normal)");
        }

        // Atualizar o estado anterior da tecla
        wasTeamTargetKeyPressed = isTeamTargetKeyPressed;
    }

    //---------------------------------------------------------------------
    // 		🎯	Sistema de Aimbot Avançado com Inércia, Smoothing Adaptativo e Humanização
    //---------------------------------------------------------------------
    void RunAimbot(SDK::UWorld *World, SDK::APlayerController *PlayerController, SDK::APlayerCameraManager *PlayerCameraManager, SDK::AMarvelBaseCharacter *TargetPlayer, SDK::FName HeadSocketName, float actualFovCircle)
    {
        // Validação de segurança para todos os ponteiros críticos para previnir crashes de "use-after-free".
        if (!mods::aimbot || !IsValidObjectPtr(World) || !IsValidObjectPtr(PlayerController) || !IsValidObjectPtr(PlayerCameraManager) || !IsValidObjectPtr(TargetPlayer))
            return;

        USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
        if (!IsValidPtr(TargetMesh) || !TargetMesh->SkeletalMesh)
            return;

        bool isAimbotKeyPressed = false;

        // Verificar teclado/mouse
        bool isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentAimbotKey);

        // Verificar gamepad
        SDK::FKey *aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
        bool isGamepadPressed = false;
        if (IsValidPtr(aimbotGamepadButton))
        {
            isGamepadPressed = PlayerController->IsInputKeyDown(*aimbotGamepadButton);
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isAimbotKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        // Detectar se a tecla foi pressionada pela primeira vez
        static bool wasAimbotKeyPressed = false;
        bool keyJustPressed = isAimbotKeyPressed && !wasAimbotKeyPressed;
        wasAimbotKeyPressed = isAimbotKeyPressed;

        if (!isAimbotKeyPressed)
        {
            // Resetar o rastreamento quando a tecla não está pressionada
            Variables::LastTarget = nullptr;
            // CORREÇÃO: Só resetar FirstLockedTarget se firstEnemyLock estiver ativo
            // Isso permite que o sistema mantenha o alvo enquanto a tecla estiver pressionada
            if (mods::firstEnemyLock) {
                Variables::FirstLockedTarget = nullptr;
            }
            Variables::TargetTrackingTime = 0.0f;
            Variables::IsAcquiringTarget = false;
            Variables::TargetAcquisitionTime = 0.0f;

            // Resetar os valores adaptativos para os iniciais
            // GARANTIA: Aplicar valores seguros com verificação de limites
            Variables::CurrentSmoothAmount = mods::Clamp(mods::initialSmoothAmount, 0.1f, 100.0f);
            Variables::CurrentSmoothAmountPitch = mods::Clamp(mods::initialSmoothAmountPitch, 0.1f, 100.0f);
            Variables::CurrentSmoothAmountYaw = mods::Clamp(mods::initialSmoothAmountYaw, 0.1f, 100.0f);
            Variables::CurrentInertiaFactor = mods::Clamp(mods::initialInertiaFactor, 0.0f, 1.0f);

            // DEBUG: Log do reset quando tecla é solta (opcional)
            // printf("🔄 ADAPTIVE RESET - Tecla do aimbot solta. Valores resetados.\n");

            // Resetar estado da tecla do Flick quando aimbot não está ativo
            // (wasAimbotKeyPressed será resetado automaticamente na próxima iteração)

            return;
        }

        // Se a tecla foi pressionada pela primeira vez e o delay está ativado, iniciar o processo de aquisição
        if (keyJustPressed && mods::useAimDelay)
        {
            Variables::IsAcquiringTarget = true;
            Variables::TargetAcquisitionTime = 0.0f;
            printf("Aim Delay INICIADO - Aguardando %.2fs antes de ativar aimbot\n", mods::aimDelayTime);
        }

        // VERIFICAÇÃO IMEDIATA DO DELAY - Deve ser a primeira coisa após ativar o delay
        if (Variables::IsAcquiringTarget)
        {
            // Obter o deltaTime apenas para atualizar o tempo de aquisição
            float deltaTime = UGameplayStatics::GetWorldDeltaSeconds(World);
            Variables::TargetAcquisitionTime += deltaTime;

            // Verificar se o tempo de aquisição foi atingido
            if (Variables::TargetAcquisitionTime >= mods::aimDelayTime)
            {
                Variables::IsAcquiringTarget = false;
                printf("Aim Delay CONCLUÍDO - Aimbot ativado após %.2fs\n", Variables::TargetAcquisitionTime);
            }
            else
            {
                // Ainda estamos adquirindo o alvo, não fazer NADA
                return;
            }
        }

        // Obter o deltaTime diretamente do motor do jogo (mais confiável)
        float deltaTime = UGameplayStatics::GetWorldDeltaSeconds(World);

        // Limitação de deltaTime removida para máxima responsividade

        // Verificar se o alvo mudou ou se o alvo anterior morreu/se tornou inválido
        bool targetChanged = (Variables::LastTarget != TargetPlayer);
        bool lastTargetDied = false;

        // Verificar se o último alvo ainda está vivo (para detectar morte)
        if (!targetChanged && IsValidObjectPtr(Variables::LastTarget))
        {
            if (Variables::LastTarget->GetCurrentHealth() <= 0.0f)
            {
                lastTargetDied = true;
                targetChanged = true; // Tratar como mudança de alvo
            }
        }

        // Se o alvo mudou ou morreu, resetar o rastreamento e movimentos adaptativos
        if (targetChanged)
        {
            Variables::LastTarget = TargetPlayer;
            Variables::TargetTrackingTime = 0.0f;

            // CORREÇÃO: Sempre reiniciar o delay quando o alvo muda, se o delay estiver ativado
            // Isso garante que não haja movimento inicial ao trocar de alvos
            if (mods::useAimDelay)
            {
                Variables::IsAcquiringTarget = true;
                Variables::TargetAcquisitionTime = 0.0f;
                printf("Aim Delay REINICIADO - Novo alvo detectado, aguardando %.2fs\n", mods::aimDelayTime);
            }

            // SEMPRE resetar os valores adaptativos para os iniciais quando o alvo muda
            // GARANTIA: Verificar se os valores iniciais são válidos antes de aplicar
            Variables::CurrentSmoothAmount = mods::Clamp(mods::initialSmoothAmount, 0.1f, 100.0f);
            Variables::CurrentSmoothAmountPitch = mods::Clamp(mods::initialSmoothAmountPitch, 0.1f, 100.0f);
            Variables::CurrentSmoothAmountYaw = mods::Clamp(mods::initialSmoothAmountYaw, 0.1f, 100.0f);
            Variables::CurrentInertiaFactor = mods::Clamp(mods::initialInertiaFactor, 0.0f, 1.0f);

            // DEBUG: Log da mudança de alvo (opcional)
            // printf("🎯 ADAPTIVE RESET - Novo alvo detectado. Valores resetados para iniciais.\n");

            // Se First Enemy Lock não está ativo, resetar o primeiro alvo travado também
            if (!mods::firstEnemyLock)
            {
                Variables::FirstLockedTarget = nullptr;
            }
        }
        else
        {
            // Incrementar o tempo de rastreamento
            Variables::TargetTrackingTime += deltaTime;

            // Nota: Verificação de delay foi movida para o início da função
            // para evitar execução de código antes da verificação
        }

        // Atualizar o sistema de partes do corpo alvo
        float currentTime = SDK::UGameplayStatics::GetTimeSeconds(World);
        mods::UpdateTargetBodyPart(currentTime);

        // Definir os sockets para diferentes partes do corpo (HeadSocketName já é parâmetro)
        FName NeckSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"neck_01"));
        FName ChestSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_02"));
        FName SpineSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_01"));
        FName PelvisSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"pelvis"));

        // Verificar quais sockets existem
        bool hasNeckSocket = TargetMesh->DoesSocketExist(NeckSocketName);
        bool hasChestSocket = TargetMesh->DoesSocketExist(ChestSocketName);
        bool hasSpineSocket = TargetMesh->DoesSocketExist(SpineSocketName);
        bool hasPelvisSocket = TargetMesh->DoesSocketExist(PelvisSocketName);

        // Escolher o socket alvo com base no modo selecionado
        FName TargetSocketName;

        switch (mods::targetBodyPartMode)
        {
        case 0: // Cabeça
            TargetSocketName = HeadSocketName;
            break;
        case 1: // Pescoço
            TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
            break;
        case 2: // Peito
            TargetSocketName = hasChestSocket ? ChestSocketName : (hasNeckSocket ? NeckSocketName : HeadSocketName);
            break;
        case 3: // Coluna
            TargetSocketName = hasSpineSocket ? SpineSocketName : (hasChestSocket ? ChestSocketName : HeadSocketName);
            break;
        case 4: // Pelvis
            TargetSocketName = hasPelvisSocket ? PelvisSocketName : (hasSpineSocket ? SpineSocketName : HeadSocketName);
            break;
        case 5: // Aleatório
            switch (mods::currentRandomBodyPart)
            {
            case 0:
                TargetSocketName = HeadSocketName;
                break;
            case 1:
                TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
                break;
            case 2:
                TargetSocketName = hasChestSocket ? ChestSocketName : HeadSocketName;
                break;
            case 3:
                TargetSocketName = hasSpineSocket ? SpineSocketName : HeadSocketName;
                break;
            case 4:
                TargetSocketName = hasPelvisSocket ? PelvisSocketName : HeadSocketName;
                break;
            default:
                TargetSocketName = HeadSocketName;
                break;
            }
            break;
        case 6: // Humanizado
            switch (mods::humanizedBodyPart)
            {
            case 0:
                TargetSocketName = HeadSocketName;
                break;
            case 1:
                TargetSocketName = hasNeckSocket ? NeckSocketName : HeadSocketName;
                break;
            case 2:
                TargetSocketName = hasChestSocket ? ChestSocketName : HeadSocketName;
                break;
            case 3:
                TargetSocketName = hasSpineSocket ? SpineSocketName : HeadSocketName;
                break;
            case 4:
                TargetSocketName = hasPelvisSocket ? PelvisSocketName : HeadSocketName;
                break;
            default:
                TargetSocketName = HeadSocketName;
                break;
            }
            break;
        default:
            // Fallback para o comportamento antigo
            TargetSocketName = (hasNeckSocket && mods::useNeckTarget) ? NeckSocketName : HeadSocketName;
            break;
        }

        // Obter a posição 3D do alvo
        FVector Target3D;

        // Verificar se o alvo é um summon/construto e usar função apropriada
        if (CheckIfSummonedTarget(TargetPlayer))
        {
            Target3D = GetSummonTargetLocation(TargetPlayer);
        }
        else
        {
            // Para jogadores normais, usar socket
            Target3D = TargetMesh->GetSocketLocation(TargetSocketName);
        }

        if (Target3D.IsZero())
            return;

        // Obter a velocidade do alvo
        SDK::FVector TargetVelocity = GetVelocity(TargetPlayer);

        // Obter a posição do jogador local
        SDK::FVector LocalPosition = PlayerCameraManager->GetCameraLocation();

        // Calcular a distância até o alvo
        float DistanceToTarget = (Target3D - LocalPosition).Magnitude();

        // Calcular a rotação para o alvo
        FRotator TargetRotation = UKismetMathLibrary::FindLookAtRotation(
            PlayerCameraManager->GetCameraLocation(),
            Target3D);

        // Projetar a posição 3D para 2D para calcular a distância ao centro
        FVector2D Target2D;
        PlayerController->ProjectWorldLocationToScreen(Target3D, &Target2D, true);
        double DistanceToCenter = UKismetMathLibrary::Vector_Distance2D(
            FVector(Variables::ScreenCenter.X, Variables::ScreenCenter.Y, 0),
            FVector(Target2D.X, Target2D.Y, 0));

        auto currentRotation = PlayerController->GetControlRotation();

        //---------------------------------------------------------------------
        // 		⚡	Sistema Flick Simplificado e Autônomo (Respeitando Aim Delay)
        //---------------------------------------------------------------------
        float flickSmoothingMultiplier = 1.0f;
        float flickInertiaMultiplier = 1.0f;

        // Usar a detecção de pressionamento já feita no início da função
        // (keyJustPressed já foi calculado anteriormente)

        // Verificar se temos um alvo válido e se ele passa por todas as verificações de segurança
        bool hasValidTarget = IsValidObjectPtr(TargetPlayer);
        bool targetPassesChecks = true;

        if (hasValidTarget)
        {
            // Verificar team check se ativado
            if (mods::TeamCheck && IsValidObjectPtr(Variables::AcknowledgedPawn))
            {
                // VERIFICAÇÃO CRÍTICA: Verificar se é um jogador real antes de chamar IsAlly()
                // NPCs não têm PlayerState válido e causam crash ao chamar IsAlly()
                if (!IsValidObjectPtr(TargetPlayer->PlayerState))
                {
                    targetPassesChecks = false; // NPC não é um alvo válido
                }
                else
                {
                    // Agora é seguro chamar IsAlly() pois sabemos que é um jogador real
                    bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, TargetPlayer, true);
                    if ((mods::teamTargetMode && !bIsAlly) || (!mods::teamTargetMode && bIsAlly))
                    {
                        targetPassesChecks = false;
                    }
                }
            }

            // Verificar visibilidade se ativado
            if (targetPassesChecks && mods::VisCheck)
            {
                USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
                if (IsValidObjectPtr(TargetMesh))
                {
                    FVector TargetHead3D = TargetMesh->GetSocketLocation(HeadSocketName);
                    bool isVisibleOld = TargetMesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
                    bool isVisibleNew = IsPlayerVisible(PlayerController, TargetPlayer, TargetHead3D);
                    bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

                    if (!isVisible)
                    {
                        targetPassesChecks = false;
                    }
                }
                else
                {
                    targetPassesChecks = false;
                }
            }

            // Verificar se deve mirar em alvos invisíveis
            if (targetPassesChecks && !mods::targetInvisibleEnemies)
            {
                USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
                if (IsValidObjectPtr(TargetMesh))
                {
                    FVector TargetHead3D = TargetMesh->GetSocketLocation(HeadSocketName);

                    // Verificação tradicional de visibilidade (linha de visão/renderização)
                    bool isVisibleOld = TargetMesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.f);
                    bool isVisibleNew = IsPlayerVisible(PlayerController, TargetPlayer, TargetHead3D);
                    bool isVisible = mods::UseLineOfSight ? isVisibleNew : isVisibleOld;

                    // NOVA VERIFICAÇÃO: Detectar invisibilidade real do personagem
                    bool isTrulyInvisible = IsPlayerTrulyInvisible(TargetPlayer);

                    // Rejeitar alvo se não estiver visível OU se estiver realmente invisível
                    if (!isVisible || isTrulyInvisible)
                    {
                        targetPassesChecks = false;
                    }
                }
                else
                {
                    targetPassesChecks = false;
                }
            }

            // Verificar FOV se não estivermos em modo Support Vision
            if (targetPassesChecks && !mods::isSupportVisionEnabled)
            {
                USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
                if (IsValidObjectPtr(TargetMesh))
                {
                    FVector TargetHead3D = TargetMesh->GetSocketLocation(HeadSocketName);
                    FVector2D TargetHead2D;
                    if (PlayerController->ProjectWorldLocationToScreen(TargetHead3D, &TargetHead2D, true))
                    {
                        float actualFovCircle = (mods::fov * Variables::ScreenSize.X / mods::fov_changer_amount) / 2.0f;
                        if (!InCircle(actualFovCircle, TargetHead2D))
                        {
                            targetPassesChecks = false;
                        }
                    }
                    else
                    {
                        targetPassesChecks = false;
                    }
                }
                else
                {
                    targetPassesChecks = false;
                }
            }
        }

        // Ativar Flick ao pressionar a tecla (respeitando todos os limitadores)
        if (mods::useFlick && keyJustPressed && !Variables::IsAcquiringTarget && hasValidTarget && targetPassesChecks)
        {
            mods::flickIsActive = true;
            mods::flickStartTime = currentTime;
            printf("Flick ATIVADO - Duração: %.1fs (respeitando todos os limitadores)\n", mods::flickDuration);
        }

        // Processar Flick ativo (respeitando todos os limitadores)
        if (mods::useFlick && mods::flickIsActive && !Variables::IsAcquiringTarget && hasValidTarget && targetPassesChecks)
        {
            float elapsedTime = currentTime - mods::flickStartTime;

            // Verificar se o tempo de duração foi atingido
            if (elapsedTime >= mods::flickDuration)
            {
                mods::flickIsActive = false;
                printf("Flick DESATIVADO - Tempo completado (%.1fs)\n", elapsedTime);
            }
            else
            {
                // Calcular progresso da redução (0.0 a 1.0)
                float progress = elapsedTime / mods::flickDuration;
                progress = mods::Clamp(progress, 0.0f, 1.0f);

                // Aplicar curva de redução baseada no tipo selecionado
                float reductionCurve = 0.0f;
                const char* curveTypeName = "";

                switch (mods::flickReductionType)
                {
                    case 0: // Linear
                        reductionCurve = progress;
                        curveTypeName = "Linear";
                        break;

                    case 1: // Exponencial (rápido no início, suave no fim)
                        reductionCurve = 1.0f - std::exp(-3.0f * progress);
                        curveTypeName = "Exponencial";
                        break;

                    case 2: // Quadrático (suave no início, rápido no fim)
                        reductionCurve = progress * progress;
                        curveTypeName = "Quadrático";
                        break;

                    default:
                        reductionCurve = progress; // Fallback para linear
                        curveTypeName = "Linear (fallback)";
                        break;
                }

                // Aplicar redução: de 100% para 10% baseado na curva
                // Fórmula: 1.0 - (0.9 * curva) = vai de 1.0 (100%) para 0.1 (10%)
                flickSmoothingMultiplier = 1.0f - (0.9f * reductionCurve);
                flickInertiaMultiplier = 1.0f - (0.9f * reductionCurve);

                // Garantir que nunca vá abaixo de 10%
                flickSmoothingMultiplier = mods::Clamp(flickSmoothingMultiplier, 0.1f, 1.0f);
                flickInertiaMultiplier = mods::Clamp(flickInertiaMultiplier, 0.1f, 1.0f);

                // Log para depuração (limitado para evitar spam)
                static float lastFlickLogTime = 0.0f;
                if (currentTime - lastFlickLogTime > 1.0f) {
                    printf("Flick ATIVO - Tipo: %s, Progresso: %.2f, Curva: %.2f, Mult: %.2f, Tempo restante: %.1fs\n",
                           curveTypeName, progress, reductionCurve, flickSmoothingMultiplier, mods::flickDuration - elapsedTime);
                    lastFlickLogTime = currentTime;
                }
            }
        }

        // Se o Flick está ativo mas não pode ser processado, pausar mas manter o estado
        if (mods::useFlick && mods::flickIsActive &&
            (Variables::IsAcquiringTarget || !hasValidTarget || !targetPassesChecks))
        {
            // Log para indicar por que o Flick está pausado
            static float lastPauseLogTime = 0.0f;
            if (currentTime - lastPauseLogTime > 2.0f) {
                const char* pauseReason = "desconhecido";
                if (Variables::IsAcquiringTarget) {
                    pauseReason = "aim delay ativo";
                } else if (!hasValidTarget) {
                    pauseReason = "alvo inválido";
                } else if (!targetPassesChecks) {
                    pauseReason = "alvo não passou nas verificações de segurança";
                }

                printf("Flick PAUSADO - Motivo: %s\n", pauseReason);
                lastPauseLogTime = currentTime;
            }
        }

        // Sistema de suavização adaptativo
        if (mods::useAdaptiveMovement)
        {
            // SEGURANÇA: Verificar se a duração adaptativa é válida
            float safeDuration = mods::Clamp(mods::adaptiveDuration, 0.1f, 30.0f);

            // Calcular o progresso da adaptação (0.0 a 1.0)
            // GARANTIA: Progresso proporcional ao tempo selecionado
            float adaptiveProgress = mods::Clamp(Variables::TargetTrackingTime / safeDuration, 0.0f, 1.0f);

            // Aplicar curva linear para uma transição consistente e previsível
            // A inércia já cuida da suavização, então linear é mais apropriado
            float adaptiveCurve = adaptiveProgress;

            if (mods::separateAdaptiveSettings)
            {
                // SEGURANÇA: Garantir que valores mínimos não excedam os iniciais
                float safePitchMin = mods::Clamp(mods::minSmoothAmountPitch, 0.1f, mods::initialSmoothAmountPitch);
                float safeYawMin = mods::Clamp(mods::minSmoothAmountYaw, 0.1f, mods::initialSmoothAmountYaw);

                // Interpolar entre os valores inicial e mínimo para pitch e yaw usando a curva
                Variables::CurrentSmoothAmountPitch = mods::initialSmoothAmountPitch -
                    (adaptiveCurve * (mods::initialSmoothAmountPitch - safePitchMin));

                Variables::CurrentSmoothAmountYaw = mods::initialSmoothAmountYaw -
                    (adaptiveCurve * (mods::initialSmoothAmountYaw - safeYawMin));
            }
            else
            {
                // SEGURANÇA: Garantir que valor mínimo não exceda o inicial
                float safeMin = mods::Clamp(mods::minSmoothAmount, 0.1f, mods::initialSmoothAmount);

                // Interpolar entre os valores inicial e mínimo usando a curva
                Variables::CurrentSmoothAmount = mods::initialSmoothAmount -
                    (adaptiveCurve * (mods::initialSmoothAmount - safeMin));
            }

            // Atualizar o fator de inércia se a inércia adaptativa estiver ativada
            if (mods::useAdaptiveInertia)
            {
                // SEGURANÇA: Garantir que valor mínimo de inércia não exceda o inicial
                float safeInertiaMin = mods::Clamp(mods::minInertiaFactor, 0.0f, mods::initialInertiaFactor);

                Variables::CurrentInertiaFactor = mods::initialInertiaFactor -
                    (adaptiveCurve * (mods::initialInertiaFactor - safeInertiaMin));
            }

            // DEBUG: Log do progresso adaptativo (opcional)
            // static float lastLogTime = 0.0f;
            // if (Variables::TargetTrackingTime - lastLogTime >= 0.5f) // Log a cada 0.5s
            // {
            //     printf("📈 ADAPTIVE PROGRESS - Tempo: %.2fs/%.2fs (%.1f%%) | Smooth: %.2f\n",
            //            Variables::TargetTrackingTime, safeDuration, adaptiveProgress * 100.0f,
            //            mods::separateAdaptiveSettings ? Variables::CurrentSmoothAmountPitch : Variables::CurrentSmoothAmount);
            //     lastLogTime = Variables::TargetTrackingTime;
            // }
        }

        // Ajustar o fator de suavização com base na distância
        float DistanceFactor = mods::Clamp(DistanceToTarget / 1000.0f, 0.5f, 2.0f);

        // Calcular a diferença normalizada entre a rotação atual e a rotação alvo
        FRotator DeltaRot = UKismetMathLibrary::NormalizedDeltaRotator(TargetRotation, currentRotation);

        // Aplicar variação aleatória para movimento mais natural
        float randomVariation = UKismetMathLibrary::RandomFloatInRange(0.95f, 1.05f);

        // Calcular a nova rotação com base nas configurações
        // Sistema de smoothing melhorado baseado no projeto de exemplo
        // Usa interpolação exponencial em vez de linear para movimento mais natural
        SDK::FRotator newRotation;

        if (mods::separatePitchYawSmoothing)
        {
            // Usar valores separados para Pitch e Yaw
            float smoothAmountPitch = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmountPitch : mods::smoothAmountPitch;
            float smoothAmountYaw = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmountYaw : mods::smoothAmountYaw;

            // Aplicar multiplicadores do sistema Flick
            smoothAmountPitch *= flickSmoothingMultiplier;
            smoothAmountYaw *= flickSmoothingMultiplier;

            // Normalizar para 0-1 para aplicar curva quadrática (igual ao projeto de exemplo)
            float normalizedPitch = smoothAmountPitch / 10.0f;
            float normalizedYaw = smoothAmountYaw / 10.0f;

            // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
            float pitchCurve = normalizedPitch * normalizedPitch;
            float yawCurve = normalizedYaw * normalizedYaw;

            // Calcular o fator de suavização com escala exponencial (igual ao projeto de exemplo)
            float pitchFactor = mods::Clamp(std::exp(-4.0f * pitchCurve), 0.01f, 1.0f);
            float yawFactor = mods::Clamp(std::exp(-4.0f * yawCurve), 0.01f, 1.0f);

            // Calcular velocidade de interpolação exponencial (melhorado do projeto de exemplo)
            float InterpSpeedPitch = 20.0f * pitchFactor;
            float InterpSpeedYaw = 20.0f * yawFactor;

            // Ajustar com base na distância e adicionar variação aleatória
            InterpSpeedPitch *= DistanceFactor * randomVariation;
            InterpSpeedYaw *= DistanceFactor * randomVariation;

            if (mods::useInertia)
            {
                // Sistema de inércia melhorado (baseado no projeto de exemplo)
                float inertiaFactorToUse = mods::useAdaptiveInertia ? Variables::CurrentInertiaFactor : mods::inertiaFactor;

                // Aplicar multiplicador do sistema Flick
                inertiaFactorToUse *= flickInertiaMultiplier;

                // Normalizar para 0-1 (baseado no projeto de exemplo)
                float normalizedInertia = mods::Clamp(inertiaFactorToUse / 10.0f, 0.0f, 1.0f);

                // Aplicar curva linear para distribuição mais consistente
                // Curva linear oferece influência mais uniforme em todos os níveis
                float inertiaCurve = normalizedInertia;

                // Calcular o fator de inércia com base na curva (igual ao projeto de exemplo)
                // Valores maiores de inertiaFactor resultam em mais inércia
                float adjustedInertiaFactor = mods::Clamp(inertiaCurve, 0.01f, 0.99f);

                // Log para depuração (limitado para evitar spam) - baseado no projeto de exemplo
                static float lastInertiaLogTime = 0.0f;
                float currentInertiaTime = UGameplayStatics::GetTimeSeconds(UWorld::GetWorld());
                if (currentInertiaTime - lastInertiaLogTime > 5.0f) {
                    std::string adaptiveInfo = mods::useAdaptiveInertia ? " (adaptativo)" : "";
                    printf("Inércia aplicada%s - Valor original=%.3f, curva=%.3f, fator ajustado=%.3f\n",
                           adaptiveInfo.c_str(), normalizedInertia, inertiaCurve, adjustedInertiaFactor);
                    lastInertiaLogTime = currentInertiaTime;
                }

                // Calcular o delta de rotação suavizado (baseado no projeto de exemplo)
                SDK::FRotator rotationDelta;
                rotationDelta.Pitch = DeltaRot.Pitch * (1.0f - std::exp(-InterpSpeedPitch * deltaTime));
                rotationDelta.Yaw = DeltaRot.Yaw * (1.0f - std::exp(-InterpSpeedYaw * deltaTime));
                rotationDelta.Roll = 0.0f;

                // Interpolar entre a velocidade atual e a nova velocidade desejada (igual ao projeto de exemplo)
                // Quanto maior o adjustedInertiaFactor, mais a velocidade atual é preservada
                Variables::CurrentVelocity.Pitch = Variables::CurrentVelocity.Pitch * adjustedInertiaFactor +
                                                  rotationDelta.Pitch * (1.0f - adjustedInertiaFactor);
                Variables::CurrentVelocity.Yaw = Variables::CurrentVelocity.Yaw * adjustedInertiaFactor +
                                                rotationDelta.Yaw * (1.0f - adjustedInertiaFactor);

                // Aplicar a velocidade atual para obter a nova rotação (igual ao projeto de exemplo)
                newRotation.Pitch = currentRotation.Pitch + Variables::CurrentVelocity.Pitch;
                newRotation.Yaw = currentRotation.Yaw + Variables::CurrentVelocity.Yaw;
                newRotation.Roll = 0;

                // Adicionar informações de velocidade ao log de inércia (baseado no projeto de exemplo)
                if (currentInertiaTime - lastInertiaLogTime > 5.0f) {
                    printf("Velocidade aplicada - Pitch=%.3f, Yaw=%.3f\n",
                           Variables::CurrentVelocity.Pitch, Variables::CurrentVelocity.Yaw);
                }
            }
            else
            {
                // Interpolar suavemente sem inércia usando interpolação exponencial (do projeto de exemplo)
                // Fórmula: Current + Delta * (1.0 - std::exp(-InterpSpeed * DeltaTime))
                // Isso cria um efeito de desaceleração mais natural e suave
                float pitchInterpolationFactor = 1.0f - std::exp(-InterpSpeedPitch * deltaTime);
                float yawInterpolationFactor = 1.0f - std::exp(-InterpSpeedYaw * deltaTime);

                newRotation.Pitch = currentRotation.Pitch + DeltaRot.Pitch * pitchInterpolationFactor;
                newRotation.Yaw = currentRotation.Yaw + DeltaRot.Yaw * yawInterpolationFactor;
                newRotation.Roll = 0;
            }
        }
        else
        {
            // Usar valor único para ambos Pitch e Yaw
            float smoothAmount = mods::useAdaptiveMovement ? Variables::CurrentSmoothAmount : mods::smoothAmount;

            // Aplicar multiplicador do sistema Flick
            smoothAmount *= flickSmoothingMultiplier;

            // Normalizar para 0-1 para aplicar curva quadrática (igual ao projeto de exemplo)
            float normalizedSmooth = smoothAmount / 10.0f;

            // Aplicar curva quadrática: y = x^2 (igual ao projeto de exemplo)
            float smoothCurve = normalizedSmooth * normalizedSmooth;

            // Calcular o fator de suavização com escala exponencial (igual ao projeto de exemplo)
            float smoothFactor = mods::Clamp(std::exp(-4.0f * smoothCurve), 0.01f, 1.0f);

            // Calcular velocidade de interpolação exponencial (melhorado do projeto de exemplo)
            float InterpSpeed = 20.0f * smoothFactor;

            // Ajustar com base na distância e adicionar variação aleatória
            InterpSpeed *= DistanceFactor * randomVariation;

            if (mods::useInertia)
            {
                // Sistema de inércia melhorado (baseado no projeto de exemplo)
                float inertiaFactorToUse = mods::useAdaptiveInertia ? Variables::CurrentInertiaFactor : mods::inertiaFactor;

                // Aplicar multiplicador do sistema Flick
                inertiaFactorToUse *= flickInertiaMultiplier;

                // Normalizar para 0-1 (baseado no projeto de exemplo)
                float normalizedInertia = mods::Clamp(inertiaFactorToUse / 10.0f, 0.0f, 1.0f);

                // Aplicar curva linear para distribuição mais consistente
                // Curva linear oferece influência mais uniforme em todos os níveis
                float inertiaCurve = normalizedInertia;

                // Calcular o fator de inércia com base na curva (igual ao projeto de exemplo)
                // Valores maiores de inertiaFactor resultam em mais inércia
                float adjustedInertiaFactor = mods::Clamp(inertiaCurve, 0.01f, 0.99f);

                // Log para depuração (limitado para evitar spam) - baseado no projeto de exemplo
                static float lastInertiaLogTime2 = 0.0f;
                float currentInertiaTime2 = UGameplayStatics::GetTimeSeconds(UWorld::GetWorld());
                if (currentInertiaTime2 - lastInertiaLogTime2 > 5.0f) {
                    std::string adaptiveInfo = mods::useAdaptiveInertia ? " (adaptativo)" : "";
                    printf("Inércia aplicada%s - Valor original=%.3f, curva=%.3f, fator ajustado=%.3f\n",
                           adaptiveInfo.c_str(), normalizedInertia, inertiaCurve, adjustedInertiaFactor);
                    lastInertiaLogTime2 = currentInertiaTime2;
                }

                // Calcular o delta de rotação suavizado (baseado no projeto de exemplo)
                SDK::FRotator rotationDelta;
                rotationDelta.Pitch = DeltaRot.Pitch * (1.0f - std::exp(-InterpSpeed * deltaTime));
                rotationDelta.Yaw = DeltaRot.Yaw * (1.0f - std::exp(-InterpSpeed * deltaTime));
                rotationDelta.Roll = 0.0f;

                // Interpolar entre a velocidade atual e a nova velocidade desejada (igual ao projeto de exemplo)
                // Quanto maior o adjustedInertiaFactor, mais a velocidade atual é preservada
                Variables::CurrentVelocity.Pitch = Variables::CurrentVelocity.Pitch * adjustedInertiaFactor +
                                                  rotationDelta.Pitch * (1.0f - adjustedInertiaFactor);
                Variables::CurrentVelocity.Yaw = Variables::CurrentVelocity.Yaw * adjustedInertiaFactor +
                                                rotationDelta.Yaw * (1.0f - adjustedInertiaFactor);

                // Aplicar a velocidade atual para obter a nova rotação (igual ao projeto de exemplo)
                newRotation.Pitch = currentRotation.Pitch + Variables::CurrentVelocity.Pitch;
                newRotation.Yaw = currentRotation.Yaw + Variables::CurrentVelocity.Yaw;
                newRotation.Roll = 0;

                // Adicionar informações de velocidade ao log de inércia (baseado no projeto de exemplo)
                if (currentInertiaTime2 - lastInertiaLogTime2 > 5.0f) {
                    printf("Velocidade aplicada - Pitch=%.3f, Yaw=%.3f\n",
                           Variables::CurrentVelocity.Pitch, Variables::CurrentVelocity.Yaw);
                }
            }
            else
            {
                // Interpolar suavemente sem inércia usando interpolação exponencial (do projeto de exemplo)
                // Fórmula: Current + Delta * (1.0 - std::exp(-InterpSpeed * DeltaTime))
                // Isso cria um efeito de desaceleração mais natural e suave
                float interpolationFactor = 1.0f - std::exp(-InterpSpeed * deltaTime);

                newRotation.Pitch = currentRotation.Pitch + DeltaRot.Pitch * interpolationFactor;
                newRotation.Yaw = currentRotation.Yaw + DeltaRot.Yaw * interpolationFactor;
                newRotation.Roll = 0;
            }
        }

        // VERIFICAÇÃO FINAL: Garantir que não há movimento durante o delay
        // Esta é uma verificação de segurança adicional para evitar movimento inicial
        if (Variables::IsAcquiringTarget)
        {
            // Ainda estamos em processo de aquisição de alvo, não aplicar movimento
            return;
        }

        // Armazenar a última rotação aplicada
        Variables::LastRotation = newRotation;

        // Aplicar a nova rotação
        PlayerController->SetControlRotation(newRotation);
    }

    //---------------------------------------------------------------------
    // 		🔍	Debug simples para rastrear problemas
    //---------------------------------------------------------------------
    void LogProjectileIssue(const std::string& issue, float angle, float distance, float speed)
    {
        if (mods::bulletTPDebug) {
            std::string message = "PROBLEMA: " + issue +
                                 " | Ângulo=" + std::to_string(angle) + "°" +
                                 " | Dist=" + std::to_string(distance) +
                                 " | Vel=" + std::to_string(speed);
            SafetySystem::LogError("BulletTP", message.c_str());
        }
    }

    //---------------------------------------------------------------------
    // 		🛡️	Verificar se estamos em estado de combate válido
    //---------------------------------------------------------------------
    bool IsInFightingState(SDK::UWorld* World)
    {
        if (!IsValidObjectPtr(World))
            return false;

        try
        {
            SDK::EMatchState outState;
            if (SDK::UMarvelBlueprintLibrary::GetMatchState(World, &outState))
            {
                return (outState == SDK::EMatchState::Fighting);
            }
        }
        catch (...)
        {
            SafetySystem::LogError("IsInFightingState", "Exceção ao verificar estado de combate");
        }

        return false; // Assumir não-Fighting para segurança
    }

    //---------------------------------------------------------------------
    // 		🧹	Limpar dados persistentes do BulletTP quando sair do combate
    //---------------------------------------------------------------------
    void CleanupBulletTPOnStateChange()
    {
        if (!Variables::TrackedProjectiles.empty())
        {
            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Limpando dados do BulletTP - mudança de estado detectada");
            }

            Variables::TrackedProjectiles.clear();
            Variables::CurrentBulletTPTarget = 0;
            Variables::TotalProjectilesFired = 0;
            Variables::LastBulletTPTime = 0.0f;
        }
    }

    //---------------------------------------------------------------------
    // 		🔍	Verificar se o projétil causou dano ao alvo
    //---------------------------------------------------------------------
    void CheckBulletTPDamage(AMarvelBaseCharacter *TargetPlayer, float currentTime)
    {
        // 🛡️ PROTEÇÃO CRÍTICA: Verificar estado de combate primeiro
        if (!IsInFightingState(Variables::World))
        {
            CleanupBulletTPOnStateChange();
            return;
        }

        // Verificar se o sistema de detecção de dano está ativado
        if (!mods::bulletTPShowDamageInfo)
            return;

        // Verificar se o alvo é válido
        if (!IsValidObjectPtr(TargetPlayer))
            return;

        // Verificar se é hora de verificar o dano
        if (currentTime - mods::bulletTPLastCheckTime < mods::bulletTPDamageCheckInterval)
            return;

        // Atualizar o tempo da última verificação
        mods::bulletTPLastCheckTime = currentTime;

        // Obter a saúde atual do alvo com proteção
        float currentHealth = 0.0f;
        try
        {
            currentHealth = TargetPlayer->GetCurrentHealth();
        }
        catch (...)
        {
            SafetySystem::LogError("CheckBulletTPDamage", "Exceção ao acessar saúde do alvo - estado inválido");
            return;
        }

        // Se a saúde do alvo foi inicializada
        if (Variables::LastTargetHealth > 0.0f)
        {
            // Verificar se a saúde do alvo diminuiu
            if (currentHealth < Variables::LastTargetHealth)
            {
                // Calcular o dano causado
                float damageCaused = Variables::LastTargetHealth - currentHealth;

                // Atualizar contadores
                mods::bulletTPHitCount++;
                Variables::TotalProjectilesHit++;
                Variables::HasTargetHealthChanged = true;
                Variables::LastHealthChangeTime = currentTime;
                mods::bulletTPLastHitTime = currentTime;

                // Exibir informações de dano se o debug estiver ativado
                if (mods::bulletTPDebug)
                {
                    std::string message = "BulletTP: Dano causado: " + std::to_string(damageCaused) +
                                         " | Acertos: " + std::to_string(mods::bulletTPHitCount) +
                                         " | Erros: " + std::to_string(mods::bulletTPMissCount);
                    SafetySystem::LogError("BulletTP", message.c_str());
                }
            }
            // Verificar se projéteis foram teleportados mas não causaram dano
            else if (Variables::TrackedProjectiles.empty() && Variables::TotalProjectilesFired > 0 &&
                    !Variables::HasTargetHealthChanged &&
                    (currentTime - Variables::LastHealthChangeTime > 1.0f))
            {
                // Incrementar contador de erros
                mods::bulletTPMissCount++;

                // Exibir informações de erro se o debug estiver ativado
                if (mods::bulletTPDebug)
                {
                    std::string message = "BulletTP: Projétil não causou dano | Acertos: " +
                                         std::to_string(mods::bulletTPHitCount) +
                                         " | Erros: " + std::to_string(mods::bulletTPMissCount);
                    SafetySystem::LogError("BulletTP", message.c_str());
                }

                // Resetar o contador de projéteis disparados
                Variables::TotalProjectilesFired = 0;
            }
        }

        // Atualizar a última saúde do alvo
        Variables::LastTargetHealth = currentHealth;
    }

    //---------------------------------------------------------------------
    // 		⏱️	Controle de frequência simples
    //---------------------------------------------------------------------
    bool ShouldRedirectProjectile(uint64_t ProjectileID, float currentTime)
    {
        static std::unordered_map<uint64_t, float> LastRedirectTimes;

        const float MIN_REDIRECT_INTERVAL = 0.1f; // 100ms entre redirecionamentos

        auto it = LastRedirectTimes.find(ProjectileID);
        if (it != LastRedirectTimes.end()) {
            if (currentTime - it->second < MIN_REDIRECT_INTERVAL) {
                return false;
            }
        }

        LastRedirectTimes[ProjectileID] = currentTime;
        return true;
    }

    //---------------------------------------------------------------------
    // 		🔍	Rastreamento simples de projéteis
    //---------------------------------------------------------------------
    void TrackProjectileForDebug(SDK::AActor* Bullet, SDK::FVector TargetLocation, float currentTime, float angle, float distance)
    {
        static std::unordered_map<uint64_t, int> ProjectileRedirectCount;

        uint64_t ProjectileID = reinterpret_cast<uint64_t>(Bullet);
        ProjectileRedirectCount[ProjectileID]++;

        if (mods::bulletTPDebug) {
            std::string message = "Projétil: ID=" + std::to_string(ProjectileID) +
                                 " | Redirs=" + std::to_string(ProjectileRedirectCount[ProjectileID]) +
                                 " | Dist=" + std::to_string(distance) +
                                 " | Ângulo=" + std::to_string(angle) + "°";
            SafetySystem::LogError("BulletTP", message.c_str());
        }

        // Detectar problemas específicos
        if (angle > 90.0f) {
            LogProjectileIssue("Ângulo muito grande", angle, distance, 0.0f);
        }

        if (distance < 50.0f) {
            LogProjectileIssue("Muito próximo do alvo", angle, distance, 0.0f);
        }
    }

    //---------------------------------------------------------------------
    // 		🛡️	SISTEMA DE LIMPEZA SEGURA DE PROJÉTEIS RASTREADOS
    //---------------------------------------------------------------------
    void SafeClearTrackedProjectiles(const char* reason)
    {
        if (!Variables::TrackedProjectiles.empty())
        {
            if (mods::bulletTPDebug)
            {
                std::string message = "Limpando " + std::to_string(Variables::TrackedProjectiles.size()) +
                                    " projéteis rastreados - Motivo: " + std::string(reason);
                SafetySystem::LogError("BulletTP", message.c_str());
            }

            Variables::TrackedProjectiles.clear();
            Variables::CurrentBulletTPTarget = 0;
            Variables::TotalProjectilesFired = 0;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	BYPASS ESPECÍFICO: Validação de Tempo de Voo
    //---------------------------------------------------------------------
    void BypassFlightTimeValidation(SDK::AMarvelAbilityTargetActor_Projectile* Projectile, float currentTime)
    {
        if (!IsValidPtr(Projectile)) return;

        try {
            // Bypass 1: Resetar tempo de spawn para evitar timeout por tempo excessivo
            // Baseado em FProjectileHitInfo.FlyTime identificado no SDK
            try {
                // Simular que o projétil foi spawned recentemente
                // Isso evita que validações de tempo rejeitem projéteis redirecionados
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("TimeBypass", "Tempo de voo resetado para evitar timeout");
                }
            } catch (...) {
                // Falha silenciosa
            }

            // Bypass 2: Manipular dados de movimento para resetar contadores de tempo
            if (IsValidPtr(Projectile->MovementComponent)) {
                try {
                    // Tentar resetar propriedades de tempo no componente de movimento
                    // Nota: Propriedades específicas podem variar no SDK customizado
                    if (mods::bulletTPDebug) {
                        SafetySystem::LogError("TimeBypass", "Componente de movimento acessado para bypass de tempo");
                    }
                } catch (...) {
                    // Falha silenciosa para componente de movimento
                }
            }

            // Bypass 3: Lidar com validações específicas de swift projectiles
            // Baseado em HasFlyDuration e SegmentFlyDuration identificados no SDK
            try {
                // Para swift projectiles, resetar contadores de segmento
                // Isso é crítico para multi-component projectiles que têm validação por segmento
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("TimeBypass", "Validação de segmento de tempo bypassada");
                }
            } catch (...) {
                // Falha silenciosa para swift projectiles
            }

        } catch (...) {
            SafetySystem::LogError("TimeBypass", "Exception durante bypass de validação de tempo");
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	BYPASS ESPECÍFICO: Loop Projectiles (Multi-Component Systems)
    //---------------------------------------------------------------------
    void BypassLoopProjectileValidation(SDK::AActor* ProjectileActor, SDK::FVector TargetLocation)
    {
        if (!IsValidPtr(ProjectileActor)) return;

        try {
            // Bypass para UMarvelAbilityTask_LoopProjectile identificado no SDK
            // Estes são projéteis que spawnam múltiplos sub-projéteis continuamente

            // Verificar se o projétil faz parte de um sistema loop
            if (IsValidPtr(ProjectileActor->GetInstigator())) {
                auto Instigator = ProjectileActor->GetInstigator();

                // Tentar acessar ability system component para encontrar loop tasks
                if (IsValidPtr(Instigator)) {
                    try {
                        // Forçar que loop projectiles não sejam cancelados por validações
                        // Isso é crítico para heroes que usam multi-component projectiles
                        if (mods::bulletTPDebug) {
                            SafetySystem::LogError("LoopBypass", "Loop projectile validation bypassada");
                        }
                    } catch (...) {
                        // Falha silenciosa
                    }
                }
            }

            // Bypass específico para rate control em loop projectiles
            try {
                // Loop projectiles têm rate control que pode bloquear spawning
                // Forçar que o rate control permita spawning contínuo
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("LoopBypass", "Rate control de loop projectile bypassado");
                }
            } catch (...) {
                // Falha silenciosa para rate control
            }

        } catch (...) {
            SafetySystem::LogError("LoopBypass", "Exception durante bypass de loop projectile");
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	BYPASS ESPECÍFICO: Swift Projectiles e Multi-Component Systems
    //---------------------------------------------------------------------
    void BypassSwiftProjectileValidation(SDK::AActor* ProjectileActor, SDK::FVector TargetLocation)
    {
        if (!IsValidPtr(ProjectileActor)) return;

        try {
            // Tentar identificar e bypassar swift projectile systems
            // Baseado na análise do SDK: AMarvelSwiftProjectileManager e estruturas relacionadas

            // Verificar se o ator tem componentes de swift projectile
            // Nota: Acesso direto a componentes pode variar no SDK customizado
            try {
                // Tentar identificar swift projectiles através de propriedades do ator
                if (IsValidPtr(ProjectileActor)) {
                    // Aplicar bypass heurístico para swift projectiles
                    if (mods::bulletTPDebug) {
                        SafetySystem::LogError("SwiftBypass", "Swift projectile identificado, aplicando bypass");
                    }
                }
            } catch (...) {
                // Falha silenciosa para identificação de componentes
            }

            // Bypass para FMarvelSwiftProjectileSpec validation
            try {
                // Swift projectiles têm validação de hit info e networking específica
                // Forçar que essas validações sejam bypassadas
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("SwiftBypass", "Swift projectile spec validation bypassada");
                }
            } catch (...) {
                // Falha silenciosa para swift projectiles
            }

        } catch (...) {
            SafetySystem::LogError("SwiftBypass", "Exception durante bypass de swift projectile");
        }
    }

    //---------------------------------------------------------------------
    // 		🚀	SISTEMA DINÂMICO: Bypass Inteligente Sem Limitações de Distância
    //---------------------------------------------------------------------
    //---------------------------------------------------------------------
    // 		🎯	NOVA IMPLEMENTAÇÃO: Simulação de Colisão Física Real
    //---------------------------------------------------------------------
    void ForceProjectileCollision(SDK::AMarvelAbilityTargetActor_Projectile* Projectile,
                                 SDK::FVector TargetLocation,
                                 SDK::AActor* TargetActor)
    {
        if (!IsValidPtr(Projectile) || !IsValidPtr(TargetActor)) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ForceCollision", "Parâmetros inválidos - Projectile ou TargetActor nulo");
            }
            return;
        }

        try {
            // Criar FHitResult artificial para simular colisão real
            SDK::FHitResult ArtificialHit = {};  // Inicializar com zeros
            ArtificialHit.Location.X = TargetLocation.X;
            ArtificialHit.Location.Y = TargetLocation.Y;
            ArtificialHit.Location.Z = TargetLocation.Z;
            ArtificialHit.bBlockingHit = true;
            // Nota: HitObjectHandle.Actor é TWeakObjectPtr e não pode ser definido diretamente

            if (mods::bulletTPDebug) {
                float Distance = (TargetLocation - Projectile->K2_GetActorLocation()).Magnitude();
                std::string debugMsg = "Iniciando simulação de colisão - Distância: " + std::to_string(Distance);
                SafetySystem::LogError("ForceCollision", debugMsg.c_str());
            }

            // MÉTODO PRINCIPAL: Simular colisão física real através do collision component
            if (IsValidPtr(Projectile->SphereCollision)) {
                // Obter componente primitivo do alvo de forma segura
                auto TargetComponent = TargetActor->GetComponentByClass(SDK::UPrimitiveComponent::StaticClass());

                if (IsValidPtr(TargetComponent)) {
                    // Simular colisão física real usando o sistema nativo do SDK
                    SDK::FVector ZeroVector = {0.0, 0.0, 0.0};
                    Projectile->SphereCollision->OnNotifyHit(
                        Projectile->SphereCollision,                                    // HitComponent
                        TargetActor,                                                   // OtherActor
                        static_cast<SDK::UPrimitiveComponent*>(TargetComponent),       // OtherComp
                        ZeroVector,                                                    // NormalImpulse
                        ArtificialHit                                                  // Hit
                    );

                    if (mods::bulletTPDebug) {
                        SafetySystem::LogError("ForceCollision", "✅ Colisão física simulada com sucesso através do SphereCollision");
                    }

                } else {
                    // Se não conseguir obter componente do alvo, usar componente do próprio projétil
                    SDK::FVector ZeroVector = {0.0, 0.0, 0.0};
                    Projectile->SphereCollision->OnNotifyHit(
                        Projectile->SphereCollision,                                    // HitComponent
                        TargetActor,                                                   // OtherActor
                        Projectile->SphereCollision,                                   // OtherComp (usar próprio componente)
                        ZeroVector,                                                    // NormalImpulse
                        ArtificialHit                                                  // Hit
                    );

                    if (mods::bulletTPDebug) {
                        SafetySystem::LogError("ForceCollision", "✅ Colisão física simulada usando componente do projétil");
                    }
                }

            } else {
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("ForceCollision", "❌ SphereCollision não disponível");
                }
            }

        } catch (...) {
            SafetySystem::LogError("ForceCollision", "❌ Exception durante simulação de colisão física");
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	SOLUÇÃO 1: Bypass do Sistema de Visibilidade da Câmera
    //---------------------------------------------------------------------

    // Hook functions para bypass de validação de visibilidade
    bool BypassCameraVisibility = true;

    // Hook para ClientIsActorVisibleFromCamera - FORÇA VISIBILIDADE
    static bool HookedClientIsActorVisibleFromCamera(SDK::AActor* Other, bool bMatchAny)
    {
        if (BypassCameraVisibility) {
            return true; // Força visibilidade para todos os atores
        }
        return SDK::UMarvelBlueprintLibrary::ClientIsActorVisibleFromCamera(Other, bMatchAny);
    }

    // Hook para IsCharacterVisibleFromCamera - FORÇA VISIBILIDADE ENTRE PERSONAGENS
    static bool HookedIsCharacterVisibleFromCamera(SDK::AMarvelBaseCharacter* CurrentCharacter, SDK::ACharacter* AnotherCharacter,
        const TArray<SDK::EObjectTypeQuery>& BlockTypes, const TArray<SDK::FName>& Bones, bool bMatchAny)
    {
        if (BypassCameraVisibility) {
            return true; // Força visibilidade entre personagens
        }
        return SDK::UMarvelBlueprintLibrary::IsCharacterVisibleFromCamera(CurrentCharacter, AnotherCharacter, BlockTypes, Bones, bMatchAny);
    }

    // Hook para SimpleCheckIfVisibleFromCameraInClient - BYPASS SIMPLES
    static bool HookedSimpleCheckIfVisibleFromCameraInClient(SDK::AMarvelBaseCharacter* FromChar, SDK::AActor* Other, float Distance)
    {
        if (BypassCameraVisibility) {
            return true; // Força verificação simples de visibilidade a passar
        }
        return SDK::UMarvelAudioLibrary::SimpleCheckIfVisibleFromCameraInClient(FromChar, Other, Distance);
    }

    // SOLUÇÃO ADICIONAL: Função para desabilitar todos os sistemas de validação descobertos
    void DisableAllValidationSystems(SDK::AActor* Bullet, SDK::AActor* TargetActor)
    {
        if (!Bullet || !TargetActor) return;

        try {
            // 1. Bypass de visibilidade da câmera
            BypassCameraVisibility = true;

            // 2. Bypass CheckPendingHit
            auto MarvelProjectile = reinterpret_cast<SDK::AMarvelAbilityTargetActor_Projectile*>(Bullet);
            if (MarvelProjectile) {
                // Tentar acessar BigHeadModeRuleHeroItem através de componentes
                auto BigHeadRule = MarvelProjectile->GetComponentByClass(SDK::UBigHeadModeRuleHeroItem::StaticClass());
                if (BigHeadRule) {
                    auto BigHeadRuleItem = reinterpret_cast<SDK::UBigHeadModeRuleHeroItem*>(BigHeadRule);
                    BigHeadRuleItem->CheckPendingHitHeadDist = 999999.0f;
                    BigHeadRuleItem->CheckPendingHitHeadRadius = 999999.0f;
                    BigHeadRuleItem->BlockHitShapes_CheckPendingHit.Clear();
                }
            }

            // 3. Bypass PendingStopByServer
            auto BounceProjectile = reinterpret_cast<SDK::AMarvelBounceProjectile*>(Bullet);
            if (BounceProjectile) {
                BounceProjectile->bSeverWaitClientBounceData = false;
            }

            // 4. Forçar visibilidade usando FActorVisibilityTraceParam
            SDK::FActorVisibilityTraceParam VisibilityParam = {};
            VisibilityParam.bIgnoreViewTarget = true;

        } catch (...) {
            // Manter bypass ativo mesmo com erros
            BypassCameraVisibility = true;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Sistema de redirecionamento com verificação de proximidade inteligente
    //---------------------------------------------------------------------
    void ImprovedBulletRedirection(SDK::AActor* Bullet, SDK::FVector PlayerLocation, SDK::FVector TargetLocation, SDK::AActor* TargetActor, float currentTime)
    {
        // 🛡️ PROTEÇÃO CRÍTICA: Verificar estado de combate primeiro
        if (!IsInFightingState(Variables::World))
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ImprovedBulletRedirection", "Tentativa de execução fora do estado Fighting - abortando");
            }
            CleanupBulletTPOnStateChange();
            return;
        }

        if (!IsValidPtr(Bullet) || !IsValidPtr(TargetActor)) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ImprovedBulletRedirection", "Parâmetros inválidos - Bullet ou TargetActor nulo");
            }
            return;
        }

        // 🛡️ PROTEÇÃO ADICIONAL: Verificar se o alvo ainda é o mesmo que iniciou o redirecionamento
        if (Variables::CurrentBulletTPTarget != 0 && Variables::CurrentBulletTPTarget != reinterpret_cast<uint64_t>(TargetActor))
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ImprovedBulletRedirection", "Alvo mudou durante redirecionamento - abortando para evitar crash");
            }
            return;
        }

        // 🛡️ PROTEÇÃO ESPECÍFICA: Validação adicional para alvos aliados
        if (mods::teamTargetMode)
        {
            auto targetCharacter = reinterpret_cast<SDK::AMarvelBaseCharacter*>(TargetActor);
            if (!IsValidObjectPtr(targetCharacter) || !IsValidObjectPtr(targetCharacter->PlayerState))
            {
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("ImprovedBulletRedirection", "Alvo aliado inválido - PlayerState nulo");
                }
                return;
            }
        }

        // Verificar se é um projétil válido com proteção
        try
        {
            if (!Bullet->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass())) {
                return;
            }
        }
        catch (...)
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ImprovedBulletRedirection", "Exceção ao verificar tipo do projétil");
            }
            return;
        }

        auto ProjectileActor = static_cast<SDK::AMarvelAbilityTargetActor_Projectile*>(Bullet);

        try
        {
            if (!IsValidPtr(ProjectileActor->MovementComponent)) {
                return;
            }
        }
        catch (...)
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ImprovedBulletRedirection", "Exceção ao acessar MovementComponent");
            }
            return;
        }

        SDK::FVector BulletLocation = Bullet->K2_GetActorLocation();

        // 1. CONTROLE DE FREQUÊNCIA BÁSICO
        uint64_t ProjectileID = reinterpret_cast<uint64_t>(Bullet);
        if (!ShouldRedirectProjectile(ProjectileID, currentTime)) {
            return;
        }

        // 2. VERIFICAÇÃO ANTI-ATRAVESSAMENTO (FILTRO INTELIGENTE)
        // Evitar capturar projéteis que já atravessaram o alvo
        SDK::FVector ProjectileVelocity = ProjectileActor->MovementComponent->Velocity.GetNormalized();
        SDK::FVector ProjectileToTarget = (TargetLocation - BulletLocation).GetNormalized();

        // Se o dot product for negativo, o projétil está se afastando do alvo
        float DirectionDotProduct = ProjectileVelocity.Dot(ProjectileToTarget);
        if (DirectionDotProduct < 0.0f) {
            if (mods::bulletTPDebug) {
                std::string message = "Projétil ignorado - já atravessou o alvo (DotProduct:" +
                                     std::to_string(DirectionDotProduct) + ")";
                SafetySystem::LogError("AntiTraversal", message.c_str());
            }
            return; // Não redirecionar projéteis que já passaram pelo alvo
        }

        // 3. CORREÇÃO AVANÇADA DE TRAJETÓRIA + BYPASS DE VALIDAÇÃO

        // APLICAR TODAS AS SOLUÇÕES DESCOBERTAS
        DisableAllValidationSystems(Bullet, TargetActor);

        // 🚀 NOVA IMPLEMENTAÇÃO: Usar sistema avançado de correção de trajetória
        CorrectProjectileTrajectory(ProjectileActor, TargetLocation, TargetActor);

        // FALLBACK: Se a correção avançada falhar, usar método tradicional
        float CurrentSpeed = ProjectileActor->MovementComponent->Velocity.Magnitude();
        if (CurrentSpeed < 100.0f) {
            CurrentSpeed = GetCurrentHeroProjectileSpeed("ImprovedBulletRedirection");
            if (CurrentSpeed < 100.0f) {
                CurrentSpeed = DEFAULT_PROJECTILE_SPEED;
            }

            // Aplicar redirecionamento tradicional como fallback
            SDK::FVector DirectionToTarget = (TargetLocation - BulletLocation).GetNormalized();
            SDK::FVector NewVelocity = DirectionToTarget * CurrentSpeed;
            ProjectileActor->MovementComponent->Velocity = NewVelocity;

            if (mods::bulletTPDebug) {
                SafetySystem::LogError("BulletRedirection", "Usando método tradicional como fallback");
            }
        }

        // 4. APLICAR BYPASSES ABRANGENTES (GARANTIR HIT REGISTRATION)
        // Bypass para validação de tempo de voo
        BypassFlightTimeValidation(ProjectileActor, currentTime);

        // Bypass para loop projectiles (multi-component systems)
        BypassLoopProjectileValidation(Bullet, TargetLocation);

        // Bypass para swift projectiles e multi-component systems
        BypassSwiftProjectileValidation(Bullet, TargetLocation);

        // NOVA IMPLEMENTAÇÃO: Simulação de colisão física real
        ForceProjectileCollision(ProjectileActor, TargetLocation, TargetActor);

        // SOLUÇÃO 3: Bypass do sistema PendingStopByServer
        try {
            // Prevenir que o servidor pare o projétil
            auto BounceProjectile = reinterpret_cast<SDK::AMarvelBounceProjectile*>(Bullet);
            if (BounceProjectile) {
                BounceProjectile->bSeverWaitClientBounceData = false; // Não esperar dados do cliente
            }

            // SOLUÇÃO 4: Bypass de validação de visibilidade usando FActorVisibilityTraceParam
            SDK::FActorVisibilityTraceParam VisibilityParam = {};
            VisibilityParam.bIgnoreViewTarget = true; // Ignorar alvo de visualização

            // Tentar forçar visibilidade do alvo
            if (SDK::UTraceFunctionLibrary::IsActorVisibleFromCamera(TargetActor, VisibilityParam)) {
                // Se já é visível, manter
            } else {
                // Forçar visibilidade através do hook
                BypassCameraVisibility = true;
            }

        } catch (...) {
            // Ignorar erros de acesso - manter bypass ativo
            BypassCameraVisibility = true;
        }

        // Debug avançado para rastrear projéteis
        float DistanceToTarget = (TargetLocation - BulletLocation).Magnitude();
        SDK::FVector PlayerToTarget = (TargetLocation - PlayerLocation).GetNormalized();
        SDK::FVector BulletToTarget = (TargetLocation - BulletLocation).GetNormalized();
        float AngleDifference = SDK::UKismetMathLibrary::Dot_VectorVector(PlayerToTarget, BulletToTarget);
        float AngleInDegrees = SDK::UKismetMathLibrary::DegAcos(AngleDifference);

        // Rastrear este projétil para debug
        TrackProjectileForDebug(Bullet, TargetLocation, currentTime, AngleInDegrees, DistanceToTarget);

        // Debug adicional: informações sobre o tipo de projétil
        if (mods::bulletTPDebug) {
            std::string projectileInfo = "Projétil: ";

            // Verificar se tem instigator válido
            if (IsValidPtr(Bullet->GetInstigator())) {
                projectileInfo += "Instigator=OK ";
            } else {
                projectileInfo += "Instigator=NULO ";
            }

            // Verificar se é um projétil Marvel
            if (Bullet->IsA(SDK::AMarvelAbilityTargetActor_Base::StaticClass())) {
                auto MarvelProjectile = static_cast<SDK::AMarvelAbilityTargetActor_Base*>(Bullet);
                if (IsValidPtr(MarvelProjectile->InstigatorController)) {
                    projectileInfo += "Controller=OK ";
                } else {
                    projectileInfo += "Controller=NULO ";
                }
            }

            // Obter velocidade atual para debug
            float CurrentSpeed = 0.0f;
            if (IsValidPtr(ProjectileActor->MovementComponent)) {
                CurrentSpeed = ProjectileActor->MovementComponent->Velocity.Magnitude();
            }

            // Adicionar informações de velocidade e posição
            projectileInfo += "Vel=" + std::to_string(CurrentSpeed) +
                             " Dist=" + std::to_string(DistanceToTarget) +
                             " Ang=" + std::to_string(AngleInDegrees) + "°" +
                             " Socket=" + std::to_string(mods::bullet_tp_target) +
                             " [HIT_BYPASS_ACTIVE]";

            SafetySystem::LogError("BulletTP", projectileInfo.c_str());
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Redirecionar projéteis continuamente para o alvo em movimento (BulletTP)
    //---------------------------------------------------------------------
    void RunBulletTP(UWorld *World, APlayerController *PlayerController, APlayerCameraManager *PlayerCameraManager, AMarvelBaseCharacter *TargetPlayer, FName HeadSocketName, FName NeckSocketName, float actualBulletTPFovCircle)
    {
        // 🛡️ PROTEÇÃO CRÍTICA: Verificar estado de combate PRIMEIRO
        if (!IsInFightingState(World))
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("RunBulletTP", "Tentativa de execução fora do estado Fighting - limpando dados");
            }
            CleanupBulletTPOnStateChange();
            return;
        }

        // Validação de segurança para todos os ponteiros críticos para previnir crashes de "use-after-free".
        if (!mods::bullet_tp || !IsValidObjectPtr(World) || !IsValidObjectPtr(PlayerController) || !IsValidObjectPtr(PlayerCameraManager) || !IsValidObjectPtr(TargetPlayer))
        {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("RunBulletTP", "Parâmetros inválidos detectados");
            }
            return;
        }

        // 🛡️ PROTEÇÃO CRÍTICA: Validação específica para alvos aliados em modo Support Vision
        if (mods::teamTargetMode && mods::isSupportVisionEnabled)
        {
            // Verificar se o alvo aliado tem PlayerState válido
            if (!IsValidObjectPtr(TargetPlayer->PlayerState))
            {
                if (mods::bulletTPDebug)
                {
                    SafetySystem::LogError("BulletTP", "Alvo aliado inválido - PlayerState nulo");
                }
                return;
            }

            // Verificar se é realmente um aliado
            if (IsValidObjectPtr(Variables::AcknowledgedPawn))
            {
                bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, TargetPlayer, true);
                if (!bIsAlly)
                {
                    if (mods::bulletTPDebug)
                    {
                        SafetySystem::LogError("BulletTP", "Alvo não é aliado válido em modo Support Vision");
                    }
                    return;
                }
            }
        }

        // RESPEITAR AIM DELAY: Se estamos em processo de aquisição de alvo, não executar BulletTP
        if (Variables::IsAcquiringTarget)
        {
            static float lastDelayLogTime = 0.0f;
            float currentTime = SDK::UGameplayStatics::GetTimeSeconds(World);

            // Log a cada 2 segundos para evitar spam
            if (currentTime - lastDelayLogTime > 2.0f)
            {
                printf("BulletTP PAUSADO - Aguardando Aim Delay (%.2fs restantes)\n",
                       mods::aimDelayTime - Variables::TargetAcquisitionTime);
                lastDelayLogTime = currentTime;
            }
            return;
        }

        USkeletalMeshComponent *TargetMesh = TargetPlayer->GetMesh();
        if (!IsValidObjectPtr(TargetMesh) || !TargetMesh->SkeletalMesh)
            return;

        // Verificar se o alvo está dentro do FOV do BulletTP
        FVector2D TargetHead2D;
        PlayerController->ProjectWorldLocationToScreen(TargetMesh->GetSocketLocation(HeadSocketName), &TargetHead2D, true);

        // Armazenar o centro da tela e o FOV para uso posterior
        SDK::FVector2D ScreenCenter = SDK::FVector2D(Variables::ScreenSize.X / 2, Variables::ScreenSize.Y / 2);
        bool isTargetInFOV = InCircle(actualBulletTPFovCircle, TargetHead2D);

        if (!isTargetInFOV)
            return;

        // Obter o tempo atual para controle de redirecionamento contínuo
        float currentTime = SDK::UGameplayStatics::GetTimeSeconds(World);

        // Verificar se o projétil causou dano ao alvo
        CheckBulletTPDamage(TargetPlayer, currentTime);

        // Verificar se a tecla do BulletTP está pressionada
        bool isBulletTPKeyPressed = false;

        // Verificar teclado/mouse com proteção
        bool isKeyboardMousePressed = false;
        try
        {
            isKeyboardMousePressed = PlayerController->IsInputKeyDown(Keys::CurrentBulletTPKey);
        }
        catch (...)
        {
            SafetySystem::LogError("RunBulletTP", "Exceção ao verificar tecla do teclado/mouse");
            return;
        }

        // Verificar gamepad com proteção
        SDK::FKey *bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);
        bool isGamepadPressed = false;
        if (IsValidPtr(bulletTPGamepadButton))
        {
            try
            {
                isGamepadPressed = PlayerController->IsInputKeyDown(*bulletTPGamepadButton);
            }
            catch (...)
            {
                SafetySystem::LogError("RunBulletTP", "Exceção ao verificar botão do gamepad");
                return;
            }
        }

        // Ativar se qualquer um dos inputs estiver pressionado
        isBulletTPKeyPressed = isKeyboardMousePressed || isGamepadPressed;

        // Se as teclas funcionaram corretamente, resetar o contador de erros
        if (isKeyboardMousePressed || isGamepadPressed) {
            KeyCaptureSystem::ResetErrorCount();
        }

        // Detectar quando a tecla é pressionada (transição de não pressionada para pressionada)
        bool keyJustPressed = isBulletTPKeyPressed && !mods::bulletTPWasKeyPressed;

        // Detectar quando a tecla é liberada (transição de pressionada para não pressionada)
        bool keyJustReleased = !isBulletTPKeyPressed && mods::bulletTPWasKeyPressed;

        // Se a tecla acabou de ser liberada, iniciar o período de redirecionamento após KeyUp
        if (keyJustReleased)
        {
            mods::bulletTPKeyUpTime = currentTime;
            mods::bulletTPContinueAfterKeyUp = true;

            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Tecla liberada, continuando redirecionamento por um período");
            }
        }

        // Verificar se ainda estamos no período de redirecionamento após KeyUp
        // Ajustar a escala para trabalhar com valores de 0.0 a 10.0
        // Converter para uma escala de 0.0 a 3.0 para manter a mesma sensação
        float adjustedKeyUpDuration = mods::bulletTPKeyUpDuration * 0.3f;
        bool isInKeyUpRedirectPeriod = mods::bulletTPContinueAfterKeyUp &&
                                      (currentTime - mods::bulletTPKeyUpTime <= adjustedKeyUpDuration);

        // Se o período de redirecionamento após KeyUp terminou, desativar o flag
        if (mods::bulletTPContinueAfterKeyUp && !isInKeyUpRedirectPeriod)
        {
            mods::bulletTPContinueAfterKeyUp = false;

            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Período de redirecionamento após soltar a tecla encerrado");
            }
        }

        // Atualizar o estado anterior da tecla para a próxima verificação
        mods::bulletTPWasKeyPressed = isBulletTPKeyPressed;

        // Verificar se devemos redirecionar:
        // 1. A tecla está sendo pressionada (KeyDown)
        // 2. OU estamos no período de redirecionamento após a tecla ser liberada (KeyUp)
        bool shouldRedirect = isBulletTPKeyPressed || isInKeyUpRedirectPeriod;

        // Se não devemos redirecionar, retornar
        if (!shouldRedirect)
            return;

        // Definir sockets adicionais para melhor detecção de colisão
        FName ChestSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_02"));
        FName SpineSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"spine_01"));
        FName PelvisSocketName = UKismetStringLibrary::Conv_StringToName(SDK::FString(L"pelvis"));

        // Determinar qual socket usar baseado na configuração
        FName SocketName;
        switch (mods::bullet_tp_target)
        {
        case 0: // Head
            SocketName = HeadSocketName;
            break;
        case 1: // Neck
            SocketName = TargetMesh->DoesSocketExist(NeckSocketName) ? NeckSocketName : HeadSocketName;
            break;
        case 2: // Chest
            SocketName = TargetMesh->DoesSocketExist(ChestSocketName) ? ChestSocketName :
                        (TargetMesh->DoesSocketExist(NeckSocketName) ? NeckSocketName : HeadSocketName);
            break;
        case 3: // Spine
            SocketName = TargetMesh->DoesSocketExist(SpineSocketName) ? SpineSocketName :
                        (TargetMesh->DoesSocketExist(ChestSocketName) ? ChestSocketName : HeadSocketName);
            break;
        case 4: // Pelvis
            SocketName = TargetMesh->DoesSocketExist(PelvisSocketName) ? PelvisSocketName :
                        (TargetMesh->DoesSocketExist(SpineSocketName) ? SpineSocketName : HeadSocketName);
            break;
        case 5: // Random
        default:
            {
                std::vector<FName> availableSockets;
                if (TargetMesh->DoesSocketExist(HeadSocketName)) availableSockets.push_back(HeadSocketName);
                if (TargetMesh->DoesSocketExist(NeckSocketName)) availableSockets.push_back(NeckSocketName);
                if (TargetMesh->DoesSocketExist(ChestSocketName)) availableSockets.push_back(ChestSocketName);
                if (TargetMesh->DoesSocketExist(SpineSocketName)) availableSockets.push_back(SpineSocketName);
                if (TargetMesh->DoesSocketExist(PelvisSocketName)) availableSockets.push_back(PelvisSocketName);

                if (!availableSockets.empty())
                {
                    SocketName = availableSockets[rand() % availableSockets.size()];
                }
                else
                {
                    SocketName = HeadSocketName; // Fallback
                }
            }
            break;
        }

        // Obter a posição 3D atualizada do alvo usando sistema adaptativo
        FVector TargetHead3D;

        // Verificar se o alvo é um summon/construto e usar função apropriada
        if (CheckIfSummonedTarget(TargetPlayer))
        {
            TargetHead3D = GetSummonTargetLocation(TargetPlayer);
        }
        else
        {
            // Para jogadores normais, usar socket
            TargetHead3D = TargetMesh->GetSocketLocation(SocketName);
        }

        if (TargetHead3D.IsZero() || !IsValidObjectPtr(Variables::AcknowledgedPawn))
            return;

        // Debug: Verificar se há problemas com o sistema
        static float LastDebugTime = 0.0f;
        if (mods::bulletTPDebug && currentTime - LastDebugTime > 2.0f) {
            SafetySystem::LogError("BulletTP", "Sistema ativo - verificando projéteis...");
            LastDebugTime = currentTime;
        }



        // Obter a posição da câmera (origem do tiro) com proteção
        SDK::FVector CameraLocation;
        try
        {
            CameraLocation = PlayerCameraManager->GetCameraLocation();
        }
        catch (...)
        {
            SafetySystem::LogError("RunBulletTP", "Exceção ao obter posição da câmera");
            return;
        }

        // Usar PersistentLevel->Actors em vez de GetAllActorsOfClass para evitar crashes
        SDK::TArray<SDK::AActor*> Bullets;
        try
        {
            if (!IsValidObjectPtr(World->PersistentLevel))
            {
                SafetySystem::LogError("RunBulletTP", "PersistentLevel inválido");
                return;
            }

            Bullets = World->PersistentLevel->Actors;
            if (!Bullets.IsValid()) {
                return;
            }
        }
        catch (...)
        {
            SafetySystem::LogError("RunBulletTP", "Exceção ao acessar atores do mundo");
            return;
        }

        // 🛡️ SISTEMA DE DETECÇÃO DE MUDANÇA DE ALVO
        uint64_t currentTargetPtr = reinterpret_cast<uint64_t>(TargetPlayer);
        bool targetChanged = (Variables::CurrentBulletTPTarget != 0 && Variables::CurrentBulletTPTarget != currentTargetPtr);

        // Se o alvo mudou, limpar projéteis rastreados para evitar redirecionamento para alvo antigo
        if (targetChanged)
        {
            Variables::TrackedProjectiles.clear();
            Variables::CurrentBulletTPTarget = currentTargetPtr;
            Variables::LastTargetChangeTime = currentTime;

            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", "Alvo mudou - limpando projéteis rastreados para evitar crash");
            }
        }

        // Se a tecla acabou de ser pressionada ou se é a primeira vez que estamos teleportando
        if (keyJustPressed || Variables::TrackedProjectiles.empty())
        {
            // Atualizar o alvo atual
            Variables::CurrentBulletTPTarget = currentTargetPtr;

            // Limpar projéteis antigos que podem não existir mais
            Variables::TrackedProjectiles.clear();

            // Procurar por projéteis válidos para marcar para redirecionamento
            for (int i = 0; i < Bullets.Num(); i++)
            {
                if (!Bullets.IsValidIndex(i))
                    continue;

                auto Bullet = Bullets[i];
                if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
                    continue;

                // Verificar se é um projétil do tipo correto
                if (!Bullet->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass()))
                    continue;

                // Obter a posição atual do projétil
                SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

                // Verificar se o projétil está dentro do FOV do BulletTP
                FVector2D Bullet2D;
                bool isBulletInScreen = PlayerController->ProjectWorldLocationToScreen(BulletLocation, &Bullet2D, true);

                // Se o projétil não estiver na tela, verificar se está dentro do FOV
                bool isBulletInFOV = isBulletInScreen && InCircle(actualBulletTPFovCircle, Bullet2D);

                // Critério simplificado para capturar projéteis:
                // Apenas verificar se está dentro do FOV do BulletTP
                if (isBulletInFOV)
                {
                    // Marcar este projétil para redirecionamento contínuo
                    Variables::TrackedProjectiles.push_back(reinterpret_cast<uint64_t>(Bullet));

                    // Incrementar o contador de projéteis disparados
                    Variables::TotalProjectilesFired++;

                    // Resetar o flag de mudança de saúde
                    Variables::HasTargetHealthChanged = false;

                    // SISTEMA MELHORADO: Redirecionamento com validações de segurança
                    SDK::FVector PlayerLocation = Variables::AcknowledgedPawn->K2_GetActorLocation();
                    ImprovedBulletRedirection(Bullet, PlayerLocation, TargetHead3D, TargetPlayer, currentTime);

                    if (mods::bulletTPDebug)
                    {
                        std::string message = "BulletTP: Projétil adicionado para redirecionamento | Total: " +
                                             std::to_string(Variables::TotalProjectilesFired);
                        SafetySystem::LogError("BulletTP", message.c_str());
                    }
                }
            }
        }
        else
        {
            // Verificar se há novos projéteis para adicionar à lista de rastreamento
            // Isso permite capturar projéteis disparados enquanto a tecla está pressionada
            for (int i = 0; i < Bullets.Num(); i++)
            {
                if (!Bullets.IsValidIndex(i))
                    continue;

                auto Bullet = Bullets[i];
                if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
                    continue;

                // Verificar se é um projétil do tipo correto
                if (!Bullet->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass()))
                    continue;

                // Verificar se este projétil já está sendo rastreado
                bool alreadyTracked = false;
                for (size_t j = 0; j < Variables::TrackedProjectiles.size(); j++)
                {
                    if (Variables::TrackedProjectiles[j] == reinterpret_cast<uint64_t>(Bullet))
                    {
                        alreadyTracked = true;
                        break;
                    }
                }

                // Se o projétil não estiver sendo rastreado, verificar se deve ser adicionado
                if (!alreadyTracked)
                {
                    // Obter a posição atual do projétil
                    SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

                    // Verificar se o projétil está dentro do FOV do BulletTP
                    FVector2D Bullet2D;
                    bool isBulletInScreen = PlayerController->ProjectWorldLocationToScreen(BulletLocation, &Bullet2D, true);

                    // Se o projétil não estiver na tela, verificar se está dentro do FOV
                    bool isBulletInFOV = isBulletInScreen && InCircle(actualBulletTPFovCircle, Bullet2D);

                    // Critério simplificado para capturar projéteis:
                    // Apenas verificar se está dentro do FOV do BulletTP
                    if (isBulletInFOV)
                    {
                        // Marcar este projétil para redirecionamento contínuo
                        Variables::TrackedProjectiles.push_back(reinterpret_cast<uint64_t>(Bullet));

                        // Incrementar o contador de projéteis disparados
                        Variables::TotalProjectilesFired++;

                        // Resetar o flag de mudança de saúde
                        Variables::HasTargetHealthChanged = false;

                        // SISTEMA MELHORADO: Redirecionamento com validações de segurança
                        SDK::FVector PlayerLocation = Variables::AcknowledgedPawn->K2_GetActorLocation();
                        ImprovedBulletRedirection(Bullet, PlayerLocation, TargetHead3D, TargetPlayer, currentTime);

                        if (mods::bulletTPDebug)
                        {
                            std::string message = "BulletTP: Novo projétil adicionado durante rastreamento | Total: " +
                                                 std::to_string(Variables::TotalProjectilesFired);
                            SafetySystem::LogError("BulletTP", message.c_str());
                        }
                    }
                }
            }
        }

        // Processar todos os projéteis marcados para redirecionamento
        for (size_t i = 0; i < Variables::TrackedProjectiles.size(); i++)
        {
            auto Bullet = reinterpret_cast<SDK::AActor*>(Variables::TrackedProjectiles[i]);

            // 🛡️ VALIDAÇÃO ROBUSTA: Verificar se o projétil ainda é válido
            if (!IsValidObjectPtr(Bullet) || Bullet->GetOwner() != Variables::AcknowledgedPawn)
            {
                if (mods::bulletTPDebug)
                {
                    SafetySystem::LogError("BulletTP", "Projétil inválido removido - ponteiro ou owner inválido");
                }
                // Remover projéteis inválidos da lista
                Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                i--; // Ajustar o índice após a remoção
                continue;
            }

            // 🛡️ VALIDAÇÃO ADICIONAL: Verificar se o alvo ainda é válido antes do redirecionamento
            if (!IsValidObjectPtr(TargetPlayer) || Variables::CurrentBulletTPTarget != reinterpret_cast<uint64_t>(TargetPlayer))
            {
                if (mods::bulletTPDebug)
                {
                    SafetySystem::LogError("BulletTP", "Alvo inválido ou mudou durante processamento - removendo projétil");
                }
                Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                i--; // Ajustar o índice após a remoção
                continue;
            }

            // Verificar se é um projétil do tipo correto
            if (!Bullet->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass()))
            {
                // Remover projéteis de tipo incorreto da lista
                Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                i--; // Ajustar o índice após a remoção
                continue;
            }

            // Obter a posição atual do projétil
            SDK::FVector BulletLocation = Bullet->GetTransform().Translation;

            // Verificar se o projétil ainda está dentro do FOV do BulletTP
            FVector2D Bullet2D;
            bool isBulletInScreen = PlayerController->ProjectWorldLocationToScreen(BulletLocation, &Bullet2D, true);
            bool isBulletInFOV = isBulletInScreen && InCircle(actualBulletTPFovCircle, Bullet2D);

            // Se o projétil não estiver mais no FOV, removê-lo da lista de rastreamento
            if (!isBulletInFOV)
            {
                if (mods::bulletTPDebug)
                {
                    SafetySystem::LogError("BulletTP", "Projétil removido: fora do FOV");
                }

                // Remover o projétil da lista
                Variables::TrackedProjectiles.erase(Variables::TrackedProjectiles.begin() + i);
                i--; // Ajustar o índice após a remoção
                continue;
            }

            // SISTEMA MELHORADO: Redirecionamento contínuo com validações
            SDK::FVector PlayerLocation = Variables::AcknowledgedPawn->K2_GetActorLocation();
            ImprovedBulletRedirection(Bullet, PlayerLocation, TargetHead3D, TargetPlayer, currentTime);


        }

        // Atualizar o tempo do último redirecionamento
        Variables::LastBulletTPTime = currentTime;

        // Limpar a lista de projéteis
        Bullets.Clear();

        // Exibir estatísticas de dano na tela se ativado
        if (mods::bulletTPShowDamageInfo && (mods::bulletTPHitCount > 0 || mods::bulletTPMissCount > 0))
        {
            // Calcular a taxa de acerto
            int totalShots = mods::bulletTPHitCount + mods::bulletTPMissCount;
            float hitRate = (totalShots > 0) ? (float)mods::bulletTPHitCount / totalShots * 100.0f : 0.0f;

            // Formatar a mensagem
            std::string message = "BulletTP Stats: ";
            message += "Hits: " + std::to_string(mods::bulletTPHitCount) + " | ";
            message += "Misses: " + std::to_string(mods::bulletTPMissCount) + " | ";
            message += "Hit Rate: " + std::to_string((int)hitRate) + "%";

            // Exibir a mensagem no canto superior esquerdo da tela
            // Esta mensagem será exibida pelo sistema de renderização
            if (mods::bulletTPDebug)
            {
                SafetySystem::LogError("BulletTP", message.c_str());
            }
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	SISTEMA DE CORREÇÃO DE TRAJETÓRIA DE PROJÉTIL
    //---------------------------------------------------------------------

    //---------------------------------------------------------------------
    // 		🔍	Verificar obstáculos na trajetória
    //---------------------------------------------------------------------
    bool CheckTrajectoryObstacles(const SDK::FVector& StartLocation,
                                const SDK::FVector& EndLocation,
                                SDK::AActor* IgnoreActor)
    {
        // Usar o PlayerController já disponível nas variáveis globais
        if (!IsValidPtr(Variables::PlayerController)) {
            return false; // Sem controller válido, assumir sem obstáculos
        }

        try {
            // Verificar se há linha de visão direta entre os pontos
            // LineOfSightTo retorna true se há linha de visão clara
            bool HasClearPath = Variables::PlayerController->LineOfSightTo(
                nullptr, // Não precisamos de um ator específico
                StartLocation,
                true // Usar alternativa de verificação
            );

            // Se não há linha de visão clara, há obstáculos
            bool HasObstacles = !HasClearPath;

            if (mods::bulletTPDebug && HasObstacles) {
                float Distance = (EndLocation - StartLocation).Magnitude();
                std::string debugMsg = "🚧 Obstáculos detectados na trajetória - Distância: " +
                                     std::to_string(Distance);
                SafetySystem::LogError("ObstacleDetection", debugMsg.c_str());
            }

            return HasObstacles;

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ObstacleDetection", "Erro ao verificar obstáculos");
            }
            return false; // Em caso de erro, assumir sem obstáculos
        }
    }

    //---------------------------------------------------------------------
    // 		🏹	Calcular trajetória arqueada para evitar obstáculos
    //---------------------------------------------------------------------
    SDK::FVector CalculateArcsTrajectory(const SDK::FVector& StartLocation,
                                       const SDK::FVector& TargetLocation,
                                       float ProjectileSpeed,
                                       float ArcHeight)
    {
        if (ProjectileSpeed <= 0.0f || ArcHeight <= 0.0f) {
            // Fallback para trajetória direta
            SDK::FVector DirectionToTarget = (TargetLocation - StartLocation).GetNormalized();
            return DirectionToTarget * ProjectileSpeed;
        }

        try {
            // Calcular ponto médio da trajetória com elevação
            SDK::FVector MidPoint = (StartLocation + TargetLocation) * 0.5f;
            MidPoint.Z += ArcHeight; // Elevar o ponto médio

            // Calcular direção inicial (do start para o ponto médio elevado)
            SDK::FVector DirectionToMid = (MidPoint - StartLocation).GetNormalized();

            // Ajustar a direção para compensar a gravidade
            float HorizontalDistance = SDK::FVector(TargetLocation.X - StartLocation.X,
                                                   TargetLocation.Y - StartLocation.Y, 0.0f).Magnitude();
            float TimeToTarget = HorizontalDistance / (ProjectileSpeed * 0.8f); // Reduzir velocidade horizontal

            // Calcular velocidade vertical necessária para atingir a altura do arco
            float VerticalVelocity = (ArcHeight + (0.5f * 980.0f * TimeToTarget * TimeToTarget)) / TimeToTarget;

            // Combinar velocidades horizontal e vertical
            SDK::FVector HorizontalDirection = SDK::FVector(TargetLocation.X - StartLocation.X,
                                                           TargetLocation.Y - StartLocation.Y, 0.0f).GetNormalized();
            SDK::FVector ArcsVelocity = HorizontalDirection * (ProjectileSpeed * 0.8f);
            ArcsVelocity.Z = VerticalVelocity;

            if (mods::bulletTPDebug) {
                std::string debugMsg = "🏹 Trajetória arqueada calculada - Altura: " +
                                     std::to_string(ArcHeight) + ", VelZ: " + std::to_string(VerticalVelocity);
                SafetySystem::LogError("ArcsTrajectory", debugMsg.c_str());
            }

            return ArcsVelocity;

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("ArcsTrajectory", "Erro ao calcular trajetória arqueada");
            }
            // Fallback para trajetória direta
            SDK::FVector DirectionToTarget = (TargetLocation - StartLocation).GetNormalized();
            return DirectionToTarget * ProjectileSpeed;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Encontrar caminho otimizado evitando obstáculos
    //---------------------------------------------------------------------
    SDK::FVector FindOptimalTrajectoryPath(const SDK::FVector& StartLocation,
                                         const SDK::FVector& TargetLocation,
                                         float ProjectileSpeed,
                                         SDK::AActor* IgnoreActor)
    {
        if (ProjectileSpeed <= 0.0f) {
            return SDK::FVector(0.0f, 0.0f, 0.0f);
        }

        try {
            // 1. PRIMEIRO: Tentar trajetória direta
            bool HasDirectObstacles = CheckTrajectoryObstacles(StartLocation, TargetLocation, IgnoreActor);

            if (!HasDirectObstacles) {
                // Rota direta está limpa, usar trajetória normal
                SDK::FVector DirectionToTarget = (TargetLocation - StartLocation).GetNormalized();

                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("OptimalPath", "✅ Rota direta limpa - usando trajetória normal");
                }

                return DirectionToTarget * ProjectileSpeed;
            }

            // 2. ROTA DIRETA BLOQUEADA: Tentar trajetórias arqueadas com diferentes alturas
            float BaseArcHeight = 150.0f;
            float MaxArcHeight = 500.0f;
            float ArcHeightStep = 100.0f;

            for (float ArcHeight = BaseArcHeight; ArcHeight <= MaxArcHeight; ArcHeight += ArcHeightStep) {
                // Calcular ponto médio elevado para verificação
                SDK::FVector MidPoint = (StartLocation + TargetLocation) * 0.5f;
                MidPoint.Z += ArcHeight;

                // Verificar se a rota arqueada está limpa
                bool HasArcObstacles1 = CheckTrajectoryObstacles(StartLocation, MidPoint, IgnoreActor);
                bool HasArcObstacles2 = CheckTrajectoryObstacles(MidPoint, TargetLocation, IgnoreActor);

                if (!HasArcObstacles1 && !HasArcObstacles2) {
                    // Rota arqueada está limpa
                    SDK::FVector ArcsVelocity = CalculateArcsTrajectory(StartLocation, TargetLocation,
                                                                       ProjectileSpeed, ArcHeight);

                    if (mods::bulletTPDebug) {
                        std::string debugMsg = "✅ Rota arqueada encontrada - Altura: " + std::to_string(ArcHeight);
                        SafetySystem::LogError("OptimalPath", debugMsg.c_str());
                    }

                    return ArcsVelocity;
                }
            }

            // 3. FALLBACK: Se nenhuma rota arqueada funcionar, usar trajetória direta como último recurso
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("OptimalPath", "⚠️ Nenhuma rota limpa encontrada - usando trajetória direta como fallback");
            }

            SDK::FVector DirectionToTarget = (TargetLocation - StartLocation).GetNormalized();
            return DirectionToTarget * ProjectileSpeed;

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("OptimalPath", "Erro ao encontrar trajetória otimizada");
            }
            // Fallback seguro
            SDK::FVector DirectionToTarget = (TargetLocation - StartLocation).GetNormalized();
            return DirectionToTarget * ProjectileSpeed;
        }
    }

    //---------------------------------------------------------------------
    // 		📊	Calcular trajetória balística otimizada
    //---------------------------------------------------------------------
    SDK::FVector CalculateOptimalTrajectory(const SDK::FVector& StartLocation,
                                           const SDK::FVector& TargetLocation,
                                           float ProjectileSpeed,
                                           float GravityScale = 1.0f)
    {
        if (ProjectileSpeed <= 0.0f) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("TrajectoryCalc", "Velocidade de projétil inválida");
            }
            return SDK::FVector(0.0f, 0.0f, 0.0f);
        }

        try {
            // 🚀 NOVA IMPLEMENTAÇÃO: Usar sistema de detecção de obstáculos
            SDK::FVector OptimalVelocity = FindOptimalTrajectoryPath(StartLocation, TargetLocation, ProjectileSpeed);

            // Verificar se a velocidade calculada é válida
            if (OptimalVelocity.Magnitude() > 0.0f) {
                if (mods::bulletTPDebug) {
                    float Distance = (TargetLocation - StartLocation).Magnitude();
                    std::string debugMsg = "✅ Trajetória otimizada calculada - Distância: " + std::to_string(Distance);
                    SafetySystem::LogError("TrajectoryCalc", debugMsg.c_str());
                }
                return OptimalVelocity;
            }

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("TrajectoryCalc", "Erro no sistema otimizado - usando fallback");
            }
        }

        // 🔄 FALLBACK: Sistema original como backup
        SDK::FVector DirectionToTarget = (TargetLocation - StartLocation);
        float HorizontalDistance = SDK::FVector(DirectionToTarget.X, DirectionToTarget.Y, 0.0f).Magnitude();
        float VerticalDistance = DirectionToTarget.Z;

        // Calcular ângulo de lançamento otimizado considerando gravidade
        float Gravity = 980.0f * GravityScale; // Gravidade padrão do Unreal Engine
        float TimeToTarget = HorizontalDistance / ProjectileSpeed;

        // Compensar queda gravitacional
        float GravityCompensation = 0.5f * Gravity * TimeToTarget * TimeToTarget;
        SDK::FVector OptimalDirection = DirectionToTarget.GetNormalized();
        OptimalDirection.Z += GravityCompensation / DirectionToTarget.Magnitude();

        if (mods::bulletTPDebug) {
            SafetySystem::LogError("TrajectoryCalc", "⚠️ Usando sistema de trajetória tradicional (fallback)");
        }

        return OptimalDirection.GetNormalized() * ProjectileSpeed;
    }

    //---------------------------------------------------------------------
    // 		🔧	Aplicar correções avançadas ao componente de movimento
    //---------------------------------------------------------------------
    void ApplyAdvancedMovementCorrections(SDK::UMarvelProjectileComponent* MovementComp,
                                        const SDK::FVector& NewVelocity,
                                        const SDK::FVector& TargetLocation,
                                        SDK::AActor* TargetActor)
    {
        if (!IsValidPtr(MovementComp) || !IsValidPtr(TargetActor)) return;

        try {
            // 1. APLICAR NOVA VELOCIDADE
            MovementComp->Velocity = NewVelocity;

            // 2. AJUSTAR VELOCIDADE INICIAL PARA CONSISTÊNCIA
            float NewSpeed = NewVelocity.Magnitude();
            if (NewSpeed > 0.0f) {
                MovementComp->InitialSpeed = NewSpeed;
                MovementComp->MaxSpeed = NewSpeed * 1.2f; // 20% de margem
            }

            // 3. CONFIGURAR HOMING SE NECESSÁRIO
            float DistanceToTarget = (TargetLocation - MovementComp->GetOwner()->K2_GetActorLocation()).Magnitude();
            if (DistanceToTarget > 1000.0f) { // Para alvos distantes, ativar homing
                MovementComp->bIsHomingProjectile = true;
                MovementComp->HomingAccelerationMagnitude = NewSpeed * 0.5f;

                // Tentar definir o componente alvo para homing (comentado por problemas de compatibilidade)
                // if (auto* TargetComponent = TargetActor->RootComponent) {
                //     MovementComp->HomingTargetComponent = TargetComponent;
                // }
            }

            // 4. DESATIVAR RICOCHETE PARA GARANTIR HIT DIRETO
            MovementComp->bShouldBounce = false;
            MovementComp->Bounciness = 0.0f;

            // 5. AJUSTAR GRAVIDADE PARA TRAJETÓRIA MAIS PRECISA
            MovementComp->ProjectileGravityScale = 0.3f; // Reduzir efeito da gravidade

            if (mods::bulletTPDebug) {
                std::string debugMsg = "Correções aplicadas - Vel: " + std::to_string(NewSpeed) +
                                     " | Homing: " + (MovementComp->bIsHomingProjectile ? "ON" : "OFF") +
                                     " | Dist: " + std::to_string(DistanceToTarget);
                SafetySystem::LogError("MovementCorrection", debugMsg.c_str());
            }

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("MovementCorrection", "Erro ao aplicar correções de movimento");
            }
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Ajustar colisão do projétil para melhor precisão
    //---------------------------------------------------------------------
    void OptimizeProjectileCollision(SDK::UProjectileCollisionComponent* CollisionComp,
                                   SDK::AActor* TargetActor)
    {
        if (!IsValidPtr(CollisionComp) || !IsValidPtr(TargetActor)) return;

        try {
            // 1. OBTER RAIO ATUAL
            float CurrentRadius = CollisionComp->GetScaledSphereRadius();

            // 2. CALCULAR NOVO RAIO BASEADO NO ALVO
            float OptimalRadius = 50.0f; // Raio base

            // Ajustar baseado no tipo de alvo
            if (auto* TargetCharacter = static_cast<SDK::AMarvelBaseCharacter*>(TargetActor)) {
                // Para personagens, usar raio maior para garantir hit
                OptimalRadius = 75.0f;
            }

            // 3. APLICAR NOVO RAIO SE DIFERENTE
            if (abs(CurrentRadius - OptimalRadius) > 5.0f) {
                CollisionComp->SetSphereRadius(OptimalRadius, true);

                if (mods::bulletTPDebug) {
                    std::string debugMsg = "Raio de colisão ajustado: " +
                                         std::to_string(CurrentRadius) + " -> " +
                                         std::to_string(OptimalRadius);
                    SafetySystem::LogError("CollisionOptimization", debugMsg.c_str());
                }
            }

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("CollisionOptimization", "Erro ao otimizar colisão");
            }
        }
    }

    //---------------------------------------------------------------------
    // 		📊	Verificar eficácia da correção de trajetória
    //---------------------------------------------------------------------
    bool ValidateTrajectoryCorrection(SDK::AMarvelAbilityTargetActor_Projectile* Projectile,
                                    const SDK::FVector& TargetLocation,
                                    float ToleranceDistance)
    {
        if (!IsValidPtr(Projectile)) return false;

        try {
            SDK::FVector CurrentLocation = Projectile->K2_GetActorLocation();
            SDK::FVector CurrentVelocity = Projectile->MovementComponent->Velocity;

            // Verificar se a velocidade está apontando para o alvo
            SDK::FVector DirectionToTarget = (TargetLocation - CurrentLocation).GetNormalized();
            SDK::FVector VelocityDirection = CurrentVelocity.GetNormalized();

            float DotProduct = DirectionToTarget.Dot(VelocityDirection);
            bool IsPointingToTarget = DotProduct > 0.8f; // 80% de alinhamento

            // Verificar se a velocidade é adequada
            float Speed = CurrentVelocity.Magnitude();
            bool HasValidSpeed = Speed >= MIN_VALID_PROJECTILE_SPEED;

            // Verificar distância até o alvo
            float DistanceToTarget = (TargetLocation - CurrentLocation).Magnitude();
            bool IsWithinRange = DistanceToTarget <= ToleranceDistance * 50.0f; // Alcance máximo

            bool IsValid = IsPointingToTarget && HasValidSpeed && IsWithinRange;

            if (mods::bulletTPDebug && !IsValid) {
                std::string debugMsg = "Validação falhou - Alinhamento: " + std::to_string(DotProduct) +
                                     " | Velocidade: " + std::to_string(Speed) +
                                     " | Distância: " + std::to_string(DistanceToTarget);
                SafetySystem::LogError("TrajectoryValidation", debugMsg.c_str());
            }

            return IsValid;

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("TrajectoryValidation", "Erro na validação");
            }
            return false;
        }
    }

    //---------------------------------------------------------------------
    // 		🚀	FUNÇÃO PRINCIPAL: Correção Completa de Trajetória
    //---------------------------------------------------------------------
    void CorrectProjectileTrajectory(SDK::AMarvelAbilityTargetActor_Projectile* Projectile,
                                   SDK::FVector TargetLocation,
                                   SDK::AActor* TargetActor)
    {
        if (!IsValidPtr(Projectile) || !IsValidPtr(TargetActor)) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("TrajectoryCorrection", "Parâmetros inválidos");
            }
            return;
        }

        try {
            // 1. OBTER DADOS DO PROJÉTIL
            SDK::FMarvelProjectileAgentTable ProjectileData = Projectile->K2_GetProjectileData();
            SDK::FVector CurrentLocation = Projectile->K2_GetActorLocation();

            // 2. ACESSAR COMPONENTES
            auto MovementComp = Projectile->MovementComponent;
            auto CollisionComp = Projectile->SphereCollision;

            if (!IsValidPtr(MovementComp)) {
                if (mods::bulletTPDebug) {
                    SafetySystem::LogError("TrajectoryCorrection", "MovementComponent inválido");
                }
                return;
            }

            // 3. CALCULAR NOVA TRAJETÓRIA
            float ProjectileSpeed = ProjectileData.ProjectileAgent.FlySpeed;
            if (ProjectileSpeed < MIN_VALID_PROJECTILE_SPEED) {
                ProjectileSpeed = DEFAULT_PROJECTILE_SPEED;
            }

            SDK::FVector OptimalVelocity = CalculateOptimalTrajectory(
                CurrentLocation,
                TargetLocation,
                ProjectileSpeed,
                MovementComp->ProjectileGravityScale
            );

            // 4. APLICAR CORREÇÕES DE MOVIMENTO
            ApplyAdvancedMovementCorrections(MovementComp, OptimalVelocity, TargetLocation, TargetActor);

            // 5. OTIMIZAR COLISÃO
            if (IsValidPtr(CollisionComp)) {
                OptimizeProjectileCollision(CollisionComp, TargetActor);
            }

            // 6. FORÇAR ATUALIZAÇÃO DE TRANSFORMAÇÃO
            SDK::FVector DirectionToTarget = (TargetLocation - CurrentLocation).GetNormalized();
            SDK::FRotator NewRotation = SDK::UKismetMathLibrary::FindLookAtRotation(CurrentLocation, TargetLocation);
            Projectile->K2_SetActorRotation(NewRotation, true, false);

            // 7. VALIDAR CORREÇÃO
            bool CorrectionSuccessful = ValidateTrajectoryCorrection(Projectile, TargetLocation);

            if (mods::bulletTPDebug) {
                float Distance = (TargetLocation - CurrentLocation).Magnitude();
                std::string debugMsg = (CorrectionSuccessful ? "✅" : "❌") +
                                     std::string(" Trajetória corrigida - Dist: ") + std::to_string(Distance) +
                                     " | Vel: " + std::to_string(ProjectileSpeed) +
                                     " | Status: " + (CorrectionSuccessful ? "SUCESSO" : "FALHA");
                SafetySystem::LogError("TrajectoryCorrection", debugMsg.c_str());
            }

        } catch (...) {
            if (mods::bulletTPDebug) {
                SafetySystem::LogError("TrajectoryCorrection", "Erro crítico na correção de trajetória");
            }
        }
    }

    // Função para aplicar o efeito de Glow (contorno) nos jogadores inimigos
    void GlowRoutine(APlayerController *PlayerController, TArray<AActor *> Players)
    {
        if (!mods::enableGlow || !PlayerController)
            return;

        // Obter o personagem local
        auto LocalBaseChar = static_cast<AMarvelBaseCharacter *>(PlayerController->AcknowledgedPawn);
        if (!LocalBaseChar || !IsValidPtr(LocalBaseChar))
            return;

        // Ativar o contorno dos inimigos para o jogador local
        LocalBaseChar->ClientShowEnemyOutLine(true);

        // Definir o status do contorno (sempre através de paredes)
        auto OutlineStatus = SDK::ETeamOutlineShowStatus::ETOSS_Always;

        // Obter a classe para filtrar apenas AMarvelBaseCharacter
        SDK::UClass* ClassToFind = SDK::AMarvelBaseCharacter::StaticClass();
        if (!IsValidObjectPtr(ClassToFind))
            return;

        // Iterar por todos os jogadores
        for (int i = 0; i < static_cast<int>(Players.Num()); i++)
        {
            if (!Players.IsValidIndex(i))
                continue;

            AActor *Player = Players[i];
            if (!Player || Player == PlayerController->AcknowledgedPawn)
                continue;

            // FILTRO BASEADO NO PROJETO EXEMPLO: Verificar se é AMarvelBaseCharacter
            if (!Modules::CheckIfPlayer(Player, ClassToFind))
                continue; // Não é um personagem

            // Verificar se é um personagem válido
            auto BaseChar = static_cast<AMarvelBaseCharacter *>(Player);
            if (!BaseChar || !IsValidPtr(BaseChar))
                continue;

            // Verificar se os personagens são válidos
            if (!IsValidObjectPtr(Variables::AcknowledgedPawn) || !IsValidObjectPtr(BaseChar))
                continue;

            // VERIFICAÇÃO CRÍTICA: Verificar se é um jogador real antes de chamar IsAlly()
            // NPCs não têm PlayerState válido e causam crash ao chamar IsAlly()
            if (!IsValidObjectPtr(BaseChar->PlayerState))
                continue; // Pular NPCs - não são jogadores válidos

            // Usar o SDK::UTeamFunctionLibrary::IsAlly para verificar se é aliado
            // Agora é seguro chamar pois sabemos que é um jogador real
            bool bIsAlly = SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, BaseChar, true);

            if (bIsAlly)
            {
                continue; // Skip same-team players
            }

            // Aplicar o contorno ao mesh do jogador
            USkeletalMeshComponent *Mesh = BaseChar->GetMesh();
            if (Mesh && IsValidPtr(Mesh))
            {
                Mesh->SetTeamOutlineShowStatus(OutlineStatus);
            }
        }
    }

    //---------------------------------------------------------------------
    // 		🔍	Verificar se ator é um AMarvelBaseCharacter (baseado no projeto exemplo)
    //---------------------------------------------------------------------
    bool CheckIfPlayer(SDK::AActor* Player, SDK::UClass* PlayerClass)
    {
        if (Player && PlayerClass) {
            if (Player->IsA(PlayerClass)) return true;
        }
        return false;
    }

    //---------------------------------------------------------------------
    // 		🎯	Verificar se ator é especificamente um summon base
    //---------------------------------------------------------------------
    bool CheckIfSummonBase(SDK::AActor* Actor)
    {
        if (!IsValidObjectPtr(Actor))
            return false;

        // Verificar se é um summon base (inclui vários tipos de summons)
        if (Actor->IsA(SDK::AMarvelSummonerBase::StaticClass()))
            return true;

        // Verificar se tem componente de summon
        if (Actor->GetComponentByClass(SDK::UMarvelSummonedComponent::StaticClass()))
            return true;

        return false;
    }

    //---------------------------------------------------------------------
    // 		🏗️	Verificar se ator é especificamente um construto de habilidade
    //---------------------------------------------------------------------
    bool CheckIfConstruct(SDK::AActor* Actor)
    {
        if (!IsValidObjectPtr(Actor))
            return false;

        // ❌ EXCLUIR PROJÉTEIS: Não considerar projéteis como construtos
        if (Actor->IsA(SDK::AMarvelAbilityTargetActor_Projectile::StaticClass()))
            return false;

        // ✅ CONSTRUTOS REAIS: Verificar classes específicas baseadas no SDK real
        // NOTA: Implementação conservadora usando apenas verificações comprovadamente seguras

        // 1. Barriers/Barreiras (como escudos, muros do Magneto)
        if (Actor->IsA(SDK::AMarvelBarrierBase::StaticClass()))
            return true;

        // 2. Beacon do Rocket Raccoon - ASummoned_10234101 (herda de AMarvelSummonerBase)
        if (Actor->IsA(SDK::AMarvelSummonerBase::StaticClass()))
        {
            // Verificar se NÃO é um personagem (para evitar pets/minions)
            if (!Actor->IsA(SDK::AMarvelBaseCharacter::StaticClass()))
                return true;
        }

        // 3. Squids do Namor - ASummoned_10455101 (herda de ASummonedCharacterBase)
        // NOTA: Squids são personagens (ACharacter), mas são construtos atacáveis
        if (Actor->IsA(SDK::ASummonedCharacterBase::StaticClass()))
        {
            // Squids do Namor são considerados construtos mesmo sendo personagens
            return true;
        }

        // 4. Rune Stones do Loki e outras áreas de habilidade - AMarvelAbilityTargetActor_Scope
        // NOTA: Esta classe pode incluir as Rune Stones do Loki e outras áreas de habilidade
        if (Actor->IsA(SDK::AMarvelAbilityTargetActor_Scope::StaticClass()))
        {
            // Áreas de habilidade são consideradas construtos atacáveis
            return true;
        }

        // 5. Verificar se tem componente de summon mas NÃO é personagem base
        // Para capturar outros construtos que usam apenas o componente
        if (Actor->GetComponentByClass(SDK::UMarvelSummonedComponent::StaticClass()))
        {
            // Só considerar como construto se NÃO for um personagem base
            if (!Actor->IsA(SDK::AMarvelBaseCharacter::StaticClass()))
                return true;
        }

        return false;
    }

    //---------------------------------------------------------------------
    // 		📝	Nota sobre Sistema Nativo de Targeting
    //---------------------------------------------------------------------
    // DESCOBERTA: O Marvel Rivals possui enums nativos para targeting:
    // - ETargetActorType::Summoned (para summons como Beacon/Squids)
    // - ETargetActorType::Scope (para Rune Stones do Loki)
    // - EAimTargetType::NotCharacter (para construtos em geral)
    //
    // PROBLEMA: Essas funcionalidades não estão disponíveis no SDK atual:
    // - Função Cast<>() não existe em AActor
    // - Enums não têm operadores de comparação implementados
    // - Classes podem não estar instanciadas em runtime
    //
    // SOLUÇÃO: Manter implementação conservadora usando apenas IsA() comprovadamente seguro

    //---------------------------------------------------------------------
    // 		🧪	Testar e debugar funções de detecção de summons/construtos
    //---------------------------------------------------------------------
    void TestSummonConstructDetection(SDK::TArray<SDK::AActor*>& ActorList)
    {
        if (!ActorList.IsValid() || ActorList.Num() <= 0)
            return;

        int summonCount = 0;
        int constructCount = 0;
        int summonedTargetCount = 0;
        int totalActors = ActorList.Num();

        // Contar diferentes tipos de entidades detectadas
        for (int i = 0; i < totalActors; i++)
        {
            if (!ActorList.IsValidIndex(i))
                continue;

            SDK::AActor* actor = ActorList[i];
            if (!IsValidObjectPtr(actor))
                continue;

            // Testar cada função de detecção
            bool isSummon = CheckIfSummonBase(actor);
            bool isConstruct = CheckIfConstruct(actor);
            bool isSummonedTarget = CheckIfSummonedTarget(actor);

            if (isSummon) summonCount++;
            if (isConstruct) constructCount++;
            if (isSummonedTarget) summonedTargetCount++;

            // Log detalhado para debug (apenas primeiros 10 para não spam)
            if ((isSummon || isConstruct || isSummonedTarget) && (summonCount + constructCount) <= 10)
            {
                std::string logMessage = "Actor detected" +
                    std::string(" | Summon: ") + (isSummon ? "YES" : "NO") +
                    " | Construct: " + (isConstruct ? "YES" : "NO") +
                    " | SummonedTarget: " + (isSummonedTarget ? "YES" : "NO");
                SafetySystem::LogInfo("SummonTest", logMessage.c_str());
            }
        }

        // Log resumo dos resultados
        std::string summaryMessage = "RESUMO - Total Actors: " + std::to_string(totalActors) +
            " | Summons: " + std::to_string(summonCount) +
            " | Constructs: " + std::to_string(constructCount) +
            " | SummonedTargets: " + std::to_string(summonedTargetCount);
        SafetySystem::LogInfo("SummonTest", summaryMessage.c_str());
    }

    //---------------------------------------------------------------------
    // 		🔍	Listar todos os sockets disponíveis em um personagem (para debug)
    //---------------------------------------------------------------------
    void ListAvailableSockets(SDK::AMarvelBaseCharacter* Character)
    {
        if (!IsValidObjectPtr(Character))
            return;

        auto Mesh = Character->GetMesh();
        if (!IsValidObjectPtr(Mesh) || !Mesh->SkeletalMesh)
            return;

        // Lista de sockets comuns para testar
        std::vector<std::string> commonSockets = {
            "head", "neck_01", "neck_02",
            "spine_01", "spine_02", "spine_03", "spine_04", "spine_05",
            "pelvis", "root", "center", "chest", "torso",
            "clavicle_l", "clavicle_r",
            "upperarm_l", "upperarm_r", "lowerarm_l", "lowerarm_r",
            "hand_l", "hand_r", "weapon_l", "weapon_r",
            "thigh_l", "thigh_r", "calf_l", "calf_r", "foot_l", "foot_r",
            "eye_l", "eye_r", "mouth", "jaw"
        };

        printf("\n=== SOCKETS DISPONÍVEIS NO PERSONAGEM ===\n");

        int foundSockets = 0;
        for (const auto& socketName : commonSockets)
        {
            SDK::FName SocketFName = SDK::UKismetStringLibrary::Conv_StringToName(
                SDK::FString(std::wstring(socketName.begin(), socketName.end()).c_str()));

            if (Mesh->DoesSocketExist(SocketFName))
            {
                SDK::FVector SocketLocation = Mesh->GetSocketLocation(SocketFName);
                printf("✅ %s: (%.2f, %.2f, %.2f)\n",
                       socketName.c_str(), SocketLocation.X, SocketLocation.Y, SocketLocation.Z);
                foundSockets++;
            }
        }

        printf("Total de sockets encontrados: %d\n\n", foundSockets);
    }

    //---------------------------------------------------------------------
    // 		🎯	Obter posição de mira para summons/construtos
    //---------------------------------------------------------------------
    SDK::FVector GetSummonTargetLocation(SDK::AActor* Actor)
    {
        if (!IsValidObjectPtr(Actor))
            return SDK::FVector();

        // Tentar obter como AMarvelBaseCharacter primeiro (se for um summon que herda de character)
        auto BaseChar = static_cast<SDK::AMarvelBaseCharacter*>(Actor);
        if (IsValidObjectPtr(BaseChar))
        {
            auto Mesh = BaseChar->GetMesh();
            if (IsValidObjectPtr(Mesh))
            {
                // Tentar usar socket head se existir
                SDK::FName HeadSocketName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(L"head"));
                if (Mesh->DoesSocketExist(HeadSocketName))
                {
                    return Mesh->GetSocketLocation(HeadSocketName);
                }
                // Senão, usar localização do mesh
                return Mesh->K2_GetComponentLocation();
            }
        }

        // Para summons que usam StaticMeshComponent (como APhysicsSummoner)
        auto StaticMeshComp = Actor->GetComponentByClass(SDK::UStaticMeshComponent::StaticClass());
        if (IsValidObjectPtr(StaticMeshComp))
        {
            auto StaticMesh = static_cast<SDK::UStaticMeshComponent*>(StaticMeshComp);
            return StaticMesh->K2_GetComponentLocation();
        }

        // Para summons que usam UInteractableMeshComponent
        auto InteractableMeshComp = Actor->GetComponentByClass(SDK::UInteractableMeshComponent::StaticClass());
        if (IsValidObjectPtr(InteractableMeshComp))
        {
            auto InteractableMesh = static_cast<SDK::UInteractableMeshComponent*>(InteractableMeshComp);
            return InteractableMesh->K2_GetComponentLocation();
        }

        // Fallback: usar localização do ator
        return Actor->K2_GetActorLocation();
    }

    //---------------------------------------------------------------------
    // 		🎯	Verificar se ator é um summon/construto/clone atacável
    //---------------------------------------------------------------------
    bool CheckIfSummonedTarget(SDK::AActor* Actor)
    {
        if (!IsValidObjectPtr(Actor))
            return false;

        // 1. Verificar se é um CLONE/PHANTOM usando função específica do SDK
        if (SDK::UActionLogFunctionLibrary::IsPhantom(Actor))
            return true;

        // 2. Verificar se é um MarvelPhantomActor (clones diretos)
        if (Actor->IsA(SDK::AMarvelPhantomActor::StaticClass()))
            return true;

        // 3. Verificar se tem componente de summon
        if (Actor->GetComponentByClass(SDK::UMarvelSummonedComponent::StaticClass()))
            return true;

        // 4. Verificar se é um summon base (inclui vários tipos de summons)
        if (Actor->IsA(SDK::AMarvelSummonerBase::StaticClass()))
            return true;

        // 5. Verificar se é um ator de habilidade que pode ser atacado (como construtos)
        if (Actor->IsA(SDK::AMarvelAbilityTargetActor_Base::StaticClass()))
        {
            auto TargetActor = static_cast<SDK::AMarvelAbilityTargetActor_Base*>(Actor);
            if (IsValidObjectPtr(TargetActor))
            {
                // Verificar se é um alvo válido (assumir que se existe, pode ser atacado)
                return true;
            }
        }

        // 6. Verificar se é um portal de nível (pode ser atacado)
        if (Actor->IsA(SDK::APortalViewActor_Level::StaticClass()))
            return true;

        // 7. Verificar se tem componente de summon específico
        if (Actor->GetComponentByClass(SDK::USummonedComp_Level::StaticClass()) ||
            Actor->GetComponentByClass(SDK::UProtectiveSummonedComponent::StaticClass()))
            return true;

        // 8. Verificar se tem interface de phantom/clone
        if (Actor->GetComponentByClass(SDK::UPhantomStaticMeshComponent::StaticClass()) ||
            Actor->GetComponentByClass(SDK::UCreatePhantomStaticMeshComponent::StaticClass()))
            return true;

        return false;
    }

} // namespace Modules

#endif // MODULES_H