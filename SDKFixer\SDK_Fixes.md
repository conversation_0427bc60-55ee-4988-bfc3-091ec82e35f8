# Correções do SDK

## Visão Geral

Este documento descreve as correções aplicadas ao SDK extraído do jogo para resolver problemas de dependências cíclicas e outras questões que impediam a compilação do projeto.

## Problemas Comuns em SDKs Atualizados

Quando um jogo é atualizado, o SDK extraído pode apresentar diversos problemas:

1. **Dependências Cíclicas**: Ocorrem quando dois ou mais arquivos dependem um do outro de forma circular, criando um impasse na compilação.
2. **Includes Ausentes**: Arquivos de cabeçalho necessários que não são incluídos automaticamente.
3. **Incompatibilidades de Estruturas**: Mudanças nas estruturas de dados que causam erros de compilação.
4. **Assertions Incorretas**: Verificações de tamanho e alinhamento que falham devido a mudanças na estrutura do jogo.
5. **Herança <PERSON>**: Classes que herdam de outras que foram modificadas ou removidas.

## Correções Aplicadas

### 1. GameplayTags_structs.hpp

Foram realizadas as seguintes correções:

- Modificada a herança da estrutura `FGameplayTagTableRow` para remover a dependência de `FTableRowBase`
- Comentadas as verificações de tamanho e alinhamento (static_assert) para:
  - `FGameplayTagTableRow`
  - `FRestrictedGameplayTagTableRow`
  - `FGameplayTagContainer`

Exemplo de correção:
```cpp
// Antes
struct FGameplayTagTableRow : public FTableRowBase

// Depois
struct FGameplayTagTableRow //: public FTableRowBase
```

### 2. PhysicsCore_classes.hpp

Foram realizadas as seguintes correções:

- Modificada a herança da classe `UChaosServerCollisionDebugSubsystem` para herdar de `UObject` em vez de `UTickableWorldSubsystem`
- Comentada a verificação de tamanho (static_assert) para `UChaosServerCollisionDebugSubsystem`

Exemplo de correção:
```cpp
// Antes
class UChaosServerCollisionDebugSubsystem final : public UTickableWorldSubsystem

// Depois
class UChaosServerCollisionDebugSubsystem final : public UObject
```

## Diagnóstico de Problemas

Para diagnosticar problemas em SDKs atualizados:

1. **Análise de Erros de Compilação**: Examinar cuidadosamente as mensagens de erro do compilador para identificar a causa raiz.
2. **Verificação de Dependências**: Mapear as dependências entre arquivos para identificar ciclos.
3. **Comparação com Versões Anteriores**: Comparar o SDK atual com versões anteriores para identificar mudanças estruturais.
4. **Testes Incrementais**: Aplicar correções incrementalmente e testar a compilação a cada passo.

## Estratégias de Resolução

### Para Dependências Cíclicas:
- Remover ou comentar herança problemática
- Usar declarações antecipadas (forward declarations)
- Reorganizar a estrutura de includes

### Para Verificações de Tamanho e Alinhamento:
- Comentar static_assert problemáticos
- Ajustar os valores esperados para refletir a nova estrutura

### Para Includes Ausentes:
- Adicionar os includes necessários
- Verificar a ordem dos includes para evitar problemas de dependência

## Conclusão

As correções aplicadas resolveram os problemas de compilação do SDK atualizado, permitindo que o projeto compile com sucesso. Estas correções são específicas para esta versão do SDK e podem precisar ser revisadas em atualizações futuras do jogo.
