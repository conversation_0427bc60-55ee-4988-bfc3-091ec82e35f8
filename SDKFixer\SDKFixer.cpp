#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <utility>
#include <filesystem>
#include <regex>

namespace fs = std::filesystem;

//---------------------------------------------------------------------
// 		🔍	SDKFixer Class
//---------------------------------------------------------------------
class SDKFixer {
private:
    std::string sdk_folder;
    std::map<std::string, std::vector<std::pair<std::string, std::string>>> fixes_map;
    std::vector<std::string> files_to_check;

public:
    SDKFixer() {
        // Obter o diretório atual e adicionar o caminho para a pasta SDK
        sdk_folder = fs::current_path().string() + "\\Game\\SDK\\SDK";
        
        // Lista de arquivos que precisam ser verificados
        files_to_check = {
            "GameplayTags_structs.hpp",
            "PhysicsCore_classes.hpp",
            "Engine_classes.hpp",
            "CoreUObject_structs.hpp",
            "Marvel_classes.hpp"
        };

        // Correções para GameplayTags_structs.hpp
        fixes_map["GameplayTags_structs.hpp"] = {
            {"struct FGameplayTagTableRow : public FTableRowBase", 
             "struct FGameplayTagTableRow //: public FTableRowBase"},
            {"static_assert(sizeof(FGameplayTagTableRow)", 
             "//static_assert(sizeof(FGameplayTagTableRow)"},
            {"static_assert(offsetof(FGameplayTagTableRow, Tag)", 
             "//static_assert(offsetof(FGameplayTagTableRow, Tag)"},
            {"static_assert(offsetof(FGameplayTagTableRow, DevComment)", 
             "//static_assert(offsetof(FGameplayTagTableRow, DevComment)"},
            {"static_assert(sizeof(FRestrictedGameplayTagTableRow)", 
             "//static_assert(sizeof(FRestrictedGameplayTagTableRow)"},
            {"static_assert(offsetof(FRestrictedGameplayTagTableRow, bAllowNonRestrictedChildren)", 
             "//static_assert(offsetof(FRestrictedGameplayTagTableRow, bAllowNonRestrictedChildren)"},
            {"static_assert(alignof(FGameplayTagContainer)", 
             "//static_assert(alignof(FGameplayTagContainer)"}
        };

        // Correções para PhysicsCore_classes.hpp
        fixes_map["PhysicsCore_classes.hpp"] = {
            {"class UChaosServerCollisionDebugSubsystem final : public UTickableWorldSubsystem", 
             "class UChaosServerCollisionDebugSubsystem final : public UObject"},
            {"static_assert(sizeof(UChaosServerCollisionDebugSubsystem)", 
             "//static_assert(sizeof(UChaosServerCollisionDebugSubsystem)"}
        };
    }

    void fix_sdk() {
        std::cout << "Iniciando correção do SDK..." << std::endl;
        
        for (const auto& filename : files_to_check) {
            std::string filepath = sdk_folder + "\\" + filename;
            
            // Verificar se o arquivo existe
            if (!fs::exists(filepath)) {
                std::cout << "Arquivo não encontrado: " << filepath << " - pulando" << std::endl;
                continue;
            }
            
            // Verificar se existem correções para este arquivo
            if (fixes_map.find(filename) == fixes_map.end()) {
                std::cout << "Nenhuma correção definida para: " << filename << " - verificando apenas" << std::endl;
                check_file(filepath, filename);
                continue;
            }
            
            // Ler o conteúdo do arquivo
            std::string code;
            {
                std::ifstream file(filepath);
                if (file.is_open()) {
                    std::string line;
                    while (std::getline(file, line)) {
                        code += line + "\n";
                    }
                    file.close();
                }
                else {
                    std::cout << "Falha ao abrir " << filepath << " para leitura" << std::endl;
                    continue;
                }
            }
            
            // Aplicar as correções específicas para o arquivo
            bool changes_made = false;
            std::string original_code = code;
            
            for (const auto& [pattern, replacement] : fixes_map[filename]) {
                // Encontrar todas as ocorrências e substituir
                size_t pos = 0;
                while ((pos = code.find(pattern, pos)) != std::string::npos) {
                    code.replace(pos, pattern.length(), replacement);
                    pos += replacement.length();
                    changes_made = true;
                }
            }
            
            // Se foram feitas alterações, escrever o conteúdo modificado de volta para o arquivo
            if (changes_made) {
                std::ofstream file(filepath);
                if (file.is_open()) {
                    file << code;
                    file.close();
                    std::cout << "Arquivo corrigido: " << filename << std::endl;
                }
                else {
                    std::cout << "Falha ao abrir " << filepath << " para escrita" << std::endl;
                }
            }
            else {
                std::cout << "Nenhuma correção necessária para: " << filename << std::endl;
            }
        }
        
        std::cout << "Correção do SDK concluída!" << std::endl;
    }

    void check_file(const std::string& filepath, const std::string& filename) {
        // Ler o conteúdo do arquivo
        std::string code;
        {
            std::ifstream file(filepath);
            if (file.is_open()) {
                std::string line;
                while (std::getline(file, line)) {
                    code += line + "\n";
                }
                file.close();
            }
            else {
                std::cout << "Falha ao abrir " << filepath << " para leitura" << std::endl;
                return;
            }
        }
        
        // Verificar padrões comuns de problemas
        std::vector<std::string> potential_issues;
        
        // Verificar static_assert
        std::regex static_assert_regex("static_assert\\(.*\\);");
        std::smatch match;
        std::string::const_iterator search_start(code.cbegin());
        while (std::regex_search(search_start, code.cend(), match, static_assert_regex)) {
            potential_issues.push_back(match.str());
            search_start = match.suffix().first;
        }
        
        // Verificar herança problemática
        std::regex inheritance_regex("(class|struct)\\s+\\w+\\s+.*:\\s+public\\s+\\w+");
        search_start = code.cbegin();
        while (std::regex_search(search_start, code.cend(), match, inheritance_regex)) {
            potential_issues.push_back(match.str());
            search_start = match.suffix().first;
        }
        
        // Reportar potenciais problemas
        if (!potential_issues.empty()) {
            std::cout << "Potenciais problemas encontrados em " << filename << ":" << std::endl;
            for (size_t i = 0; i < potential_issues.size() && i < 5; ++i) {
                std::cout << "  - " << potential_issues[i] << std::endl;
            }
            if (potential_issues.size() > 5) {
                std::cout << "  ... e mais " << (potential_issues.size() - 5) << " problemas potenciais." << std::endl;
            }
        }
    }

    void add_fix(const std::string& filename, const std::string& pattern, const std::string& replacement) {
        fixes_map[filename].push_back({pattern, replacement});
    }
};

//---------------------------------------------------------------------
// 		🔍	Main Function
//---------------------------------------------------------------------
int main() {
    SDKFixer fixer;
    fixer.fix_sdk();
    
    std::cout << "\nDeseja adicionar correções personalizadas? (s/n): ";
    char response;
    std::cin >> response;
    
    if (response == 's' || response == 'S') {
        std::string filename, pattern, replacement;
        
        std::cout << "Nome do arquivo (ex: Engine_classes.hpp): ";
        std::cin.ignore();
        std::getline(std::cin, filename);
        
        std::cout << "Padrão a ser substituído: ";
        std::getline(std::cin, pattern);
        
        std::cout << "Substituição: ";
        std::getline(std::cin, replacement);
        
        fixer.add_fix(filename, pattern, replacement);
        fixer.fix_sdk();
    }
    
    return 0;
}

