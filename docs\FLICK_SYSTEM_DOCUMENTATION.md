# Sistema Flick Simplificado - Documentação Técnica

## Visão Geral
O Sistema Flick é uma funcionalidade **autônoma e simplificada** que reduz temporariamente os valores de smoothing (suavização) e inércia aos valores mínimos para permitir movimentos de mira extremamente rápidos quando necessário.

## Funcionalidade Principal

### Objetivo
Criar um sistema **simples e eficaz** que proporciona movimentos de mira ultrarrápidos por um período configurável, funcionando de forma completamente independente do sistema de aimbot.

### Comportamento (Versão com Curvas de Redução)
1. **Detecção Autônoma**: Monitora a tecla do aimbot independentemente do estado do aimbot
2. **Ativação Instantânea**: Ativa imediatamente ao detectar pressionamento da tecla
3. **Redução Configurável**: Três tipos de curvas de redução selecionáveis
4. **Duração Configurável**: Permanece ativo pelo tempo configurado (0.1 a 3.0 segundos)
5. **Desativação Automática**: Restaura valores originais automaticamente após o tempo configurado
6. **Independência Total**: Funciona independente do estado do aimbot ou presença de alvos

## Implementação Técnica

### Arquivos Modificados
- **Globals.h**: Adicionadas variáveis de configuração
- **Modules.h**: Implementada a lógica principal do sistema
- **main.cpp**: Adicionada interface do usuário e sistema de salvamento
- **LanguageSystem.h/.cpp**: Adicionadas traduções em português, inglês e espanhol

### Variáveis de Configuração (Com Curvas de Redução)
```cpp
// Configurações do usuário (salvas no perfil)
bool useFlick = false;                  // Ativar/desativar sistema Flick
float flickDuration = 1.0f;             // Duração do Flick em segundos (0.1-3.0)
int flickReductionType = 0;             // Tipo de redução: 0=Linear, 1=Exponencial, 2=Quadrático

// Variáveis internas de controle (não salvas no perfil)
bool flickIsActive = false;             // Estado atual do Flick
float flickStartTime = 0.0f;            // Tempo quando o Flick foi iniciado
bool lastAimbotKeyState = false;        // Estado anterior da tecla para detectar pressionamento
```

### Algoritmo Principal (Com Curvas de Redução)
```cpp
// 1. Detectar pressionamento da tecla (independente do aimbot)
bool currentAimbotKeyState = isAimbotKeyPressed;
bool keyJustPressed = currentAimbotKeyState && !lastAimbotKeyState;
lastAimbotKeyState = currentAimbotKeyState;

// 2. Ativar Flick ao pressionar a tecla
if (useFlick && keyJustPressed) {
    flickIsActive = true;
    flickStartTime = currentTime;
}

// 3. Processar Flick ativo
if (useFlick && flickIsActive) {
    float elapsedTime = currentTime - flickStartTime;

    // Verificar se o tempo de duração foi atingido
    if (elapsedTime >= flickDuration) {
        flickIsActive = false;  // Desativar Flick
    } else {
        // Calcular progresso (0.0 a 1.0)
        float progress = elapsedTime / flickDuration;

        // Aplicar curva de redução baseada no tipo selecionado
        float reductionCurve = 0.0f;
        switch (flickReductionType) {
            case 0: // Linear
                reductionCurve = progress;
                break;
            case 1: // Exponencial (rápido início, suave fim)
                reductionCurve = 1.0f - std::exp(-3.0f * progress);
                break;
            case 2: // Quadrático (suave início, rápido fim)
                reductionCurve = progress * progress;
                break;
        }

        // Aplicar redução: de 100% para 10% baseado na curva
        flickSmoothingMultiplier = 1.0f - (0.9f * reductionCurve);
        flickInertiaMultiplier = 1.0f - (0.9f * reductionCurve);
    }
}
```

### Tipos de Curvas de Redução

#### **1. Linear (Tipo 0)**
- **Comportamento**: Redução constante e uniforme
- **Fórmula**: `reductionCurve = progress`
- **Características**:
  - Redução previsível e constante
  - Ideal para usuários que preferem comportamento linear
  - Velocidade de redução constante durante toda a duração

#### **2. Exponencial (Tipo 1)**
- **Comportamento**: Redução rápida no início, suaviza no final
- **Fórmula**: `reductionCurve = 1.0f - std::exp(-3.0f * progress)`
- **Características**:
  - Redução agressiva nos primeiros momentos
  - Suavização gradual conforme se aproxima do final
  - Ideal para flicks rápidos que precisam de controle no final

#### **3. Quadrático (Tipo 2)**
- **Comportamento**: Redução suave no início, acelera no final
- **Fórmula**: `reductionCurve = progress * progress`
- **Características**:
  - Início suave para controle inicial
  - Aceleração da redução conforme progride
  - Ideal para movimentos que precisam de precisão inicial

### Integração com Sistemas Existentes
- **Smoothing Adaptativo**: Funciona em conjunto, aplicando multiplicadores após cálculos adaptativos
- **Inércia Adaptativa**: Compatível com sistema de inércia adaptativa
- **Smoothing Separado**: Suporta tanto modo unificado quanto separado (Pitch/Yaw)
- **Sistema de Keybind**: Utiliza as mesmas teclas configuradas para o aimbot

## Interface do Usuário

### Configurações Disponíveis (Com Curvas de Redução)
1. **Use Flick**: Checkbox para habilitar/desabilitar o sistema
2. **Flick Duration**: Slider de 0.1 a 3.0 segundos para definir duração do Flick
3. **Reduction Type**: Combo box para selecionar tipo de curva (Linear, Exponential, Quadratic)

### Localização na UI
- Seção: "FLICK SETTINGS"
- Posição: Após configurações de delay, antes de humanização
- Tooltip: Explicações detalhadas em português, inglês e espanhol

## Sistema de Perfil

### Salvamento Automático (Com Curvas de Redução)
As configurações do Flick são automaticamente salvas e carregadas com o perfil:
```ini
useFlick=1
flickDuration=1.000000
flickReductionType=0
```

**Nota**: As variáveis internas de controle (flickIsActive, flickStartTime, lastAimbotKeyState) não são salvas no perfil, sendo resetadas a cada inicialização.

## Características Técnicas

### Vantagens (Versão Simplificada)
- **Simplicidade Extrema**: Algoritmo direto e fácil de entender
- **Autonomia Total**: Funciona independente do estado do aimbot
- **Performance Máxima**: Cálculos mínimos, máxima eficiência
- **Confiabilidade**: Sem dependências complexas ou condições especiais
- **Previsibilidade**: Comportamento consistente e previsível
- **Facilidade de Configuração**: Apenas uma configuração (duração)
- **Integração Perfeita**: Funciona com todos os sistemas existentes

### Limitações de Segurança (Respeitando Todos os Limitadores)
- **Aim Delay**: Respeita completamente o sistema de aim delay
- **Team Check**: Só ativa se o alvo passar pela verificação de equipe
- **Verificação de Visibilidade**: Respeita configurações de visibilidade (VisCheck)
- **FOV Check**: Só ativa se alvo estiver dentro do FOV (exceto Support Vision)
- **Alvo Válido**: Verifica se o alvo é válido e está vivo
- **Duração Limitada**: Máximo de 3 segundos para prevenir uso excessivo
- **Ativação por Tecla**: Só ativa quando tecla é pressionada (não automático)
- **Validação de Entrada**: Duração limitada entre 0.1 e 3.0 segundos

## Debugging e Monitoramento

### Log de Depuração (Com Verificações de Segurança)
```cpp
// Log de ativação
printf("Flick ATIVADO - Duração: %.1fs (respeitando todos os limitadores)\n", flickDuration);

// Log durante execução
printf("Flick ATIVO - Tipo: %s, Progresso: %.2f, Curva: %.2f, Mult: %.2f, Tempo restante: %.1fs\n",
       curveTypeName, progress, reductionCurve, flickSmoothingMultiplier, flickDuration - elapsedTime);

// Log de desativação
printf("Flick DESATIVADO - Tempo completado (%.1fs)\n", elapsedTime);

// Log de pausa
printf("Flick PAUSADO - Motivo: %s\n", pauseReason);
// Motivos possíveis: "aim delay ativo", "alvo inválido", "alvo não passou nas verificações de segurança"
```

### Frequência de Log (Simplificada)
- **Ativação/Desativação**: Log imediato para eventos importantes
- **Execução**: Log limitado a cada 1 segundo para evitar spam
- **Estados**: Log simples sobre tempo restante e multiplicadores
- **Debugging**: Informações básicas sobre duração e estado

## Configurações Recomendadas

### Para Iniciantes
- **Use Flick**: Ativado
- **Flick Duration**: 1.5s (duração moderada para aprender)
- **Reduction Type**: Linear (comportamento previsível)

### Para Usuários Avançados
- **Use Flick**: Ativado
- **Flick Duration**: 1.0s (duração balanceada)
- **Reduction Type**: Exponencial (controle no final)

### Para Máxima Velocidade
- **Use Flick**: Ativado
- **Flick Duration**: 0.5s (duração curta para flicks rápidos)
- **Reduction Type**: Exponencial (redução rápida inicial)

### Para Alvos Distantes
- **Use Flick**: Ativado
- **Flick Duration**: 2.0s (duração longa para rotações grandes)
- **Reduction Type**: Quadrático (precisão inicial)

### Para Uso Geral (Recomendado)
- **Use Flick**: Ativado
- **Flick Duration**: 1.0s (duração padrão equilibrada)
- **Reduction Type**: Linear (comportamento consistente)

### Para Controle Preciso
- **Use Flick**: Ativado
- **Flick Duration**: 1.2s (duração moderada)
- **Reduction Type**: Quadrático (início suave, fim rápido)

## Compatibilidade

### Sistemas Compatíveis
✅ Smoothing Adaptativo
✅ Inércia Adaptativa
✅ Smoothing Separado (Pitch/Yaw)
✅ Sistema de Humanização
✅ Sistema de Delay
✅ Keybind Flexível
✅ Gamepad e Teclado/Mouse

### Requisitos
- SDK customizado do projeto
- Sistema de aimbot ativo
- Configuração válida de FOV

## Notas de Desenvolvimento

### Data de Implementação
24 de maio de 2025

### Versão
2.2.0 - Versão com Respeito Total aos Limitadores de Segurança

### Desenvolvedor
Aprimorado seguindo questionamento do usuário sobre respeitar aim delay e outros limitadores

### Melhorias na Versão 2.2.0 (Limitadores de Segurança)
- **Respeito ao Aim Delay**: Só ativa após o fim do período de aquisição de alvo
- **Team Check**: Verifica se o alvo é válido conforme configuração de equipe
- **Verificação de Visibilidade**: Respeita configurações de VisCheck e LineOfSight
- **FOV Check**: Só ativa se alvo estiver dentro do FOV (exceto Support Vision)
- **Validação de Alvo**: Verifica se o alvo é válido e está vivo
- **Três Curvas de Redução**: Linear, Exponencial e Quadrático selecionáveis
- **Sistema de Pausa**: Pausa inteligente quando limitadores não são atendidos
- **Logs Detalhados**: Sistema de log com motivos de pausa e ativação

### Diferenças da Versão Anterior (2.0.0 → 2.1.0)
- ❌ **Removido**: Redução fixa instantânea para 10%
- ✅ **Adicionado**: Três tipos de curvas de redução selecionáveis
- ✅ **Adicionado**: Redução gradual baseada em progresso temporal
- ✅ **Adicionado**: Interface combo box para seleção de curva
- ✅ **Adicionado**: Logs detalhados com tipo de curva
- ✅ **Mantido**: Sistema autônomo de detecção de tecla
- ✅ **Mantido**: Duração configurável simples
- ✅ **Mantido**: Performance otimizada

### Próximas Melhorias Sugeridas (Mantendo Simplicidade)
- Configuração de intensidade da redução (além dos 10% fixos)
- Modo de ativação alternativo (toggle em vez de hold)
- Perfis rápidos de duração (0.5s, 1.0s, 1.5s, 2.0s)
- Indicador visual na UI quando Flick está ativo
