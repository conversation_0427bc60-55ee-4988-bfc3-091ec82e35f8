// Não incluir WarningSuppressions.h aqui, pois já está incluído no main.cpp

#include <windows.h>
#include <psapi.h>
#include <dxgi1_4.h>
#include <d3d12.h>
#pragma comment(lib, "d3d12.lib")

#include "./ThirdParty/HookLib/HookLib.h"

#include "ImGui/imgui.h"
#include "ImGui/imgui_impl_dx12.h"
#include "ImGui/imgui_impl_win32.h"

//=========================================================================================================================//

#include <fstream>
using namespace std;

//=========================================================================================================================//

WNDCLASSEX WindowClass;
HWND WindowHwnd;

//---------------------------------------------------------------------
// 		🔒	Sistema de Validação com Timeout e Cache
//---------------------------------------------------------------------
#include <unordered_map>
#include <chrono>
#include <atomic>

// Cache para resultados de validação com timeout
struct ValidationCache {
    std::unordered_map<uintptr_t, bool> cache;
    std::unordered_map<uintptr_t, std::chrono::steady_clock::time_point> timestamps;
    std::atomic<bool> inValidation{false};
    static constexpr auto CACHE_TIMEOUT = std::chrono::milliseconds(100); // Cache por 100ms
    static constexpr auto MAX_VALIDATION_TIME = std::chrono::milliseconds(5); // Timeout de 5ms
};

static ValidationCache g_validationCache;

inline bool safe_memory_check(uintptr_t address)
{
    // Verificação básica rápida
    if (address < 0x10000 || address > 0x7FFFFFFFFFFF)
        return false;

    auto now = std::chrono::steady_clock::now();

    // Verificar cache primeiro
    auto cacheIt = g_validationCache.cache.find(address);
    if (cacheIt != g_validationCache.cache.end()) {
        auto timestampIt = g_validationCache.timestamps.find(address);
        if (timestampIt != g_validationCache.timestamps.end()) {
            if (now - timestampIt->second < ValidationCache::CACHE_TIMEOUT) {
                return cacheIt->second; // Retornar resultado do cache
            }
        }
    }

    // Evitar validações concorrentes que podem causar deadlock
    if (g_validationCache.inValidation.exchange(true)) {
        // Se já há uma validação em andamento, assumir inválido para evitar deadlock
        return false;
    }

    bool result = false;
    auto startTime = std::chrono::steady_clock::now();

    __try
    {
        // Verificar timeout durante a validação
        if (std::chrono::steady_clock::now() - startTime > ValidationCache::MAX_VALIDATION_TIME) {
            result = false;
        } else {
            volatile uint8_t temp = *reinterpret_cast<uint8_t *>(address);
            (void)temp;
            result = true;
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = false;
    }

    // Atualizar cache
    g_validationCache.cache[address] = result;
    g_validationCache.timestamps[address] = now;

    // Limpar cache antigo periodicamente (a cada 1000 validações)
    static std::atomic<int> validationCount{0};
    if (++validationCount % 1000 == 0) {
        auto cutoff = now - ValidationCache::CACHE_TIMEOUT;
        for (auto it = g_validationCache.timestamps.begin(); it != g_validationCache.timestamps.end();) {
            if (it->second < cutoff) {
                g_validationCache.cache.erase(it->first);
                it = g_validationCache.timestamps.erase(it);
            } else {
                ++it;
            }
        }
    }

    g_validationCache.inValidation = false;
    return result;
}

//---------------------------------------------------------------------
// 		⚡	Validação Rápida para Loops Críticos
//---------------------------------------------------------------------
inline bool safe_memory_check_fast(uintptr_t address)
{
    // Validação ultra-rápida sem SEH para loops críticos
    // Usar apenas verificações básicas para evitar deadlocks
    if (address < 0x10000 || address > 0x7FFFFFFFFFFF)
        return false;

    // Verificar se está na faixa de memória do jogo
    if (address >= 0x44000000 && address < 0x80000000)
        return true;

    // Para outros endereços, usar verificação básica sem SEH
    return (address >= 0x10000);
}

//---------------------------------------------------------------------
// 		🔄	Validação com Limite de Iterações (Sem SEH)
//---------------------------------------------------------------------
template<typename Func>
inline bool safe_loop_execute(const char* loopName, int maxIterations, Func&& loopBody)
{
    static std::unordered_map<std::string, std::chrono::steady_clock::time_point> lastExecutionTime;
    static std::unordered_map<std::string, int> executionCount;

    auto now = std::chrono::steady_clock::now();
    auto& lastTime = lastExecutionTime[loopName];
    auto& count = executionCount[loopName];

    // Reset contador se passou mais de 1 segundo
    if (now - lastTime > std::chrono::seconds(1)) {
        count = 0;
        lastTime = now;
    }

    // Limitar execuções por segundo para evitar loops infinitos
    if (count > maxIterations) {
        return false;
    }

    count++;

    // Executar sem SEH para evitar problemas com lambdas
    try {
        loopBody();
        return true;
    }
    catch (...) {
        return false;
    }
}

template <typename T>
inline T read(uintptr_t address)
{
	if (!safe_memory_check(address))
		return T{};

	return *reinterpret_cast<T *>(address);
}

template <typename T>
inline void write(uintptr_t address, const T &value)
{
	if (!safe_memory_check(address))
		return;
	*reinterpret_cast<T *>(address) = value;
}

// Função segura para ler um valor da memória com retorno de status
template <typename T>
inline bool read_memory_safe(uintptr_t address, T &output)
{
	__try
	{
		if (!safe_memory_check(address))
			return false;

		output = *reinterpret_cast<T *>(address);
		return true;
	}
	__except (EXCEPTION_EXECUTE_HANDLER)
	{
		return false;
	}
}

bool InitWindow()
{

	WindowClass.cbSize = sizeof(WNDCLASSEX);
	WindowClass.style = CS_HREDRAW | CS_VREDRAW;
	WindowClass.lpfnWndProc = DefWindowProc;
	WindowClass.cbClsExtra = 0;
	WindowClass.cbWndExtra = 0;
	WindowClass.hInstance = GetModuleHandle(NULL);
	WindowClass.hIcon = NULL;
	WindowClass.hCursor = NULL;
	WindowClass.hbrBackground = NULL;
	WindowClass.lpszMenuName = NULL;
	WindowClass.lpszClassName = "MJ";
	WindowClass.hIconSm = NULL;
	RegisterClassEx(&WindowClass);
	WindowHwnd = CreateWindow(WindowClass.lpszClassName, "DirectX Window", WS_OVERLAPPEDWINDOW, 0, 0, 100, 100, NULL, NULL, WindowClass.hInstance, NULL);
	if (WindowHwnd == NULL)
	{
		return false;
	}
	return true;
}

bool DeleteWindow()
{
	DestroyWindow(WindowHwnd);
	UnregisterClass(WindowClass.lpszClassName, WindowClass.hInstance);
	if (WindowHwnd != NULL)
	{
		return false;
	}
	return true;
}

#if defined _M_X64
typedef uint64_t uintx_t;
#elif defined _M_IX86
typedef uint32_t uintx_t;
#endif

static uintx_t *MethodsTable = NULL;

//=========================================================================================================================//

namespace DirectX12
{
	bool Init()
	{

		if (InitWindow() == false)
		{
			return false;
		}

		HMODULE D3D12Module = GetModuleHandle("d3d12.dll");
		HMODULE DXGIModule = GetModuleHandle("dxgi.dll");
		if (D3D12Module == NULL || DXGIModule == NULL)
		{
			DeleteWindow();
			return false;
		}

		void *CreateDXGIFactory = GetProcAddress(DXGIModule, "CreateDXGIFactory");
		if (CreateDXGIFactory == NULL)
		{
			DeleteWindow();
			return false;
		}

		IDXGIFactory *Factory;
		if (((long(__stdcall *)(const IID &, void **))(CreateDXGIFactory))(__uuidof(IDXGIFactory), (void **)&Factory) < 0)
		{
			DeleteWindow();
			return false;
		}

		IDXGIAdapter *Adapter;
		if (Factory->EnumAdapters(0, &Adapter) == DXGI_ERROR_NOT_FOUND)
		{
			DeleteWindow();
			return false;
		}

		void *D3D12CreateDevice = GetProcAddress(D3D12Module, "D3D12CreateDevice");
		if (D3D12CreateDevice == NULL)
		{
			DeleteWindow();
			return false;
		}

		ID3D12Device *Device;
		if (((long(__stdcall *)(IUnknown *, D3D_FEATURE_LEVEL, const IID &, void **))(D3D12CreateDevice))(Adapter, D3D_FEATURE_LEVEL_11_0, __uuidof(ID3D12Device), (void **)&Device) < 0)
		{
			DeleteWindow();
			return false;
		}

		D3D12_COMMAND_QUEUE_DESC QueueDesc;
		QueueDesc.Type = D3D12_COMMAND_LIST_TYPE_DIRECT;
		QueueDesc.Priority = 0;
		QueueDesc.Flags = D3D12_COMMAND_QUEUE_FLAG_NONE;
		QueueDesc.NodeMask = 0;

		ID3D12CommandQueue *CommandQueue;
		if (Device->CreateCommandQueue(&QueueDesc, __uuidof(ID3D12CommandQueue), (void **)&CommandQueue) < 0)
		{
			DeleteWindow();
			return false;
		}

		ID3D12CommandAllocator *CommandAllocator;
		if (Device->CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE_DIRECT, __uuidof(ID3D12CommandAllocator), (void **)&CommandAllocator) < 0)
		{
			DeleteWindow();
			return false;
		}

		ID3D12GraphicsCommandList *CommandList;
		if (Device->CreateCommandList(0, D3D12_COMMAND_LIST_TYPE_DIRECT, CommandAllocator, NULL, __uuidof(ID3D12GraphicsCommandList), (void **)&CommandList) < 0)
		{
			DeleteWindow();
			return false;
		}

		DXGI_RATIONAL RefreshRate;
		RefreshRate.Numerator = 60;
		RefreshRate.Denominator = 1;

		DXGI_MODE_DESC BufferDesc;
		BufferDesc.Width = 100;
		BufferDesc.Height = 100;
		BufferDesc.RefreshRate = RefreshRate;
		BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
		BufferDesc.ScanlineOrdering = DXGI_MODE_SCANLINE_ORDER_UNSPECIFIED;
		BufferDesc.Scaling = DXGI_MODE_SCALING_UNSPECIFIED;

		DXGI_SAMPLE_DESC SampleDesc;
		SampleDesc.Count = 1;
		SampleDesc.Quality = 0;

		DXGI_SWAP_CHAIN_DESC SwapChainDesc = {};
		SwapChainDesc.BufferDesc = BufferDesc;
		SwapChainDesc.SampleDesc = SampleDesc;
		SwapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
		SwapChainDesc.BufferCount = 2;
		SwapChainDesc.OutputWindow = WindowHwnd;
		SwapChainDesc.Windowed = 1;
		SwapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_FLIP_DISCARD;
		SwapChainDesc.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;

		IDXGISwapChain *SwapChain;
		if (Factory->CreateSwapChain(CommandQueue, &SwapChainDesc, &SwapChain) < 0)
		{
			DeleteWindow();
			return false;
		}

		MethodsTable = (uintx_t *)::calloc(150, sizeof(uintx_t));
		memcpy(MethodsTable, *(uintx_t **)Device, 44 * sizeof(uintx_t));
		memcpy(MethodsTable + 44, *(uintx_t **)CommandQueue, 19 * sizeof(uintx_t));
		memcpy(MethodsTable + 44 + 19, *(uintx_t **)CommandAllocator, 9 * sizeof(uintx_t));
		memcpy(MethodsTable + 44 + 19 + 9, *(uintx_t **)CommandList, 60 * sizeof(uintx_t));
		memcpy(MethodsTable + 44 + 19 + 9 + 60, *(uintx_t **)SwapChain, 18 * sizeof(uintx_t));

		MH_Initialize();
		Device->Release();
		Device = NULL;
		CommandQueue->Release();
		CommandQueue = NULL;
		CommandAllocator->Release();
		CommandAllocator = NULL;
		CommandList->Release();
		CommandList = NULL;
		SwapChain->Release();
		SwapChain = NULL;
		DeleteWindow();
		return true;
	}
}

//=========================================================================================================================//

bool CreateHook(uint16_t Index, void **Original, void *Function)
{
	assert(_index >= 0 && _original != NULL && _function != NULL);
	void *target = (void *)MethodsTable[Index];
	if (MH_CreateHook(target, Function, Original) != MH_OK || MH_EnableHook(target) != MH_OK)
	{
		return false;
	}
	return true;
}

void DisableHook(uint16_t Index)
{
	assert(Index >= 0);
	MH_DisableHook((void *)MethodsTable[Index]);
}

void DisableAll()
{
	MH_DisableHook(MH_ALL_HOOKS);
	free(MethodsTable);
	MethodsTable = NULL;
}