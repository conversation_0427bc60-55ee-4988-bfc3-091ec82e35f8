#pragma once

// Este arquivo contém diretivas para suprimir avisos específicos em partes do código
// que não podemos modificar diretamente (como SDKs de terceiros)

// Desativar avisos antes de incluir arquivos do SDK
#ifdef _MSC_VER
// Salvar o estado atual dos avisos
#pragma warning(push)

// Desativar avisos específicos
#pragma warning(disable : 4244)  // Conversão de tipo com possível perda de dados
#pragma warning(disable : 4267)  // Conversão de 'size_t' para tipo menor
#pragma warning(disable : 4305)  // Truncamento de constante
#pragma warning(disable : 4311)  // Truncamento de ponteiro
#pragma warning(disable : 4302)  // Truncamento de tipo
#pragma warning(disable : 4018)  // Comparação signed/unsigned
#pragma warning(disable : 4309)  // Truncamento de valor constante
#pragma warning(disable : 4369)  // Valor de enumerador não representável
#pragma warning(disable : 4996)  // Função ou variável pode ser insegura
#pragma warning(disable : 5054)  // Operador entre enumerações de tipos diferentes
#pragma warning(disable : 26451) // Overflow aritmético
#pragma warning(disable : 26495) // Variável não inicializada
#pragma warning(disable : 26812) // Tipo enum não escopo
#pragma warning(disable : 4127)  // Expressão condicional é constante
#endif

// Incluir este arquivo antes de incluir arquivos do SDK
// Depois de incluir os arquivos do SDK, usar:
// #ifdef _MSC_VER
//     #pragma warning(pop)
// #endif
