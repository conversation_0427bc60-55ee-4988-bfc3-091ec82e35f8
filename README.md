# 🎮 JocastaProtocol - Game Enhancement Tool

## 📋 Descrição

JocastaProtocol é uma ferramenta de aprimoramento para jogos desenvolvida em C++ com interface ImGui, projetada para funcionar com um SDK customizado extraído do jogo alvo.

## 🛠️ Tecnologias Utilizadas

- **Linguagem**: C++
- **Interface**: Dear ImGui
- **Renderização**: DirectX 12
- **Compilação**: Visual Studio 2022
- **SDK**: SDK customizado extraído do jogo

## 🔧 Compilação

### Pré-requisitos
- Visual Studio 2022 com Build Tools
- Windows SDK

### Comando de Compilação
```powershell
MSBuild.exe "JocastaProtocol.sln" /p:Configuration=Release /p:Platform=x64 /m:4
```

Ou usando o caminho completo:
```powershell
& "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" "JocastaProtocol.sln" /p:Configuration=Release /p:Platform=x64 /m:4
```

## 🎨 Interface e Temas

O projeto utiliza um tema personalizado laranja/dourado para a interface ImGui, com suporte completo para:
- Animações suaves
- Widgets personalizados
- Sistema de captura de teclas
- Múltiplos idiomas

## 🔧 Correções Implementadas

### ✅ Correção AMD ImGui (2025-06-15) - COMPLETA
- **Problema**: Corrupção de cores em placas AMD devido a valores de alpha 1.0f/255
- **Solução**: Alteração de valores alpha de 1.0f→0.99f e 255→252
- **Arquivos Afetados**: `main.cpp`, `KeyCaptureSystem.h`, `AnimatedWidgets.cpp`, `Globals.h`, `OverlayRenderer.h`, `Modules.h`
- **Total de Correções**: 18 valores de alpha corrigidos
- **Documentação**: [AMD_ImGui_Fix.md](docs/AMD_ImGui_Fix.md)

### 🛡️ Sistema de Segurança
- Sistema SafetySystem para execução segura de código crítico
- Validação robusta de ponteiros
- Modo seguro para situações de instabilidade

### 🎯 Melhorias de Estabilidade
- ✅ **CORREÇÃO COMPLETA**: Todas as ocorrências de GetAllActorsOfClass substituídas por PersistentLevel->Actors
- ✅ **NOVA FUNCIONALIDADE**: Sistema de detecção de summons/construtos/clones atacáveis
- Sistema de validação de memória aprimorado
- Tratamento de exceções SEH
- Eliminação da causa raiz de crashes em mapas com NPCs

## 🎮 Funcionalidades Principais

### 🎯 Sistema de Detecção de Alvos
- **ESP (Extra Sensory Perception)** para jogadores
- **Detecção de summons/construtos/clones** atacáveis (NOVO)
- **Indicadores visuais diferenciados** por tipo:
  - 🎭 **Clones**: Magenta + "CLONE"
  - 🤖 **Summons**: Laranja + "SUMMON"
  - 🏗️ **Construtos**: Ciano + "CONSTRUCT"
- **Sistema de priorização configurável** no aimbot (NOVO):
  - Opção para priorizar summons
  - Opção para priorizar construtos
  - Mira adaptativa por tipo de alvo

### 🎮 Sistema de Mira
- **Aimbot** com múltiplos modos de mira
- **Seleção automática de alvo** com FOV configurável
- **Detecção de projéteis** em tempo real
- **Predição de movimento** avançada

### 🛡️ Recursos de Segurança
- **Validação robusta** de memória
- **Sistema SafetySystem** para execução segura
- **Tratamento de exceções** SEH
- **Modo seguro** para situações críticas

## 📁 Estrutura do Projeto

```
JocastaProtocol/
├── main.cpp                    # Arquivo principal
├── main.h                      # Cabeçalho principal
├── Globals.h                   # Variáveis globais
├── Modules.h                   # Módulos de funcionalidades
├── OverlayRenderer.h           # Sistema de renderização
├── AnimatedWidgets.cpp/h       # Widgets animados
├── KeyCaptureSystem.h          # Sistema de captura de teclas
├── LanguageSystem.cpp/h        # Sistema de idiomas
├── ImGui/                      # Biblioteca ImGui
├── Game/SDK/                   # SDK customizado do jogo
├── docs/                       # Documentação técnica
└── Release/                    # Arquivos de release
```

## 📚 Documentação

A documentação técnica completa está disponível na pasta `docs/`:

- [Correção AMD ImGui](docs/AMD_ImGui_Fix.md)
- [Análise de Crashes](docs/CRASH_ROOT_CAUSE_ANALYSIS.md)
- [Sistema de Segurança](docs/SAFE_MODE_CORRIGIDO.md)
- [Validação de Memória](docs/SAFE_MEMORY_VALIDATION_IMPROVEMENTS.md)
- [Solução GetAllActors](docs/SOLUCAO_DEFINITIVA_GETALLACTORS.md)
- [Correção GetAllActorsOfClass](docs/CORRECAO_GETALLACTORSOFCLASS_COMPLETA.md)
- [Detecção de Summons/Construtos/Clones](docs/DETECCAO_SUMMONS_CONSTRUTOS.md)
- [Priorização de Summons/Construtos no Aimbot](docs/PRIORIZACAO_AIMBOT_SUMMONS.md)
- [Sockets de Personagens e Solução para Projéteis Atravessando](docs/SOCKETS_PERSONAGENS_BULLETTP.md)

## ⚠️ Limitações Técnicas

### SDK Customizado
- Classes do SDK não são polimórficas (sem `dynamic_cast`)
- Bibliotecas padrão do Unreal Engine não estão disponíveis
- Desenvolvimento limitado a C++ padrão e SDK customizado

### Estrutura de Nomenclatura
- Use `SDK::FRotator` em vez de estruturas diretas
- Atualize `Variables::AcknowledgedPawn` para `SDK::AcknowledgedPawn`
- Consulte `./Game/SDK/SDK.hpp` para classes disponíveis

## 🔍 Resolução de Problemas

### Problemas de Compilação
1. Verifique se o Visual Studio 2022 está instalado
2. Confirme que o Windows SDK está disponível
3. Execute o comando de compilação como administrador se necessário

### Problemas de Renderização AMD
- A correção para corrupção de cores AMD já está implementada
- Se persistirem problemas, verifique os drivers da placa gráfica

## 🤝 Contribuição

Para contribuir com o projeto:
1. Mantenha a documentação atualizada
2. Siga as convenções de nomenclatura do SDK
3. Teste em múltiplas configurações de hardware
4. Documente todas as correções implementadas

## 📄 Licença

Este projeto é desenvolvido para fins educacionais e de pesquisa.

---

**Última Atualização**: 2025-06-15  
**Versão**: 1.0  
**Status**: ✅ Estável com Correções AMD
