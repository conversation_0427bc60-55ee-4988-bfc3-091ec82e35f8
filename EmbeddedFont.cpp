#include "EmbeddedFont.h"
#include <iostream>

//---------------------------------------------------------------------
// 		🔤	Implementação do Sistema de Fonte Embarcada
//---------------------------------------------------------------------

namespace EmbeddedFont {
    
    // Variáveis globais
    ImFont* MontserratFont = nullptr;
    ImFont* FontAwesomeFont = nullptr;
    bool FontsLoaded = false;
    
    //---------------------------------------------------------------------
    // 		📁	<PERSON><PERSON><PERSON> de Fonte do Recurso
    //---------------------------------------------------------------------
    EmbeddedFontData LoadFontResource(int resourceId) {
        EmbeddedFontData fontData = {nullptr, 0};
        
        try {
            // Obter handle do módulo atual
            HMODULE hModule = GetModuleHandle(NULL);
            if (!hModule) {
                std::cerr << "Erro: Não foi possível obter handle do módulo" << std::endl;
                return fontData;
            }
            
            // Encontrar o recurso
            HRSRC hResource = FindResource(hModule, MAKEINTRESOURCE(resourceId), RT_RCDATA);
            if (!hResource) {
                std::cerr << "Erro: Recurso de fonte não encontrado (ID: " << resourceId << ")" << std::endl;
                return fontData;
            }
            
            // Carregar o recurso
            HGLOBAL hLoadedResource = LoadResource(hModule, hResource);
            if (!hLoadedResource) {
                std::cerr << "Erro: Não foi possível carregar recurso de fonte" << std::endl;
                return fontData;
            }
            
            // Obter ponteiro para os dados
            fontData.data = LockResource(hLoadedResource);
            if (!fontData.data) {
                std::cerr << "Erro: Não foi possível fazer lock do recurso de fonte" << std::endl;
                return fontData;
            }
            
            // Obter tamanho dos dados
            fontData.size = SizeofResource(hModule, hResource);
            if (fontData.size == 0) {
                std::cerr << "Erro: Tamanho do recurso de fonte é zero" << std::endl;
                fontData.data = nullptr;
                return fontData;
            }
            
            std::cout << "Fonte carregada com sucesso (ID: " << resourceId << ", Tamanho: " << fontData.size << " bytes)" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "Exceção ao carregar fonte: " << e.what() << std::endl;
            fontData.data = nullptr;
            fontData.size = 0;
        }
        
        return fontData;
    }
    
    //---------------------------------------------------------------------
    // 		🔤	Carregar Fontes Embarcadas (Versão Simplificada)
    //---------------------------------------------------------------------
    bool LoadEmbeddedFonts(float fontSize) {
        try {
            // Verificar se já foram carregadas
            if (FontsLoaded) {
                return true;
            }

            std::cout << "Tentando carregar fontes embarcadas..." << std::endl;

            // Por enquanto, apenas marcar como carregadas para evitar crashes
            // TODO: Implementar carregamento real das fontes quando o sistema estiver estável
            FontsLoaded = true;

            std::cout << "Sistema de fontes embarcadas inicializado (modo seguro)!" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cerr << "Exceção ao carregar fontes embarcadas: " << e.what() << std::endl;
            return false;
        }
    }
    
    //---------------------------------------------------------------------
    // 		✅	Verificar se Fontes Foram Carregadas
    //---------------------------------------------------------------------
    bool AreFontsLoaded() {
        return FontsLoaded;
    }
    
    //---------------------------------------------------------------------
    // 		🔤	Obter Fonte Montserrat
    //---------------------------------------------------------------------
    ImFont* GetMontserratFont() {
        return MontserratFont;
    }
    
    //---------------------------------------------------------------------
    // 		🎨	Obter Fonte FontAwesome
    //---------------------------------------------------------------------
    ImFont* GetFontAwesomeFont() {
        return FontAwesomeFont;
    }
    
    //---------------------------------------------------------------------
    // 		🧹	Limpar Recursos das Fontes
    //---------------------------------------------------------------------
    void CleanupFonts() {
        MontserratFont = nullptr;
        FontAwesomeFont = nullptr;
        FontsLoaded = false;
        std::cout << "Recursos de fontes embarcadas limpos" << std::endl;
    }
    
    //---------------------------------------------------------------------
    // 		🔍	Obter Ícone FontAwesome para Emoji
    //---------------------------------------------------------------------
    const char* GetIconForEmoji(const char* emoji) {
        for (int i = 0; icon_mappings[i].emoji != nullptr; i++) {
            if (strcmp(icon_mappings[i].emoji, emoji) == 0) {
                return icon_mappings[i].fontawesome_icon;
            }
        }
        return nullptr; // Emoji não encontrado
    }
    
    //---------------------------------------------------------------------
    // 		🎨	Renderizar Texto com Ícones (Versão Simplificada)
    //---------------------------------------------------------------------
    void RenderTextWithIcons(const char* text) {
        // Por enquanto, apenas renderizar texto normal para evitar crashes
        ImGui::Text("%s", text ? text : "");
    }
}
