# Movimentos Adaptativos e First Enemy Lock - Documentação

## Resumo das Implementações

Este documento descreve as funcionalidades implementadas no sistema de aimbot:

1. **Reset de Movimentos Adaptativos ao Trocar de Alvo** - ✅ VERIFICADO E MELHORADO
2. **First Enemy Lock (Trava no Primeiro Inimigo)** - CORRIGIDO
3. **Target Invisible Enemies (Mirar em Inimigos Invisíveis)** - NOVO
4. **Verificações de Segurança para Valores Adaptativos** - ✅ NOVO

---

## 1. Reset de Movimentos Adaptativos ao Trocar de Alvo ✅ VERIFICADO E MELHORADO

### Descrição
Os movimentos adaptativos são resetados automaticamente sempre que:
- O alvo muda (incluindo quando o alvo anterior morre)
- A tecla do aimbot é solta
- Um novo alvo é detectado

### Implementação Atual
- **Arquivo**: `Modules.h`
- **Função**: `RunAimbot()`
- **Linhas**: 1734-1783 (detecção de mudança) e 2126-2179 (cálculo adaptativo)

### Comportamento Verificado
✅ **Reset entre alvos diferentes**: Quando `targetChanged = true`, todas as variáveis adaptativas são resetadas
✅ **Proporcionalidade ao tempo**: Redução baseada em `TargetTrackingTime / adaptiveDuration`
✅ **Verificações de segurança**: Valores são limitados a ranges seguros

### Melhorias Implementadas
- **Verificação de limites**: Valores adaptativos são limitados com `Clamp()`
- **Detecção de morte**: Alvos mortos são tratados como mudança de alvo
- **Valores seguros**: Prevenção de valores inválidos que poderiam causar problemas
- **Curva linear**: Mudança de curva quadrática para linear para comportamento mais consistente

### Benefícios
- Garante consistência no comportamento do aimbot
- Evita que configurações adaptativas de um alvo anterior afetem o novo alvo
- Melhora a precisão inicial ao trocar de alvos

### Detecção Aprimorada de Mudança de Alvo
- Detecta quando um alvo morre (`GetCurrentHealth() <= 0.0f`)
- Reseta movimentos adaptativos automaticamente quando alvo se torna inválido
- Funciona em todas as situações de mudança de alvo

---

## 2. First Enemy Lock (Trava no Primeiro Inimigo) - CORRIGIDO

### Descrição
Nova funcionalidade que permite travar no primeiro inimigo selecionado no FOV. Quando ativada, a mira não irá trocar de alvos enquanto esse inimigo estiver presente no FOV, independente dos filtros.

### Configuração
- **Variável**: `mods::firstEnemyLock` (bool)
- **Padrão**: `false` (desativado)
- **Interface**: Checkbox "Trava no Primeiro Inimigo" na aba Aimbot/BulletTP

### Implementação

#### Arquivos Modificados
1. **Globals.h**
   - Adicionada variável `mods::firstEnemyLock`
   - Adicionada variável `Variables::FirstLockedTarget`

2. **LanguageSystem.h/cpp**
   - Adicionados textos em todos os idiomas suportados:
     - `FirstEnemyLockLabel`
     - `FirstEnemyLockTooltip`

3. **main.cpp**
   - Adicionada interface de usuário
   - Adicionado salvamento/carregamento de configuração

4. **Modules.h**
   - Modificada função `SelectTarget()` (linhas 794-1102)
   - Modificada função `RunAimbot()` (linhas 1183-1236)

#### Lógica de Funcionamento

##### Na Função SelectTarget():
1. **Detecção de Nova Ativação**: Verifica se a tecla foi pressionada pela primeira vez (transição de não-pressionado para pressionado)
2. **Seleção Limpa**: Se nova ativação detectada, reseta `FirstLockedTarget` para garantir seleção limpa
3. **Verificação de Tecla**: Se `firstEnemyLock` está ativo e tecla de aimbot está pressionada
4. **Validação do Alvo Travado**:
   - Verifica se o alvo ainda é válido (`PlayerState` válido)
   - Verifica se ainda está no FOV
   - Calcula distância do centro da tela
5. **Retorno do Alvo Travado**: Se ainda está no FOV, retorna o alvo travado
6. **Reset Automático**: Se o alvo não é mais válido ou tecla não está pressionada, reseta `Variables::FirstLockedTarget`
7. **Definição do Primeiro Alvo**: Se encontra um novo alvo, não há alvo travado e tecla está pressionada, define como primeiro alvo

##### Na Função RunAimbot():
1. **Reset ao Soltar Tecla**: Quando o botão do aimbot não está pressionado, reseta `Variables::FirstLockedTarget`
2. **Reset Condicional**: Quando o alvo muda e `firstEnemyLock` não está ativo, reseta o primeiro alvo travado

### Seleção Limpa Entre Ativações

#### Problema Anterior
- O alvo do FirstEnemyLock persistia entre ativações da tecla
- Se o usuário soltasse e pressionasse a tecla novamente, o mesmo alvo anterior era reutilizado
- Comportamento inconsistente e não intuitivo

#### Solução Implementada
- **Detecção de Nova Ativação**: Sistema detecta transição de tecla não-pressionada para pressionada
- **Reset Forçado**: A cada nova ativação, o `FirstLockedTarget` é resetado para `nullptr`
- **Seleção Limpa**: Garante que cada ativação resulte em uma nova seleção de alvo

#### Comportamento Atual
1. **Primeira Ativação**: Pressionar tecla → Seleciona primeiro inimigo no FOV
2. **Soltar Tecla**: Alvo é resetado
3. **Segunda Ativação**: Pressionar tecla novamente → **Nova seleção limpa** do primeiro inimigo no FOV
4. **Resultado**: Cada ativação é independente e consistente

### Textos da Interface

#### Português
- **Label**: "Trava no Primeiro Inimigo"
- **Tooltip**: "Trava no primeiro inimigo selecionado no FOV e não troca de alvo enquanto esse inimigo permanecer visível. Cada ativação faz uma nova seleção limpa."

#### Inglês
- **Label**: "First Enemy Lock"
- **Tooltip**: "Locks onto the first enemy selected in FOV and won't switch targets while that enemy remains visible. Each activation performs a clean new selection."

### Configuração de Arquivo
- **Seção**: `[Aimbot]`
- **Chave**: `firstEnemyLock=0` (0=desativado, 1=ativado)

---

## Integração e Compatibilidade

### Compatibilidade com Outras Funcionalidades
- ✅ **Team Target Mode**: Funciona corretamente
- ✅ **Role Target**: Funciona corretamente
- ✅ **Flying Target Priority**: Funciona corretamente
- ✅ **Support Vision**: Funciona corretamente
- ✅ **VisCheck**: Funciona corretamente
- ✅ **LineOfSight**: Funciona corretamente
- ✅ **Target Invisible Enemies**: Nova funcionalidade integrada
- ✅ **Movimentos Adaptativos**: Reset implementado corretamente

### Prioridade de Seleção
Quando `firstEnemyLock` está ativo:
1. **Primeiro**: Alvo travado (se ainda no FOV)
2. **Segundo**: Seleção normal baseada em filtros

### Reset Automático
O primeiro alvo travado é resetado quando:
- O botão do aimbot é solto
- O alvo sai do FOV
- O alvo se torna inválido
- O alvo morre
- `firstEnemyLock` é desativado

### Correções Implementadas
- **Verificação de Vida**: Agora verifica se o alvo ainda está vivo
- **LineOfSight**: Respeita configurações de LineOfSight
- **VisCheck**: Respeita configurações de verificação de visibilidade
- **Team Check**: Aplica verificações de equipe corretamente
- **Validação Completa**: Aplica todas as verificações do sistema principal

---

## 3. Target Invisible Enemies (Mirar em Inimigos Invisíveis) - NOVO

### Descrição
Nova funcionalidade que permite ao usuário escolher se quer mirar em alvos invisíveis/camuflados ou não. Anteriormente, o sistema sempre permitia mirar em alvos invisíveis.

### Configuração
- **Variável**: `mods::targetInvisibleEnemies` (bool)
- **Padrão**: `true` (permite mirar em invisíveis - comportamento anterior)
- **Interface**: Checkbox "Mirar em Inimigos Invisíveis" na aba Aimbot/BulletTP

### Comportamento
- **Quando ATIVADO** (`true`): Permite mirar em alvos invisíveis (comportamento anterior)
- **Quando DESATIVADO** (`false`): Só mira em alvos visíveis

### Implementação
- Verificação adicional em `SelectTarget()`, `RunAimbot()` e `IsFirstLockedTargetStillValid()`
- Usa os mesmos métodos de detecção de visibilidade (`WasRecentlyRendered` ou `LineOfSight`)
- Independente da configuração `VisCheck` - é uma verificação adicional

### Textos da Interface

#### Português
- **Label**: "Mirar em Inimigos Invisíveis"
- **Tooltip**: "Quando ativado, permite mirar em inimigos invisíveis/camuflados. Quando desativado, mira apenas em inimigos visíveis"

#### Inglês
- **Label**: "Target Invisible Enemies"
- **Tooltip**: "When enabled, allows targeting enemies that are invisible/cloaked. When disabled, only targets visible enemies"

### Configuração de Arquivo
- **Seção**: `[Aimbot]`
- **Chave**: `targetInvisibleEnemies=1` (1=permite invisíveis, 0=apenas visíveis)

---

## Testes Recomendados

1. **Teste de Reset de Movimentos Adaptativos**:
   - Ativar movimentos adaptativos
   - Mirar em um alvo por alguns segundos
   - Trocar para outro alvo
   - Verificar se os valores adaptativos resetaram

2. **Teste de First Enemy Lock**:
   - Ativar a funcionalidade
   - Mirar no primeiro inimigo que aparecer no FOV
   - Verificar se não troca de alvo enquanto o inimigo estiver visível
   - Verificar se troca quando o inimigo sai do FOV

3. **Teste de Target Invisible Enemies**:
   - Ativar a funcionalidade e verificar se mira em alvos invisíveis
   - Desativar e verificar se ignora alvos invisíveis
   - Testar com diferentes métodos de detecção (VisCheck, LineOfSight)

4. **Teste de Compatibilidade**:
   - Testar com diferentes filtros ativos
   - Testar com Team Target Mode
   - Testar com Role Target
   - Testar First Enemy Lock com alvos invisíveis

---

## 4. Verificações de Segurança para Valores Adaptativos ✅ NOVO

### Descrição
Sistema de verificações implementado para garantir que os valores adaptativos sejam sempre válidos e seguros.

### Implementação
- **Arquivo**: `Modules.h`
- **Linhas**: 1688-1699, 1762-1774, 2132-2179

### Verificações Implementadas
```cpp
// Verificação de limites para valores de smoothing
Variables::CurrentSmoothAmount = mods::Clamp(mods::initialSmoothAmount, 0.1f, 100.0f);

// Verificação de duração adaptativa
float safeDuration = mods::Clamp(mods::adaptiveDuration, 0.1f, 30.0f);

// Garantir que valores mínimos não excedam os iniciais
float safeMin = mods::Clamp(mods::minSmoothAmount, 0.1f, mods::initialSmoothAmount);

// Curva linear para adaptação mais consistente
float adaptiveCurve = adaptiveProgress; // Linear em vez de quadrática

// Curva linear para inércia mais uniforme
float inertiaCurve = normalizedInertia; // Linear em vez de quadrática
```

### Benefícios
- **Prevenção de crashes**: Valores inválidos são corrigidos automaticamente
- **Comportamento consistente**: Garantia de que a adaptação funciona corretamente
- **Robustez**: Sistema resistente a configurações incorretas do usuário
- **Curvas lineares**: Comportamento mais previsível e uniforme em todos os níveis
- **Inércia melhorada**: Influência mais consistente da inércia em valores baixos e altos

---

## ✅ **RESULTADO FINAL**

Todas as funcionalidades foram implementadas e verificadas com sucesso:

1. ✅ **Reset de Movimentos Adaptativos** - Verificado e melhorado com verificações de segurança
2. ✅ **First Enemy Lock Corrigido** - Agora mantém o primeiro alvo até que seja morto ou saia do FOV
3. ✅ **Target Invisible Enemies** - Permite mirar em inimigos invisíveis quando ativado
4. ✅ **Verificações de Segurança** - Sistema robusto com prevenção de valores inválidos

### Confirmações de Funcionamento
- ✅ **Reset entre alvos**: Valores adaptativos resetam corretamente
- ✅ **Proporcionalidade**: Redução é proporcional ao tempo selecionado
- ✅ **Segurança**: Valores são limitados a ranges seguros
- ✅ **Compilação**: 0 erros, 0 avisos

---

## Notas de Desenvolvimento

- Todas as modificações respeitam a arquitetura existente
- Não há breaking changes
- Compatibilidade total com configurações existentes
- Implementação thread-safe
- Logs de debug removidos para evitar spam
- Sistema adaptativo verificado e melhorado com verificações de segurança
