---
type: "always_apply"
---

NUNCA realize mudanças os buscas na pasta .example, pois ela é um projeto separado apenas de exemplo para ser usado apenas para comparação.

# Technical Constraints & Limitations
The following constraints apply to the project development environment and available resources:
SDK: Development relies on a custom Software Development Kit (SDK) extracted from the target game. The standard Unreal Engine Integrated Development Environment (IDE) is not used.
Compilation Environment: The project is compiled using Visual Studio 2022.
Standard Compilation: ALWAYS use the Visual Studio development environment to compile the projects. The standard command to be used is 
MSBuild.exe "JocastaProtocol.sln" /p:Configuration=Release /p:Platform=x64 /m:4

Library Restrictions:
Standard Unreal Engine classes and libraries are generally unavailable within this custom SDK.
Development is primarily restricted to:
-Standard C++ libraries.
As classes do SDK não são polimórficas, então não podemos usar dynamic_cast
SEMPRE verificar as assinaturas corretas das funções
SEMPRE utilizar as chamadas de funções com os parâmetros corretos


Usage Example:
Forbidden (unless available in SDK): Direct use of standard Unreal Engine static classes (e.g., FMath) or constants (e.g., FVector::ZeroVector).
Adding New Dependencies: If functionality requires classes or libraries not currently available (beyond standard C++ and the defined SDK subset):
You may find the classes or libraries you may need in the SDK on the folder ./Game/SDK or in the ./Game/SDK/SDK.hpp header file.

Adding New Dependencies: If functionality requires classes or libraries not currently available (beyond standard C++ and the defined SDK subset):
You may find the classes or libraries you need in the SDK on the folder ./Game/SDK

Solution Prioritization: ALWAYS prioritize finding solutions and alternatives using the components available within the custom SDK before proposing generic C++ solutions.
Attempt to resolve these issues by making necessary adaptations to the proposed code.
Avoid immediately reverting the solution; prioritize fixing the integration within the existing constraints.
NEVER ask the user to do anything relate to write or search in the code. Do everything by yourself.

You will avoid wasting a line with a comment, you will always identify each function of a file with the following style (use apropiate emoji related to function):

//---------------------------------------------------------------------
// 		🔍	Example Function
//---------------------------------------------------------------------

**Converter comentários úteis para inline**
   - Comentários explicativos devem ser convertidos para inline quando possível
   - Exemplo:
     ```cpp
     // Increment the counter
     counter++;
     ```
     Deve ser convertido para:
     ```cpp
     counter++; // Incrementa o contador
     ```

....
## I. Code Analysis and Understanding
Dependency Analysis: ALWAYS thoroughly analyze the files related to the request, as well as their dependencies and interconnections, before providing any answer or solution.
Fact-Based Foundation: ALWAYS provide concrete answers and solutions strictly based on existing code and the analysis performed. NEVER provide hypothetical answers or solutions based on guesswork. An answer should only be given after obtaining concrete and sufficient information.
Critical Mindset: ALWAYS maintain a critical mindset regarding requests, aiming to ensure that the resulting code is optimized, correct, functional, and adheres to best practices.

## II. Development and Solution Quality
Functional Solutions: NEVER provide placeholder or temporary solutions. ALWAYS deliver complete and functional solutions.
Information Seeking: If implementation is hindered by a lack of information, ALWAYS formulate detailed and specific questions that enable obtaining the necessary data to deliver the functional solution.
Solution Specificity: NEVER provide generic solutions. Solutions must be specific and appropriate to the presented context.
Lint Checking: ALWAYS check for and correct any lint errors or warnings before proceeding with compilation or code delivery.
Removal of Obsolete Code: ALWAYS remove code that has become obsolete, is commented out without a clear purpose, or will no longer be used in the project.

## IV. Documentation
Documentation as Mission-Critical: ALWAYS treat documentation as mission-critical infrastructure.
Creation of Memory Banks: In the absence of relevant "Memory Bank" files, ALWAYS create these files automatically, following established templates, without requiring explicit permission.
Continuous Updates (README.md): ALWAYS document or correct the main documentation (e.g., README.md) whenever a new understanding of the application code is gained or when a new feature is added.
Documentation vs. Code Consistency: ALWAYS check for inconsistent information between the documentation and the source code, CORRECTING the documentation to reflect the current state of the code.
New Feature Documentation: For every new feature implemented, ALWAYS create a dedicated documentation file (in .md format), recording all context, design decisions, and relevant information that might be needed for future maintenance or precise modifications.

## V. Interaction and Communication
Language: ALWAYS communicate exclusively in pt-BR.
User Clarity: NEVER leave the user without a proper understanding of the provided code, the modifications made, or the reasoning behind the decisions taken. Clear explanations must be provided as needed.