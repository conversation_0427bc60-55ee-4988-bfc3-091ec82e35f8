#!/usr/bin/env python3
"""
Analisador de Offsets do SDK
Calcula offsets corretos baseado na estrutura real definida no SDK
"""

import os
import re
import logging
from typing import Dict, List, Tuple, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OffsetAnalyzer:
    def __init__(self):
        self.sdk_folder = os.path.join(os.getcwd(), "Game", "SDK", "SDK")
        
        # Tamanhos básicos de tipos C++
        self.type_sizes = {
            'bool': 1,
            'uint8': 1, 'int8': 1, 'char': 1,
            'uint16': 2, 'int16': 2, 'short': 2,
            'uint32': 4, 'int32': 4, 'int': 4, 'float': 4,
            'uint64': 8, 'int64': 8, 'double': 8,
            'void*': 8, 'uintptr_t': 8,  # 64-bit pointers
            'class FString': 16,  # FString é tipicamente 16 bytes
            'class FName': 8,     # FName é tipicamente 8 bytes
            'struct FGameplayTag': 8,  # FGameplayTag é tipicamente 8 bytes
        }
        
    def analyze_struct_definition(self, struct_name: str, file_content: str) -> Optional[Dict]:
        """Analisa a definição de uma struct no conteúdo do arquivo"""

        # Procurar pela definição da struct
        struct_pattern = rf'struct {re.escape(struct_name)}[^{{]*\{{([^}}]*)\}};'
        match = re.search(struct_pattern, file_content, re.DOTALL)

        if not match:
            logger.warning(f"Struct {struct_name} não encontrada")
            return None

        struct_body = match.group(1)
        logger.info(f"📋 Analisando struct {struct_name}")

        # Verificar se há comentário sobre herança
        inheritance_info = self.check_inheritance_info(struct_name, file_content)

        # Extrair membros da struct
        members = self.extract_struct_members(struct_body)

        # Calcular offsets considerando herança
        offsets = self.calculate_offsets_with_inheritance(members, inheritance_info)

        return {
            'name': struct_name,
            'members': members,
            'offsets': offsets,
            'inheritance_info': inheritance_info,
            'total_size': inheritance_info.get('base_size', 0) + sum(member['size'] for member in members)
        }

    def check_inheritance_info(self, struct_name: str, file_content: str) -> Dict:
        """Verifica informações sobre herança da struct"""
        inheritance_info = {'has_base': False, 'base_size': 0, 'base_name': None}

        # Procurar comentário com informações de tamanho
        # Exemplo: // 0x0020 (0x0030 - 0x0010)
        size_pattern = rf'// 0x([0-9A-Fa-f]+) \(0x([0-9A-Fa-f]+) - 0x([0-9A-Fa-f]+)\)\s*struct {re.escape(struct_name)}'
        match = re.search(size_pattern, file_content)

        if match:
            struct_size = int(match.group(1), 16)
            total_size = int(match.group(2), 16)
            base_size = int(match.group(3), 16)

            inheritance_info.update({
                'has_base': base_size > 0,
                'base_size': base_size,
                'struct_size': struct_size,
                'total_size': total_size
            })

            logger.info(f"  Herança detectada: base_size=0x{base_size:04X}, struct_size=0x{struct_size:04X}")

        # Verificar herança comentada
        commented_inheritance = rf'struct {re.escape(struct_name)} //: public (\w+)'
        match = re.search(commented_inheritance, file_content)
        if match:
            inheritance_info['base_name'] = match.group(1)
            inheritance_info['inheritance_commented'] = True
            logger.info(f"  Herança comentada detectada: {match.group(1)}")

        return inheritance_info
    
    def extract_struct_members(self, struct_body: str) -> List[Dict]:
        """Extrai membros de uma struct"""
        members = []
        
        # Padrão para capturar declarações de membros
        # Exemplo: uint8 Pad_31[0x7]; // 0x0031(0x0007)
        member_pattern = r'(\w+(?:\s+\w+)*)\s+(\w+)(?:\[([^\]]+)\])?;\s*//[^(]*\(([^)]+)\)'
        
        for match in re.finditer(member_pattern, struct_body):
            type_name = match.group(1).strip()
            member_name = match.group(2).strip()
            array_size = match.group(3)
            offset_info = match.group(4)
            
            # Extrair offset e tamanho do comentário
            offset_match = re.search(r'0x([0-9A-Fa-f]+)\(0x([0-9A-Fa-f]+)\)', offset_info)
            if offset_match:
                offset = int(offset_match.group(1), 16)
                size = int(offset_match.group(2), 16)
            else:
                # Calcular tamanho baseado no tipo
                size = self.get_type_size(type_name)
                if array_size:
                    try:
                        array_count = int(array_size.replace('0x', ''), 16 if '0x' in array_size else 10)
                        size *= array_count
                    except:
                        pass
                offset = 0  # Será calculado depois
            
            members.append({
                'type': type_name,
                'name': member_name,
                'size': size,
                'offset': offset,
                'array_size': array_size
            })
            
        return members
    
    def get_type_size(self, type_name: str) -> int:
        """Obtém o tamanho de um tipo"""
        # Limpar o tipo
        clean_type = type_name.replace('const ', '').replace('static ', '').strip()
        
        # Verificar tipos conhecidos
        if clean_type in self.type_sizes:
            return self.type_sizes[clean_type]
            
        # Tipos de classe/struct genéricos
        if clean_type.startswith('class ') or clean_type.startswith('struct '):
            return 8  # Assumir 8 bytes para tipos desconhecidos
            
        # Padrão para tipos desconhecidos
        return 4
    
    def calculate_offsets_with_inheritance(self, members: List[Dict], inheritance_info: Dict) -> Dict[str, int]:
        """Calcula offsets corretos considerando herança"""
        offsets = {}
        current_offset = inheritance_info.get('base_size', 0)

        logger.info(f"  Iniciando cálculo de offset em 0x{current_offset:04X} (base class size)")

        for member in members:
            # Aplicar alinhamento se necessário
            alignment = self.get_alignment(member['type'])
            if current_offset % alignment != 0:
                current_offset += alignment - (current_offset % alignment)

            offsets[member['name']] = current_offset
            current_offset += member['size']

            logger.info(f"  {member['name']}: offset 0x{current_offset-member['size']:04X}, size 0x{member['size']:04X}")

        return offsets

    def calculate_offsets(self, members: List[Dict]) -> Dict[str, int]:
        """Método legado - mantido para compatibilidade"""
        return self.calculate_offsets_with_inheritance(members, {'base_size': 0})
    
    def get_alignment(self, type_name: str) -> int:
        """Obtém o alinhamento necessário para um tipo"""
        # Alinhamento básico - a maioria dos tipos se alinha ao seu tamanho
        size = self.get_type_size(type_name)
        if size >= 8:
            return 8
        elif size >= 4:
            return 4
        elif size >= 2:
            return 2
        else:
            return 1
    
    def analyze_file(self, filename: str) -> Dict[str, Dict]:
        """Analisa um arquivo do SDK"""
        filepath = os.path.join(self.sdk_folder, filename)
        
        if not os.path.exists(filepath):
            logger.error(f"Arquivo não encontrado: {filename}")
            return {}
        
        logger.info(f"🔍 Analisando arquivo: {filename}")
        
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        results = {}
        
        # Structs específicas para analisar baseado no arquivo
        if filename == "GameplayTags_structs.hpp":
            structs_to_analyze = [
                "FGameplayTagTableRow",
                "FRestrictedGameplayTagTableRow"
            ]
        elif filename == "Engine_structs.hpp":
            structs_to_analyze = [
                "FAnimNotifyEvent"
            ]
        else:
            # Encontrar todas as structs no arquivo
            struct_matches = re.findall(r'struct (\w+)', content)
            structs_to_analyze = list(set(struct_matches))[:5]  # Limitar a 5 para não sobrecarregar
        
        for struct_name in structs_to_analyze:
            result = self.analyze_struct_definition(struct_name, content)
            if result:
                results[struct_name] = result
        
        return results
    
    def generate_corrected_asserts(self, analysis_results: Dict[str, Dict]) -> List[str]:
        """Gera static_assert corrigidos baseado na análise"""
        corrections = []
        
        for struct_name, data in analysis_results.items():
            offsets = data['offsets']
            
            for member_name, offset in offsets.items():
                # Gerar static_assert corrigido
                assert_line = f"static_assert(offsetof({struct_name}, {member_name}) == 0x{offset:06X}, \"Member '{struct_name}::{member_name}' has a wrong offset!\");"
                corrections.append(assert_line)
            
            # Gerar assert de tamanho total
            total_size = data['total_size']
            size_assert = f"static_assert(sizeof({struct_name}) == 0x{total_size:06X}, \"Wrong size on {struct_name}\");"
            corrections.append(size_assert)
            corrections.append("")  # Linha em branco
        
        return corrections

def main():
    analyzer = OffsetAnalyzer()
    
    # Arquivos para analisar
    files_to_analyze = [
        "GameplayTags_structs.hpp",
        "Engine_structs.hpp"
    ]
    
    all_results = {}
    
    for filename in files_to_analyze:
        results = analyzer.analyze_file(filename)
        all_results.update(results)
    
    # Gerar correções
    if all_results:
        logger.info("\n" + "="*60)
        logger.info("📊 RESULTADOS DA ANÁLISE")
        logger.info("="*60)
        
        corrections = analyzer.generate_corrected_asserts(all_results)
        
        logger.info("💡 Static_assert corrigidos sugeridos:")
        for correction in corrections:
            if correction.strip():
                logger.info(f"  {correction}")
        
        # Salvar em arquivo
        with open("SDK_Offset_Corrections.txt", "w") as f:
            f.write("// Static_assert corrigidos baseado na análise do SDK\n\n")
            for correction in corrections:
                f.write(correction + "\n")
        
        logger.info(f"\n📄 Correções salvas em: SDK_Offset_Corrections.txt")
    else:
        logger.warning("Nenhuma estrutura foi analisada com sucesso")

if __name__ == "__main__":
    main()
