#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <utility>
#include <filesystem>

namespace fs = std::filesystem;

//---------------------------------------------------------------------
// 		🔧	SDKFixer
//---------------------------------------------------------------------
class SDKFixer {
private:
    std::string sdk_folder;
    std::vector<std::string> files_to_fix;

public:
    //---------------------------------------------------------------------
    // 		⚙️	SDKFixer
    //---------------------------------------------------------------------
    SDKFixer() {
        sdk_folder = fs::current_path().string() + "\\Game\\SDK\\SDK"; // Caminho para a pasta SDK

        files_to_fix = { // Lista de arquivos que precisam ser corrigidos
            "GameplayTags_structs.hpp",
            "PhysicsCore_classes.hpp"
        };
    }

    //---------------------------------------------------------------------
    // 		🔧	fix_sdk
    //---------------------------------------------------------------------
    void fix_sdk() {
        for (const auto& filename : files_to_fix) {
            std::string filepath = sdk_folder + "\\" + filename;

            if (!fs::exists(filepath)) { // Verificar se o arquivo existe
                std::cout << "Couldn't find " << filepath << " - skipping" << std::endl;
                continue;
            }

            std::string code;
            {
                std::ifstream file(filepath); // Ler o conteúdo do arquivo
                if (file.is_open()) {
                    std::string line;
                    while (std::getline(file, line)) {
                        code += line + "\n";
                    }
                    file.close();
                }
                else {
                    std::cout << "Failed to open " << filepath << " for reading" << std::endl;
                    continue;
                }
            }

            if (filename.find("GameplayTags") != std::string::npos) { // Aplicar correções específicas
                code = Fix_GameplayTags(code);
            }
            else if (filename.find("PhysicsCore") != std::string::npos) {
                code = Fix_PhysicsCore(code);
            }

            {
                std::ofstream file(filepath); // Escrever o conteúdo modificado
                if (file.is_open()) {
                    file << code;
                    file.close();
                    std::cout << "Fixed " << filename << std::endl;
                }
                else {
                    std::cout << "Failed to open " << filepath << " for writing" << std::endl;
                }
            }
        }
    }

    //---------------------------------------------------------------------
    // 		🔧	Fix_PhysicsCore
    //---------------------------------------------------------------------
    std::string Fix_PhysicsCore(std::string code) {
        std::vector<std::pair<std::string, std::string>> fixes = {
            {"class UChaosServerCollisionDebugSubsystem final : public UTickableWorldSubsystem",
             "class UChaosServerCollisionDebugSubsystem final : public UObject"},
            {"static_assert(sizeof(UChaosServerCollisionDebugSubsystem)",
             "//static_assert(sizeof(UChaosServerCollisionDebugSubsystem)"}
        };

        for (const auto& [broken, fixed] : fixes) {
            size_t pos = 0;
            while ((pos = code.find(broken, pos)) != std::string::npos) { // Substituir ocorrências
                code.replace(pos, broken.length(), fixed);
                pos += fixed.length();
            }
        }

        return code;
    }

    //---------------------------------------------------------------------
    // 		🔧	Fix_GameplayTags
    //---------------------------------------------------------------------
    std::string Fix_GameplayTags(std::string code) {
        std::vector<std::pair<std::string, std::string>> fixes = {
            {"struct FGameplayTagTableRow : public FTableRowBase",
             "struct FGameplayTagTableRow //: public FTableRowBase"},
            {"static_assert(sizeof(FGameplayTagTableRow)",
             "//static_assert(sizeof(FGameplayTagTableRow)"},
            {"static_assert(offsetof(FGameplayTagTableRow, Tag)",
             "//static_assert(offsetof(FGameplayTagTableRow, Tag)"},
            {"static_assert(offsetof(FGameplayTagTableRow, DevComment)",
             "//static_assert(offsetof(FGameplayTagTableRow, DevComment)"},
            {"static_assert(sizeof(FRestrictedGameplayTagTableRow)",
             "//static_assert(sizeof(FRestrictedGameplayTagTableRow)"},
            {"static_assert(offsetof(FRestrictedGameplayTagTableRow, bAllowNonRestrictedChildren)",
             "//static_assert(offsetof(FRestrictedGameplayTagTableRow, bAllowNonRestrictedChildren)"},
            {"static_assert(alignof(FGameplayTagContainer)",
             "//static_assert(alignof(FGameplayTagContainer)"}
        };

        for (const auto& [broken, fixed] : fixes) {
            size_t pos = 0;
            while ((pos = code.find(broken, pos)) != std::string::npos) { // Substituir ocorrências
                code.replace(pos, broken.length(), fixed);
                pos += fixed.length();
            }
        }

        return code;
    }
};

//---------------------------------------------------------------------
// 		⚙️	main
//---------------------------------------------------------------------
int main() {
    std::cout << "Starting SDK Fix..." << std::endl;
    SDKFixer fixer;
    fixer.fix_sdk();
    std::cout << "SDK Fix completed!" << std::endl;
    return 0;
}
