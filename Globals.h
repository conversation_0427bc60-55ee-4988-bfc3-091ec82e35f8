#ifndef COMMONS_H
#define COMMONS_H

#include <string>
#include <unordered_map>
#include <xlocbuf>
#include <codecvt>
#include <ctime>
#include <cstdlib>
#include <windows.h>
#include <stdexcept>
#include <mutex>
#include <atomic>
#include <chrono>
#include <fstream>
#include <sstream>

// Enum para temas da interface
enum class UITheme
{
    DarkBlueGold = 0,  // Azul escuro com detalhes dourados (padrão)
    OrangeGold = 1     // Laranja/dourado (secundário)
};

// Sistema de log para depuração
namespace SafetySystem
{
    // Controle de acesso thread-safe
    std::mutex logMutex;
    std::atomic<bool> safeMode{false};
    std::atomic<int> crashCount{0};
    std::atomic<int64_t> lastCrashTime{0};

    // Obter o timestamp atual em milissegundos
    int64_t GetCurrentTimeMs()
    {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
                   std::chrono::system_clock::now().time_since_epoch())
            .count();
    }

    // Registrar um erro no arquivo de log com informações detalhadas
    void LogError(const char *functionName, const char *errorMessage)
    {
        std::lock_guard<std::mutex> lock(logMutex);

        try
        {
            std::ofstream logFile("error_log.txt", std::ios::app);
            if (logFile.is_open())
            {
                auto now = std::chrono::system_clock::now();
                auto now_c = std::chrono::system_clock::to_time_t(now);

                std::tm tm_buf;
                localtime_s(&tm_buf, &now_c);

                char timeBuffer[80];
                strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", &tm_buf);

                logFile << "[" << timeBuffer << "] ERROR in " << functionName << ": " << errorMessage << std::endl;

                logFile.close();
            }
        }
        catch (...)
        {
            // Falha silenciosa se não conseguir logar
        }
    }

    // Registrar um crash e verificar se devemos entrar em modo seguro
    void RegisterCrash(const char *functionName, const char *errorMessage)
    {
        LogError(functionName, errorMessage);

        // Incrementar contador de crashes consecutivos
        crashCount++;

        // Se tivermos muitos crashes consecutivos, ativar modo seguro
        if (crashCount >= 3)
        {
            safeMode = true;
            LogError("SafetySystem", "SAFE MODE ATIVADO - Muitos crashes consecutivos detectados");
        }
    }

    // Registrar sucesso e resetar contador se sistema estiver funcionando
    void RegisterSuccess()
    {
        // Se estava em safe mode e agora está funcionando, desativar
        if (safeMode.load() && crashCount > 0)
        {
            safeMode = false;
            crashCount = 0;
            LogError("SafetySystem", "SAFE MODE DESATIVADO - Sistema validado e funcionando");
        }
        else if (crashCount > 0)
        {
            // Reduzir contador gradualmente quando há sucessos
            crashCount--;
        }
    }

    // Verificar se estamos em modo seguro
    bool IsSafeMode()
    {
        return safeMode.load();
    }

    // Desativar modo seguro manualmente
    void DisableSafeMode()
    {
        safeMode = false;
        crashCount = 0;
        LogError("SafetySystem", "SAFE MODE DESATIVADO manualmente");
    }

    // Função para logar informações de debug durante execução
    void LogInfo(const char *functionName, const char *message)
    {
        std::lock_guard<std::mutex> lock(logMutex);

        try
        {
            std::ofstream logFile("debug_log.txt", std::ios::app);
            if (logFile.is_open())
            {
                auto now = std::chrono::system_clock::now();
                auto now_c = std::chrono::system_clock::to_time_t(now);

                std::tm tm_buf;
                localtime_s(&tm_buf, &now_c);

                char timeBuffer[80];
                strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", &tm_buf);

                logFile << "[" << timeBuffer << "] INFO in " << functionName << ": " << message << std::endl;
                logFile.close();
            }
        }
        catch (...)
        {
            // Falha silenciosa se não conseguir logar
        }
    }

    // Executar uma função com tratamento de exceção
    template <typename Func>
    bool SafeExecute(const char *functionName, Func &&func)
    {
        if (IsSafeMode())
        {
            // Em modo seguro, executamos apenas funções críticas
            if (strstr(functionName, "Critical") == nullptr)
            {
                return false;
            }
        }

        __try
        {
            func();
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Capturar informações detalhadas da exceção
            DWORD exceptionCode = GetExceptionCode();

            // Criar mensagem detalhada do erro
            char detailedError[512];

            // Identificar o tipo de exceção
            switch (exceptionCode)
            {
                case EXCEPTION_ACCESS_VIOLATION:
                    sprintf_s(detailedError, "ACCESS_VIOLATION (0x%08X) - Invalid memory access", exceptionCode);
                    break;
                case EXCEPTION_ARRAY_BOUNDS_EXCEEDED:
                    sprintf_s(detailedError, "ARRAY_BOUNDS_EXCEEDED (0x%08X)", exceptionCode);
                    break;
                case EXCEPTION_STACK_OVERFLOW:
                    sprintf_s(detailedError, "STACK_OVERFLOW (0x%08X)", exceptionCode);
                    break;
                case EXCEPTION_INT_DIVIDE_BY_ZERO:
                    sprintf_s(detailedError, "DIVIDE_BY_ZERO (0x%08X)", exceptionCode);
                    break;
                case EXCEPTION_ILLEGAL_INSTRUCTION:
                    sprintf_s(detailedError, "ILLEGAL_INSTRUCTION (0x%08X)", exceptionCode);
                    break;
                case EXCEPTION_BREAKPOINT:
                    sprintf_s(detailedError, "BREAKPOINT (0x%08X)", exceptionCode);
                    break;
                case EXCEPTION_SINGLE_STEP:
                    sprintf_s(detailedError, "SINGLE_STEP (0x%08X)", exceptionCode);
                    break;
                default:
                    sprintf_s(detailedError, "UNKNOWN_EXCEPTION (0x%08X)", exceptionCode);
                    break;
            }

            RegisterCrash(functionName, detailedError);
            return false;
        }
    }
}

namespace mods
{
    bool aimbot = true;

    // Sistema de suavização
    float smoothAmount = 10.0f;             // Fator de suavização geral (1.0-20.0) - Quanto maior, mais suave
    bool separatePitchYawSmoothing = false; // Ativar/desativar controle separado de Pitch e Yaw
    float smoothAmountPitch = 10.0f;        // Fator de suavização para movimento vertical (Pitch)
    float smoothAmountYaw = 10.0f;          // Fator de suavização para movimento horizontal (Yaw)
    bool useNeckTarget = true;              // Alternar entre head (false) e neck (true)

    // Sistema de inércia
    bool useInertia = false;       // Ativar/desativar sistema de inércia
    float inertiaFactor = 0.5f;    // Fator de inércia (0.0-1.0) - Quanto maior, mais gradual o movimento

    // Sistema de delay
    bool useAimDelay = false;      // Ativar/desativar delay na aquisição de alvos
    float aimDelayTime = 0.2f;     // Tempo de delay em segundos
    float aimDelayAmount = 0.5f;   // Quantidade de delay (0.0-1.0)

    // Sistema de movimento adaptativo
    bool useAdaptiveMovement = false;       // Ativar/desativar movimento adaptativo
    float initialSmoothAmount = 10.0f;      // Valor inicial de smoothing
    float minSmoothAmount = 2.0f;           // Valor mínimo de smoothing
    float adaptiveDuration = 1.0f;          // Duração em segundos para atingir o valor mínimo
    bool separateAdaptiveSettings = false;  // Configurações adaptativas separadas para pitch e yaw
    float initialSmoothAmountPitch = 10.0f; // Valor inicial de smoothing para pitch
    float minSmoothAmountPitch = 2.0f;      // Valor mínimo de smoothing para pitch
    float initialSmoothAmountYaw = 10.0f;   // Valor inicial de smoothing para yaw
    float minSmoothAmountYaw = 2.0f;        // Valor mínimo de smoothing para yaw
    bool useAdaptiveInertia = false;        // Ativar/desativar inércia adaptativa
    float initialInertiaFactor = 0.5f;      // Valor inicial do fator de inércia
    float minInertiaFactor = 0.1f;          // Valor mínimo do fator de inércia

    //---------------------------------------------------------------------
    // 		⚡	Sistema Flick Simplificado e Autônomo
    //---------------------------------------------------------------------
    bool useFlick = false;                  // Ativar/desativar sistema Flick
    float flickDuration = 1.0f;             // Duração do Flick em segundos (0.1-3.0)
    int flickReductionType = 0;             // Tipo de redução: 0=Linear, 1=Exponencial, 2=Quadrático

    // Variáveis internas de controle (não salvas no perfil)
    bool flickIsActive = false;             // Estado atual do Flick
    float flickStartTime = 0.0f;            // Tempo quando o Flick foi iniciado
    bool lastAimbotKeyState = false;        // Estado anterior da tecla para detectar pressionamento

    // Sistema de humanização (melhorado baseado no projeto de exemplo)
    int targetBodyPartMode = 0;     // 0 = Cabeça, 1 = Pescoço, 2 = Peito, 3 = Coluna, 4 = Pelvis, 5 = Aleatório, 6 = Humanizado
    int currentRandomBodyPart = 0;  // Parte do corpo atual para modo aleatório
    int humanizedBodyPart = 1;      // Parte do corpo atual para modo humanizado (inicializado com pescoço para evitar sempre começar com cabeça)
    bool useHumanizedTargeting = true; // Ativar/desativar sistema de humanização
    bool humanizedBodyPartInitialized = false; // Flag de inicialização da parte do corpo humanizada

    // Probabilidades mais equilibradas (baseado no projeto de exemplo)
    float headProbability = 0.25f;   // Probabilidade de mirar na cabeça (25%)
    float neckProbability = 0.25f;   // Probabilidade de mirar no pescoço (25%)
    float chestProbability = 0.25f;  // Probabilidade de mirar no peito (25%)
    float spineProbability = 0.15f;  // Probabilidade de mirar na coluna (15%)
    float pelvisProbability = 0.10f; // Probabilidade de mirar na pelvis (10%)

    // Sistema de intervalos dinâmicos (baseado no projeto de exemplo)
    float lastBodyPartUpdateTime = 0.0f; // Último tempo de atualização da parte do corpo
    float bodyPartUpdateInterval = 1.0f; // Intervalo de atualização da parte do corpo em segundos (modo aleatório)
    float bodyPartChangeMinTime = 0.1f;  // Tempo mínimo entre mudanças de parte do corpo (modo humanizado)
    float bodyPartChangeMaxTime = 0.2f;  // Tempo máximo entre mudanças de parte do corpo (modo humanizado)
    float lastTargetBodyPartChangeTime = 0.0f; // Último tempo de mudança da parte do corpo alvo

    // Configuração de Glow (contorno através de paredes)
    bool enableGlow = false; // Ativar/desativar o efeito de glow

    // Configuração para exibir porcentagem de ultimate
    bool showUltimatePercentage = false; // Exibir porcentagem de carga das ultimates inimigas

    // Configuração de Role Target
    bool enableRoleTarget = false; // Ativar/desativar o filtro por role
    int targetRole = 0;            // 0 = None (todos), 1 = Tank, 2 = Damage, 3 = Support

    // Team Target Mode (para suportes)
    bool teamTargetMode = false;   // false = mirar em inimigos (padrão), true = mirar em aliados (suporte)
    int teamTargetKey = 0;         // None por padrão (sem tecla associada)
    int gamepadTeamTargetButton = 0; // None por padrão (sem botão associado)

    // Support Vision Mode (ignora FOV para aimbot)
    bool isSupportVisionEnabled = false; // Quando ativado, o aimbot considera alvos fora do FOV

    // First Enemy Lock (trava no primeiro inimigo selecionado)
    bool firstEnemyLock = false; // Quando ativado, não troca de alvo enquanto o primeiro estiver no FOV

    // Target Invisible Enemies (permitir mirar em inimigos invisíveis)
    bool targetInvisibleEnemies = true; // Quando ativado, permite mirar em alvos invisíveis

    // Flying Target Priority (prioriza alvos voando)
    bool shouldPrioritizeFlyingTargets = false; // Opção para priorizar alvos voadores
    float flyingVelocityThreshold = 50.0f; // Limiar de velocidade vertical para considerar um alvo como voador

    // TEMPORARIAMENTE DESABILITADO: Summons/Constructs Priority (prioriza summons e construtos)
    bool shouldPrioritizeSummons = false;    // TEMPORARIAMENTE DESABILITADO: Opção para priorizar summons base
    bool shouldPrioritizeConstructs = false; // TEMPORARIAMENTE DESABILITADO: Opção para priorizar construtos de habilidade

    // Configuração de idioma
    int currentLanguage = 0; // 0 = English, 1 = Portuguese, 2 = Spanish, 3 = French, 4 = Russian, 5 = Italian

    // Configuração de tema da interface
    int currentTheme = 0; // 0 = Dark Blue with Gold (padrão), 1 = Orange/Gold (secundário)

    // Configuração de zoom da interface
    bool enableUIZoom = true;       // Ativar/desativar zoom da interface
    float uiZoomFactor = 1.5f;      // Fator de zoom da interface (1.0 = 100%)
    float uiZoomStep = 0.1f;        // Incremento/decremento do zoom por passo
    float uiZoomMin = 1.0f;         // Zoom mínimo (100%)
    float uiZoomMax = 3.0f;         // Zoom máximo (300%)

    // Cores para o ESP (tema laranja/dourado) - Alpha 252 para compatibilidade AMD
    ImColor HealthBarColor = ImColor(255, 140, 0, 252);  // Cor da barra de saúde (laranja)
    ImColor HealthColor = ImColor(255, 215, 0, 252);     // Cor do texto de saúde (dourado)
    ImColor DistanceColor = ImColor(255, 165, 0, 252);   // Cor do texto de distância (laranja claro)

    bool aimbotFovCircle = false;
    bool fov_circle = false; // Para compatibilidade com o sistema de configurações
    int fov = 15;
    int actualfovcircle = 0;

    // BulletTP
    bool bullet_tp = true;
    int bullet_tp_target = 2;            // 0 = Head, 1 = Neck, 2 = Chest, 3 = Spine, 4 = Pelvis, 5 = Random
    bool bullet_tp_fov_circle = false;   // Para habilitar/desabilitar o círculo do FOV do Bullet TP
    int bullet_tp_fov = 4;               // Valor do FOV do Bullet TP


    // Controle avançado do BulletTP
    bool bulletTPWasKeyPressed = false;  // Flag para rastrear se a tecla estava pressionada no frame anterior
    float bulletTPKeyUpTime = 0.0f;      // Tempo em que a tecla foi liberada
    bool bulletTPContinueAfterKeyUp = false; // Continuar redirecionando após soltar a tecla
    float bulletTPKeyUpDuration = 1.0f;  // Duração em segundos para continuar redirecionando após soltar a tecla
    bool bulletTPDebug = false;          // Ativar logs de depuração para o BulletTP

    // Sistema de detecção de dano do BulletTP
    bool bulletTPShowDamageInfo = true;  // Mostrar informações de dano causado pelo BulletTP
    int bulletTPHitCount = 0;            // Contador de acertos do BulletTP
    int bulletTPMissCount = 0;           // Contador de erros do BulletTP
    float bulletTPLastHitTime = 0.0f;    // Tempo do último acerto
    float bulletTPLastCheckTime = 0.0f;  // Tempo da última verificação de dano
    float bulletTPDamageCheckInterval = 0.2f; // Intervalo de verificação de dano em segundos

    // Keybind and gamepad variables (added for build error fix)
    int aimbot_key = 1;                 // LeftMouseButton por padrão
    int bullet_tp_key = 1;              // LeftMouseButton por padrão
    int gamepad_aimbot_button = 7;      // GamepadRightTrigger por padrão
    int gamepad_bullet_tp_button = 7;   // GamepadRightTrigger por padrão
    int actual_bullet_tp_fovcircle = 0; // Raio do círculo do FOV do Bullet TP (calculado)
    bool esp = false;
    bool fov_changer = false;
    int fov_changer_amount = 120;
    bool VisCheck = true;
    bool UseLineOfSight = false; // Usar LineOfSight para verificar visibilidade
    bool LocalCheck = false;
    bool focusLowestHealth = false; // focar no inimigo com menos vida
    bool TeamCheck = true;

    bool ShowHealth = false;
    bool ShowDistance = false;

    // 🎨 Sistema ESP Moderno - Configurações
    bool showBoxes = true;
    int boxType = 0; // 0=2D, 1=Cornered, 2=Filled
    bool boxOutline = true;
    float boxThickness = 1.0f;

    bool showHealthBar = true;
    int healthBarPosition = 0; // 0=Left, 1=Right, 2=Top, 3=Bottom

    bool showSkeleton = false;
    float skeletonThickness = 1.0f;

    bool showPlayerNames = true;

    bool showCrosshair = false;
    int crosshairType = 0; // 0=Dot, 1=Cross, 2=Circle
    float crosshairSize = 5.0f;
    float crosshairThickness = 1.0f;
    ImU32 crosshairColor = IM_COL32(255, 255, 255, 255);

    bool showSnapLines = false;
    float maxESPDistance = 500.0f;

    bool showSummonESP = true; // Controlar exibição de ESP para summons/construtos
    bool TracerLines = false;

    template <typename T>
    T Clamp(T value, T min, T max)
    {
        if (value < min)
            return min;
        if (value > max)
            return max;
        return value;
    }

    // Gera um valor aleatório entre min e max
    inline float RandomFloat(float min, float max)
    {
        // Usar o tempo atual como semente para aleatoriedade
        static unsigned int seed = static_cast<unsigned int>(time(nullptr));
        seed = (seed * 1103515245 + 12345) & 0x7fffffff;
        float randValue = static_cast<float>(seed) / static_cast<float>(0x7fffffff);
        return min + randValue * (max - min);
    }

    // Função simplificada para alternar aleatoriamente entre head/neck
    inline void UpdateNeckTargeting(float deltaTime)
    {
        // 10% de chance de alternar o alvo a cada segundo
        if (RandomFloat(0.0f, 1.0f) < 0.1f * deltaTime)
        {
            // 50% de chance de mirar na cabeça, 50% no pescoço
            useNeckTarget = (RandomFloat(0.0f, 1.0f) < 0.5f);
        }
    }

    // Função para atualizar a parte do corpo alvo com base no modo selecionado (melhorada baseada no projeto de exemplo)
    inline void UpdateTargetBodyPart(float currentTime)
    {
        // Modo aleatório (5) - alternar entre partes do corpo a cada intervalo variável
        if (targetBodyPartMode == 5)
        {
            // Tempo para mudar a parte do corpo alvo? (intervalo variável de 2-4 segundos)
            if (currentTime - lastTargetBodyPartChangeTime > RandomFloat(2.0f, 4.0f))
            {
                // Escolher aleatoriamente entre cabeça (0), pescoço (1), peito (2), coluna (3) e pelvis (4)
                currentRandomBodyPart = static_cast<int>(RandomFloat(0.0f, 4.99f));
                lastTargetBodyPartChangeTime = currentTime;

                // Sem logs de debug para mudanças de parte do corpo (evitar spam)
            }
        }
        // Modo humanizado (6) - usar sistema de probabilidade configurável
        else if (targetBodyPartMode == 6)
        {
            // Sistema de humanização ativado?
            if (!useHumanizedTargeting)
            {
                // Auto-ativar se desativado
                useHumanizedTargeting = true;
            }

            // Forçar inicialização na primeira execução
            if (!humanizedBodyPartInitialized)
            {
                // Selecionar parte do corpo inicial usando distribuição de probabilidade
                float randomValue = RandomFloat(0.0f, 1.0f);
                float cumulativeProbability = 0.0f;

                // Normalizar probabilidades (soma=1.0)
                float totalProbability = headProbability + neckProbability + chestProbability + spineProbability + pelvisProbability;
                if (totalProbability <= 0.0f)
                {
                    // Evitar divisão por zero, usar padrões
                    headProbability = 0.25f;
                    neckProbability = 0.25f;
                    chestProbability = 0.25f;
                    spineProbability = 0.15f;
                    pelvisProbability = 0.10f;
                    totalProbability = 1.0f;
                }

                // Cabeça
                cumulativeProbability += headProbability / totalProbability;
                if (randomValue <= cumulativeProbability)
                {
                    humanizedBodyPart = 0; // Cabeça
                }
                // Pescoço
                else
                {
                    cumulativeProbability += neckProbability / totalProbability;
                    if (randomValue <= cumulativeProbability)
                    {
                        humanizedBodyPart = 1; // Pescoço
                    }
                    // Peito
                    else
                    {
                        cumulativeProbability += chestProbability / totalProbability;
                        if (randomValue <= cumulativeProbability)
                        {
                            humanizedBodyPart = 2; // Peito
                        }
                        // Coluna
                        else
                        {
                            cumulativeProbability += spineProbability / totalProbability;
                            if (randomValue <= cumulativeProbability)
                            {
                                humanizedBodyPart = 3; // Coluna
                            }
                            // Pelvis
                            else
                            {
                                humanizedBodyPart = 4; // Pelvis
                            }
                        }
                    }
                }

                lastTargetBodyPartChangeTime = currentTime;
                humanizedBodyPartInitialized = true;

                // Log de inicialização (apenas uma vez)
                printf("Sistema humanizado inicializado: Parte do corpo inicial = %d\n", humanizedBodyPart);
            }
            // Tempo para mudar a parte do corpo alvo? (intervalo variável configurável)
            else if (currentTime - lastTargetBodyPartChangeTime > RandomFloat(bodyPartChangeMinTime, bodyPartChangeMaxTime))
            {
                // Armazenar a parte do corpo anterior para logging
                int previousBodyPart = humanizedBodyPart;

                // Usar distribuição de probabilidade configurável para selecionar a parte do corpo
                float randomValue = RandomFloat(0.0f, 1.0f);
                float cumulativeProbability = 0.0f;

                // Normalizar probabilidades (soma=1.0)
                float totalProbability = headProbability + neckProbability + chestProbability + spineProbability + pelvisProbability;
                if (totalProbability <= 0.0f)
                {
                    // Evitar divisão por zero, usar padrões equilibrados
                    headProbability = 0.20f;
                    neckProbability = 0.20f;
                    chestProbability = 0.20f;
                    spineProbability = 0.20f;
                    pelvisProbability = 0.20f;
                    totalProbability = 1.0f;
                }

                // Cabeça
                cumulativeProbability += headProbability / totalProbability;
                if (randomValue <= cumulativeProbability)
                {
                    humanizedBodyPart = 0; // Cabeça
                }
                // Pescoço
                else
                {
                    cumulativeProbability += neckProbability / totalProbability;
                    if (randomValue <= cumulativeProbability)
                    {
                        humanizedBodyPart = 1; // Pescoço
                    }
                    // Peito
                    else
                    {
                        cumulativeProbability += chestProbability / totalProbability;
                        if (randomValue <= cumulativeProbability)
                        {
                            humanizedBodyPart = 2; // Peito
                        }
                        // Coluna
                        else
                        {
                            cumulativeProbability += spineProbability / totalProbability;
                            if (randomValue <= cumulativeProbability)
                            {
                                humanizedBodyPart = 3; // Coluna
                            }
                            // Pelvis
                            else
                            {
                                humanizedBodyPart = 4; // Pelvis
                            }
                        }
                    }
                }

                lastTargetBodyPartChangeTime = currentTime;

                // Sem logs de debug para mudanças de parte do corpo (evitar spam)
            }
        }
    }
}

namespace Keys
{
    struct KeyInfo
    {
        std::string name;
        SDK::FKey fkey;
        int keyCode;
    };

    SDK::FKey Insert;
    SDK::FKey CurrentAimbotKey;
    SDK::FKey CurrentBulletTPKey;
    SDK::FKey CurrentTeamTargetKey; // Tecla para alternar o modo de alvo (inimigos/aliados)
    SDK::FKey GamepadButtonNone;    // Tecla "None" para representar ausência de tecla
    SDK::FKey GamepadButtonA;
    SDK::FKey GamepadButtonB;
    SDK::FKey GamepadButtonX;
    SDK::FKey GamepadButtonY;
    SDK::FKey GamepadDPadUp;
    SDK::FKey GamepadDPadDown;
    SDK::FKey GamepadDPadLeft;
    SDK::FKey GamepadDPadRight;
    SDK::FKey GamepadLeftShoulder;
    SDK::FKey GamepadRightShoulder;
    SDK::FKey GamepadLeftTrigger;
    SDK::FKey GamepadRightTrigger;
    SDK::FKey GamepadLeftThumb;
    SDK::FKey GamepadRightThumb;

    std::unordered_map<int, std::string> KeyNames = {
        {1, "LeftMouseButton"}, {2, "RightMouseButton"}, {4, "MiddleMouseButton"}, {8, "ThumbMouseButton"}, {9, "ThumbMouseButton2"}, {16, "Q"}, {17, "W"}, {18, "E"}, {19, "R"}, {20, "T"}, {21, "Y"}, {22, "U"}, {23, "I"}, {24, "O"}, {25, "P"}, {30, "A"}, {31, "S"}, {32, "D"}, {33, "F"}, {34, "G"}, {35, "H"}, {36, "J"}, {37, "K"}, {38, "L"}, {44, "Z"}, {45, "X"}, {46, "C"}, {47, "V"}, {48, "B"}, {49, "N"}, {50, "M"}, {57, "SpaceBar"}, {58, "LeftShift"}, {59, "LeftControl"}, {60, "LeftAlt"}, {61, "Tab"}};

    // Mapeamento correto de códigos VK_* (Virtual Key Codes do Windows) para nomes de teclas
    // Este mapa é usado para captura de teclas via ImGui que usa códigos VK_* internamente
    std::unordered_map<int, std::string> VKCodeToKeyName = {
        // Botões do mouse (para compatibilidade)
        {1, "LeftMouseButton"}, {2, "RightMouseButton"}, {4, "MiddleMouseButton"}, {5, "ThumbMouseButton"}, {6, "ThumbMouseButton2"},

        // Teclas especiais
        {8, "Backspace"}, {9, "Tab"}, {13, "Enter"}, {16, "LeftShift"}, {17, "LeftControl"}, {18, "LeftAlt"},
        {20, "CapsLock"}, {27, "Escape"}, {32, "SpaceBar"}, {33, "PageUp"}, {34, "PageDown"}, {35, "End"},
        {36, "Home"}, {45, "Insert"}, {46, "Delete"},

        // Teclas de seta
        {37, "LeftArrow"}, {38, "UpArrow"}, {39, "RightArrow"}, {40, "DownArrow"},

        // Números (0-9)
        {48, "0"}, {49, "1"}, {50, "2"}, {51, "3"}, {52, "4"}, {53, "5"}, {54, "6"}, {55, "7"}, {56, "8"}, {57, "9"},

        // Letras (A-Z)
        {65, "A"}, {66, "B"}, {67, "C"}, {68, "D"}, {69, "E"}, {70, "F"}, {71, "G"}, {72, "H"}, {73, "I"},
        {74, "J"}, {75, "K"}, {76, "L"}, {77, "M"}, {78, "N"}, {79, "O"}, {80, "P"}, {81, "Q"}, {82, "R"},
        {83, "S"}, {84, "T"}, {85, "U"}, {86, "V"}, {87, "W"}, {88, "X"}, {89, "Y"}, {90, "Z"},

        // Numpad
        {96, "Numpad0"}, {97, "Numpad1"}, {98, "Numpad2"}, {99, "Numpad3"}, {100, "Numpad4"},
        {101, "Numpad5"}, {102, "Numpad6"}, {103, "Numpad7"}, {104, "Numpad8"}, {105, "Numpad9"},
        {106, "NumpadMultiply"}, {107, "NumpadAdd"}, {109, "NumpadSubtract"}, {110, "NumpadDecimal"}, {111, "NumpadDivide"},

        // Teclas de função (F1-F12)
        {112, "F1"}, {113, "F2"}, {114, "F3"}, {115, "F4"}, {116, "F5"}, {117, "F6"},
        {118, "F7"}, {119, "F8"}, {120, "F9"}, {121, "F10"}, {122, "F11"}, {123, "F12"},

        // Teclas de pontuação e símbolos
        {186, "Semicolon"}, {187, "Equals"}, {188, "Comma"}, {189, "Minus"}, {190, "Period"}, {191, "Slash"},
        {192, "Tilde"}, {219, "LeftBracket"}, {220, "Backslash"}, {221, "RightBracket"}, {222, "Quote"}
    };

    // Mapeamento reverso: de códigos VK_* para códigos customizados (para compatibilidade com sistema existente)
    std::unordered_map<int, int> VKCodeToCustomCode = {
        // Botões do mouse
        {1, 1}, {2, 2}, {4, 4}, {5, 8}, {6, 9},

        // Teclas do teclado - mapeamento para códigos customizados existentes
        {32, 57}, // SpaceBar: VK_SPACE (32) -> código customizado 57
        {9, 61},  // Tab: VK_TAB (9) -> código customizado 61
        {16, 58}, // LeftShift: VK_SHIFT (16) -> código customizado 58
        {17, 59}, // LeftControl: VK_CONTROL (17) -> código customizado 59
        {18, 60}, // LeftAlt: VK_MENU (18) -> código customizado 60

        // Letras - mapeamento para códigos customizados existentes
        {65, 30}, {66, 48}, {67, 46}, {68, 32}, {69, 18}, {70, 33}, {71, 34}, {72, 35}, {73, 23},
        {74, 36}, {75, 37}, {76, 38}, {77, 50}, {78, 49}, {79, 24}, {80, 25}, {81, 16}, {82, 19},
        {83, 31}, {84, 20}, {85, 22}, {86, 47}, {87, 17}, {88, 45}, {89, 21}, {90, 44}
    };

    std::unordered_map<int, std::string> GamepadButtonNames = {
        {0, "None"}, {1, "B/Circle"}, {2, "X/Square"}, {3, "Y/Triangle"}, {4, "Left Bumper"}, {5, "Right Bumper"}, {6, "Left Trigger"}, {7, "Right Trigger"}, {8, "D-Pad Up"}, {9, "D-Pad Down"}, {10, "D-Pad Left"}, {11, "D-Pad Right"}, {12, "Left Thumb"}, {13, "Right Thumb"}};

    namespace KeyCache
    {
        std::unordered_map<int, SDK::FKey> FKeyCache;
    }

    // Função auxiliar para obter o nome de uma tecla a partir do código VK_*
    inline std::string GetKeyNameFromVKCode(int vkCode) {
        if (VKCodeToKeyName.count(vkCode)) {
            return VKCodeToKeyName[vkCode];
        }
        return "VK_" + std::to_string(vkCode);
    }

    // Função auxiliar para obter o nome de uma tecla a partir do código customizado
    inline std::string GetKeyNameFromCustomCode(int customCode) {
        if (KeyNames.count(customCode)) {
            return KeyNames[customCode];
        }
        return "Key_" + std::to_string(customCode);
    }

    // Função auxiliar para converter código VK_* para código customizado
    inline int ConvertVKCodeToCustomCode(int vkCode) {
        if (VKCodeToCustomCode.count(vkCode)) {
            return VKCodeToCustomCode[vkCode];
        }
        // Se não há mapeamento, retornar o código VK_* original
        return vkCode;
    }

    SDK::FKey *GetGamepadButtonByIndex(int index)
    {
        switch (index)
        {
        case 0:
            return &GamepadButtonNone; // Retornar "None" quando o índice é 0
        case 1:
            return &GamepadButtonB;
        case 2:
            return &GamepadButtonX;
        case 3:
            return &GamepadButtonY;
        case 4:
            return &GamepadLeftShoulder;
        case 5:
            return &GamepadRightShoulder;
        case 6:
            return &GamepadLeftTrigger;
        case 7:
            return &GamepadRightTrigger;
        case 8:
            return &GamepadDPadUp;
        case 9:
            return &GamepadDPadDown;
        case 10:
            return &GamepadDPadLeft;
        case 11:
            return &GamepadDPadRight;
        case 12:
            return &GamepadLeftThumb;
        case 13:
            return &GamepadRightThumb;
        default:
            return &GamepadButtonNone; // Retornar "None" para índices inválidos
        }
    }

    SDK::FKey GetFKeyFromKeyCode(int keyCode)
    {
        try {
            // Verificar se o código da tecla é 0 (None)
            if (keyCode == 0) {
                printf("Key code is 0 (None), returning GamepadButtonNone\n");
                return GamepadButtonNone; // Retornar a tecla "None" para código 0
            }

            // Verificar se o código da tecla é inválido (menor que 0)
            if (keyCode < 0) {
                printf("Invalid key code: %d, returning GamepadButtonNone\n", keyCode);
                return GamepadButtonNone; // Retornar a tecla "None" para códigos inválidos
            }

            // Verificar se a tecla já está no cache
            if (KeyCache::FKeyCache.count(keyCode))
            {
                // Verificar se a tecla no cache é válida
                SDK::FKey cachedKey = KeyCache::FKeyCache[keyCode];
                if (cachedKey.KeyName.ComparisonIndex != 0) {
                    return cachedKey;
                } else {
                    printf("Invalid cached key for code: %d, recreating\n", keyCode);
                    // Se a tecla no cache for inválida, removê-la do cache
                    KeyCache::FKeyCache.erase(keyCode);
                }
            }

            // Criar uma nova tecla
            SDK::FKey newFKey;

            // Tentar obter o nome da tecla usando diferentes métodos
            std::string keyName;
            if (KeyNames.count(keyCode)) {
                // Usar mapa de códigos customizados primeiro
                keyName = KeyNames[keyCode];
                printf("Using custom code mapping: %d -> %s\n", keyCode, keyName.c_str());
            } else if (VKCodeToKeyName.count(keyCode)) {
                // Usar mapa de códigos VK_* como fallback
                keyName = VKCodeToKeyName[keyCode];
                printf("Using VK code mapping: %d -> %s\n", keyCode, keyName.c_str());
            } else {
                // Usar nome genérico se não encontrar
                keyName = "Key_" + std::to_string(keyCode);
                printf("Using generic name for key code: %d -> %s\n", keyCode, keyName.c_str());
            }

            // Converter o nome da tecla para wstring
            std::wstring wKeyName(keyName.begin(), keyName.end());

            // Criar a FKey com tratamento de erros
            try {
                newFKey = SDK::FKey{
                    SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(wKeyName.c_str())),
                    0};
            } catch (...) {
                printf("Exception when creating FKey for key code: %d, using GamepadButtonNone\n", keyCode);
                // Em caso de erro, usar a tecla "None"
                newFKey = GamepadButtonNone;
            }

            // Adicionar a nova tecla ao cache
            KeyCache::FKeyCache[keyCode] = newFKey;
            printf("Created new FKey for key code: %d, name: %s\n", keyCode, keyName.c_str());
            return newFKey;
        } catch (const std::exception& e) {
            printf("Exception in GetFKeyFromKeyCode: %s\n", e.what());
            // Em caso de exceção, retornar a tecla "None"
            return GamepadButtonNone;
        } catch (...) {
            printf("Unknown exception in GetFKeyFromKeyCode\n");
            // Em caso de exceção desconhecida, retornar a tecla "None"
            return GamepadButtonNone;
        }
    }
}

// [Conte�do de Global.h e Custom.h omitido para brevidade]

namespace Variables
{
    UEngine *Engine = nullptr;
    UWorld *World = nullptr;
    APawn *AcknowledgedPawn = nullptr;
    ULocalPlayer *LocalPlayer = nullptr;
    UGameInstance *GameInstance = nullptr;
    APlayerController *PlayerController = nullptr;
    UGameViewportClient *ViewportClient = nullptr;
    APlayerCameraManager *PlayerCameraManager = nullptr;

    FVector CameraLocation = FVector();
    FRotator CameraRotation = FRotator();
    float FieldOfView = 0.f;

    FVector2D ScreenSize = FVector2D(1920, 1080);           // Valor inicial padrão
    FVector2D ScreenCenter = FVector2D(1920 / 2, 1080 / 2); // Valor inicial padrão
    bool ResolutionInitialized = false;                     // Flag para verificar se a resolução foi inicializada

    // Variável LastUpdateTime removida - usando GetWorldDeltaSeconds() agora

    // Sistema de rastreamento de alvos
    AMarvelBaseCharacter* LastTarget = nullptr;  // Último alvo rastreado
    AMarvelBaseCharacter* FirstLockedTarget = nullptr; // Primeiro alvo travado (first enemy lock)
    float TargetTrackingTime = 0.0f;            // Tempo total rastreando o alvo atual
    float TargetAcquisitionTime = 0.0f;         // Tempo desde que começamos a adquirir o alvo
    bool IsAcquiringTarget = false;             // Flag indicando se estamos em processo de aquisição
    SDK::FRotator LastRotation = SDK::FRotator(0, 0, 0);  // Última rotação aplicada
    SDK::FRotator CurrentVelocity = SDK::FRotator(0, 0, 0); // Velocidade atual de rotação
    float CurrentSmoothAmount = 10.0f;          // Valor atual de smoothing (adaptativo)
    float CurrentSmoothAmountPitch = 10.0f;     // Valor atual de smoothing para pitch
    float CurrentSmoothAmountYaw = 10.0f;       // Valor atual de smoothing para yaw
    float CurrentInertiaFactor = 0.5f;          // Valor atual do fator de inércia

    // Sistema de rastreamento de projéteis para BulletTP
    std::vector<uint64_t> TrackedProjectiles;   // Projéteis atualmente rastreados para redirecionamento
    float LastBulletTPTime = 0.0f;              // Último tempo em que o BulletTP foi executado

    // Sistema de rastreamento de alvo para detectar mudanças
    uint64_t CurrentBulletTPTarget = 0;          // Alvo atual do BulletTP (para detectar mudanças)
    float LastTargetChangeTime = 0.0f;          // Último tempo em que o alvo mudou

    // Sistema de detecção de dano para BulletTP
    float LastTargetHealth = 0.0f;              // Última saúde registrada do alvo
    bool HasTargetHealthChanged = false;        // Flag indicando se a saúde do alvo mudou
    float LastHealthChangeTime = 0.0f;          // Tempo da última mudança de saúde
    int TotalProjectilesFired = 0;              // Total de projéteis disparados
    int TotalProjectilesHit = 0;                // Total de projéteis que acertaram o alvo

    // Referência para a função de obtenção de informações de ultimate
    SDK::AThreatValueAdmin *ThreatValueAdmin = nullptr;

    // Função para atualizar a resolução da tela dinamicamente
    inline void UpdateScreenResolution(float width, float height)
    {
        if (width <= 0 || height <= 0)
            return;

        ScreenSize = FVector2D(width, height);
        ScreenCenter = FVector2D(width / 2, height / 2);
        ResolutionInitialized = true;
    }
}

// [Conteúdo de Global.h omitido para brevidade]

// A função safe_memory_check já está definida em main.h
// Declaração externa para uso neste arquivo
extern bool safe_memory_check(uintptr_t address);

// Sistema otimizado de verificação de ponteiros
bool IsValidPtr(const void *ptr)
{
    // Verificação básica de nulidade e endereço mínimo
    if (!ptr || reinterpret_cast<uintptr_t>(ptr) < 0x10000)
    {
        return false;
    }

    // Usar a verificação otimizada
    return safe_memory_check(reinterpret_cast<uintptr_t>(ptr));
}

//---------------------------------------------------------------------
// 		🎯	Verificação Específica para Ponteiros do Jogo
//---------------------------------------------------------------------
bool IsValidGamePtr(const void *ptr)
{
    // Verificação básica de nulidade
    if (!ptr)
        return false;

    // Verificar se o endereço está na faixa válida do jogo
    uintptr_t address = reinterpret_cast<uintptr_t>(ptr);
    if (address < 0x44000000)
        return false;

    // Usar safe_memory_check para verificar se o endereço é acessível
    return safe_memory_check(address);
}

// Função para leitura segura de memória
template<typename T>
inline T read_safe(uintptr_t address)
{
    if (!safe_memory_check(address))
        return T{};

    return *reinterpret_cast<T*>(address);
}

// Verificação segura para ponteiros de objetos do jogo - APRIMORADA
template <typename T>
bool IsValidObjectPtr(const T *ptr)
{
    // Verificação básica de ponteiro
    if (!IsValidPtr(ptr))
        return false;

    // Verificação adicional para objetos do jogo - acessar o vtable
    const void *vtable = read_safe<void*>(reinterpret_cast<uintptr_t>(ptr));
    if (vtable == nullptr || !IsValidPtr(vtable))
        return false;

    // 🚀 VALIDAÇÕES UOBJECT SEM dynamic_cast (assumindo que todos os ponteiros SDK são UObject)
    // Nota: Como não podemos usar dynamic_cast, assumimos que o ponteiro é um UObject válido
    // e fazemos verificações diretas de estrutura
    try
    {
        // Tentar acessar como UObject (cast direto sem verificação de tipo)
        const SDK::UObject* uobj = reinterpret_cast<const SDK::UObject*>(ptr);

        // Verificar se consegue acessar o campo Class sem crash
        if (!safe_memory_check(reinterpret_cast<uintptr_t>(&uobj->Class)))
            return false;

        // Verificar se o objeto tem um Class válido
        if (!IsValidPtr(uobj->Class))
            return false;

        // Verificar se consegue acessar o campo Outer sem crash
        if (!safe_memory_check(reinterpret_cast<uintptr_t>(&uobj->Outer)))
            return false;

        // Verificar se o objeto tem um Outer válido (pode ser nullptr para objetos de nível superior)
        if (uobj->Outer != nullptr && !IsValidPtr(uobj->Outer))
            return false;

        // Verificar se consegue acessar o campo Flags sem crash
        if (!safe_memory_check(reinterpret_cast<uintptr_t>(&uobj->Flags)))
            return false;

        // Verificar se o objeto não está marcado para destruição
        if ((uobj->Flags & static_cast<SDK::EObjectFlags>(0x00000200)) != 0) // RF_BeginDestroyed
            return false;

        if ((uobj->Flags & static_cast<SDK::EObjectFlags>(0x00000400)) != 0) // RF_FinishDestroyed
            return false;
    }
    catch (...)
    {
        // Se qualquer acesso falhar, o objeto não é um UObject válido
        return false;
    }

    // 🚀 VERIFICAÇÕES BÁSICAS PARA OBJETOS SDK (sem dynamic_cast)
    // Nota: SDK não suporta dynamic_cast pois classes não são polimórficas
    // Fazer verificações básicas de acesso à memória apenas

    return true;
}

//---------------------------------------------------------------------
// 		🔒	Validação Segura para Ponteiros SDK
//---------------------------------------------------------------------
template <typename T>
bool IsValidSDKPtr(const T *ptr)
{
    // Verificação básica de nulidade
    if (!ptr)
        return false;

    // Usar safe_memory_check para verificar se o ponteiro é acessível
    if (!safe_memory_check(reinterpret_cast<uintptr_t>(ptr)))
        return false;

    return true;
}

//---------------------------------------------------------------------
// 		📋	Validação Segura para Arrays SDK
//---------------------------------------------------------------------
template<typename T>
bool IsValidArrayAccess(const SDK::TArray<T>& array, int index)
{
    // Verificar se o array em si é válido
    if (!safe_memory_check(reinterpret_cast<uintptr_t>(&array)))
        return false;

    // Verificar bounds do array
    if (index < 0 || array.Num() <= index)
        return false;

    // Verificar se o elemento específico é acessível
    if (!safe_memory_check(reinterpret_cast<uintptr_t>(&array[index])))
        return false;

    return true;
}

//---------------------------------------------------------------------
// 		🏗️	Validação Hierárquica do Sistema de Jogo
//---------------------------------------------------------------------
bool ValidateGameHierarchy(SDK::UWorld* World, SDK::APlayerController*& OutController, SDK::APawn*& OutPawn)
{
    // Validar World (ponto único de falha)
    if (!IsValidObjectPtr(World))
        return false;

    // Validar OwningGameInstance
    if (!IsValidObjectPtr(World->OwningGameInstance))
        return false;

    // Validar acesso seguro ao primeiro LocalPlayer
    if (!IsValidArrayAccess(World->OwningGameInstance->LocalPlayers, 0))
        return false;

    // Obter PlayerController com validação
    OutController = World->OwningGameInstance->LocalPlayers[0]->PlayerController;
    if (!IsValidObjectPtr(OutController))
        return false;

    // Obter AcknowledgedPawn com validação
    OutPawn = OutController->AcknowledgedPawn;
    return IsValidObjectPtr(OutPawn);
}

//---------------------------------------------------------------------
// 		🗺️	Validação Específica para Mapas
//---------------------------------------------------------------------
bool IsValidForCurrentMap(UWorld* world)
{
    if (!IsValidObjectPtr(world))
        return false;

    // Verificações específicas por mapa para prevenir crashes
    try
    {
        // 1. Verificar GameState básico
        auto gameState = world->GameState;
        if (!IsValidObjectPtr(gameState))
            return false;

        // 2. Verificar OwningGameInstance
        auto gameInstance = world->OwningGameInstance;
        if (!IsValidObjectPtr(gameInstance))
            return false;

        // 3. Verificar se LocalPlayers existe e tem pelo menos 1 elemento
        if (gameInstance->LocalPlayers.Num() == 0 || !gameInstance->LocalPlayers.IsValidIndex(0))
            return false;

        // 4. Verificar primeiro LocalPlayer
        auto localPlayer = gameInstance->LocalPlayers[0];
        if (!IsValidObjectPtr(localPlayer))
            return false;

        // 5. Verificar PlayerController
        auto playerController = localPlayer->PlayerController;
        if (!IsValidObjectPtr(playerController))
            return false;

        return true;
    }
    catch (...)
    {
        SafetySystem::LogError("MapValidation", "Exceção durante validação específica do mapa");
        return false;
    }
}

//---------------------------------------------------------------------
// 		⚡	Validações Rápidas para Loops Críticos
//---------------------------------------------------------------------
bool IsValidPtr_Fast(const void *ptr)
{
    // Verificação básica de nulidade e endereço mínimo
    if (!ptr || reinterpret_cast<uintptr_t>(ptr) < 0x10000)
        return false;

    // Usar validação rápida sem SEH para evitar deadlocks em loops
    return safe_memory_check_fast(reinterpret_cast<uintptr_t>(ptr));
}

template <typename T>
bool IsValidObjectPtr_Fast(const T *ptr)
{
    // Verificação básica de ponteiro
    if (!IsValidPtr_Fast(ptr))
        return false;

    // 🚀 VERIFICAÇÕES RÁPIDAS BÁSICAS (sem dynamic_cast)
    // Nota: SDK não suporta dynamic_cast, fazer apenas verificações básicas de memória

    return true;
}

//---------------------------------------------------------------------
// 		🔄	Sistema de Loops Seguros
//---------------------------------------------------------------------
template<typename ArrayType, typename Func>
bool SafeArrayLoop(const char* loopName, const ArrayType& array, Func&& itemProcessor)
{
    // Verificar se o array é válido
    if (!safe_memory_check_fast(reinterpret_cast<uintptr_t>(&array)))
        return false;

    int arraySize = array.Num();
    if (arraySize <= 0 || arraySize > 10000) // Limite de segurança
        return false;

    // Usar safe_loop_execute para limitar iterações
    return safe_loop_execute(loopName, arraySize, [&]() {
        for (int i = 0; i < arraySize; i++) {
            if (!array.IsValidIndex(i))
                continue;

            // Verificação rápida do elemento
            auto element = array[i];
            if (!IsValidPtr_Fast(element))
                continue;

            // Processar elemento
            if (!itemProcessor(element, i))
                break; // Permitir saída antecipada
        }
    });
}

bool InScreen(SDK::FVector2D ScreenLocation)
{
    return ScreenLocation.X >= 0 && ScreenLocation.X <= Variables::ScreenSize.X &&
           ScreenLocation.Y >= 0 && ScreenLocation.Y <= Variables::ScreenSize.Y;
}

bool InRect(double Radius, SDK::FVector2D ScreenLocation)
{
    // Verificar se o ponto está dentro de um retângulo centrado na tela
    return Variables::ScreenCenter.X >= (Variables::ScreenCenter.X - Radius) && Variables::ScreenCenter.X <= (Variables::ScreenCenter.X + Radius) &&
           ScreenLocation.Y >= (ScreenLocation.Y - Radius) && ScreenLocation.Y <= (ScreenLocation.Y + Radius);
}

bool InCircle(double Radius, SDK::FVector2D ScreenLocation)
{
    double dx = Variables::ScreenCenter.X - ScreenLocation.X;
    double dy = Variables::ScreenCenter.Y - ScreenLocation.Y;
    return (dx * dx + dy * dy) <= (Radius * Radius);
}

#endif // COMMONS_H