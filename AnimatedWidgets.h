#ifndef ANIMATED_WIDGETS_H
#define ANIMATED_WIDGETS_H

#include "ImGui/imgui.h"
#include "ImGui/imgui_internal.h"
#include <unordered_map>
#include <chrono>

// Classe para gerenciar animações de widgets ImGui
class AnimatedWidgets
{
public:
    // Singleton
    static AnimatedWidgets &Get()
    {
        static AnimatedWidgets instance;
        return instance;
    }

    // Estrutura para armazenar o estado de animação de um checkbox
    struct CheckboxAnimation
    {
        float progress = 0.0f; // 0.0f = desativado, 1.0f = ativado
        std::chrono::time_point<std::chrono::steady_clock> lastUpdateTime;
        bool lastState = false;
    };

    // Estrutura para armazenar o estado de animação de uma aba
    struct TabAnimation
    {
        float progress = 0.0f;     // 0.0f = inativo, 1.0f = ativo
        float contentAlpha = 0.0f; // Alfa para animação de fade do conteúdo
        std::chrono::time_point<std::chrono::steady_clock> lastUpdateTime;
        bool isActive = false;
        bool wasActive = false; // Para detectar mudanças de estado
    };

    // Estrutura para armazenar o estado de animação da UI
    struct UIAnimation
    {
        float entryProgress = 0.0f; // 0.0f = fechado, 1.0f = aberto
        float exitProgress = 0.0f;  // 0.0f = não saindo, 1.0f = saindo
        std::chrono::time_point<std::chrono::steady_clock> lastUpdateTime;
        bool isVisible = false;
        bool wasVisible = false;
        bool isExiting = false;
    };

    // Checkbox animado
    bool AnimatedCheckbox(const char *label, bool *v);

    // Função para iniciar uma animação de aba
    void BeginTabBarAnimated(const char *str_id);

    // Função para renderizar uma aba animada
    bool BeginTabItemAnimated(const char *label, bool *p_open = nullptr, ImGuiTabItemFlags flags = 0);

    // Função para finalizar a barra de abas animada
    void EndTabBarAnimated();

    // Funções para animação de entrada da UI
    void BeginUIAnimation(bool isVisible);
    float GetUIAnimationProgress();
    bool ShouldRenderUI();

private:
    AnimatedWidgets() = default;
    ~AnimatedWidgets() = default;

    // Mapa para armazenar o estado de animação de cada checkbox
    std::unordered_map<ImGuiID, CheckboxAnimation> m_checkboxAnimations;

    // Mapa para armazenar o estado de animação de cada aba
    std::unordered_map<ImGuiID, TabAnimation> m_tabAnimations;

    // Estado de animação da UI
    UIAnimation m_uiAnimation;

    // ID da barra de abas atual
    ImGuiID m_currentTabBarId = 0;

    // Constantes de animação
    const float CHECKBOX_ANIMATION_SPEED = 4.0f; // Velocidade da animação do checkbox
    const float TAB_ANIMATION_SPEED = 6.0f;      // Velocidade da animação da aba
    const float TAB_CONTENT_FADE_SPEED = 8.0f;   // Velocidade do fade do conteúdo da aba
    const float UI_ENTRY_ANIMATION_SPEED = 5.0f; // Velocidade da animação de entrada da UI
    const float UI_EXIT_ANIMATION_SPEED = 8.0f;  // Velocidade da animação de saída da UI
};

#endif // ANIMATED_WIDGETS_H
