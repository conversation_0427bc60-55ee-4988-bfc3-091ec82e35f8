struct FMeshBoneInfo
	{
		FName Name;
		__int32 ParentIndex;
	};
 
struct USkeletalMeshComponent
	{
		uintptr_t Addr;
		uintptr_t SkeletalMesh;
		uintptr_t SkinnedAsset;
	};
 
	struct USkeletalMesh
	{
		uintptr_t Addr;
		TArrayDrink<FMeshBoneInfo> FinalRefBoneInfo;
	};
 
 
namespace BoneManager {
 
          std::map<std::string, std::vector<int>> heroBonesMap;
          std::unordered_map<std::string, std::vector<std::pair<int, int>>> skeletonConnectionsMap;
 
          static std::unordered_map<std::string, int> GetBoneInfos(uintptr_t ChildActor)
          {
              std::unordered_map<std::string, int> boneMap;
 
              if (ChildActor == 0) {
                  if (debug::debug_bone) debug::log("[BoneMngr] Invalid ChildActor (0)\n");
                  return boneMap;
              }
 
              USkeletalMeshComponent Mesh;
              Mesh.Addr = driver::read<uintptr_t>(ChildActor + offsets::MeshComponent);
 
              Mesh.SkeletalMesh = driver::read<uintptr_t>(Mesh.Addr + offsets::SkeletalMesh);
              Mesh.SkinnedAsset = driver::read<uintptr_t>(Mesh.Addr + offsets::SkinnedAsset);
 
              USkeletalMesh SkeletalMesh;
              SkeletalMesh.Addr = Mesh.SkeletalMesh ? Mesh.SkeletalMesh : Mesh.SkinnedAsset;
 
              driver::read_physical(SkeletalMesh.Addr + offsets::FinalRefBone, &SkeletalMesh.FinalRefBoneInfo, sizeof(TArrayDrink<FMeshBoneInfo>));
 
              if (SkeletalMesh.FinalRefBoneInfo.Count > 0x300 || !SkeletalMesh.FinalRefBoneInfo.Data)
                  SkeletalMesh.FinalRefBoneInfo.Count = 0;
 
              std::vector<FMeshBoneInfo> FinalRefBoneInfos(SkeletalMesh.FinalRefBoneInfo.Count);
              driver::read_physical(reinterpret_cast<uintptr_t>(SkeletalMesh.FinalRefBoneInfo.Data), FinalRefBoneInfos.data(), SkeletalMesh.FinalRefBoneInfo.Count * sizeof(FMeshBoneInfo));
 
              const std::vector<std::string> importantBones = {
                   "head", "neck_01", "spine_01", "pelvis", "upperarm_l", "lowerarm_l", "hand_l",
                  "upperarm_r", "lowerarm_r", "hand_r", "thigh_l", "calf_l", "foot_l",
                  "thigh_r", "calf_r", "foot_r"
              };
 
              for (int i = 0; i < static_cast<int>(FinalRefBoneInfos.size()); i++)
              {
                  int NameIndex = FinalRefBoneInfos[i].Name.ComparisonIndex;
                  std::string Name = GNames::GetNameById(NameIndex);
 
                  if (Name.substr(0, 2) == "0x") {
                      Name = Name.substr(2);
                  }
 
                  boneMap[Name] = i;
 
                  if (std::find(importantBones.begin(), importantBones.end(), Name) != importantBones.end()) {
                      if (debug::debug_bone) {
                          std::string logMessage = "[BoneMngr] Found bone: " + Name + " ID: " + std::to_string(i) + "\n";
                          debug::log(logMessage);
                      }
                  }
              }
 
              return boneMap;
          }
 
 
          static FVector GetBonePos(uintptr_t mesh, const std::string& boneName, const std::unordered_map<std::string, int>& boneMap) {
              auto it = boneMap.find(boneName);
              if (it == boneMap.end()) {
                  return FVector();
              }
              int id = it->second;
 
              std::cout << "BoneName in GetBone: " << boneName << std::endl;
              std::cout << "ID in GetBone: " << id << std::endl;
              uintptr_t BoneArray = driver::read<uintptr_t>(mesh + offsets::BoneArray);
              if (BoneArray == NULL)
                  BoneArray = driver::read<uintptr_t>(mesh + offsets::BoneArrayCache);
 
              FTransform bone = driver::read<FTransform>(BoneArray + (id * 0x30));
 
              FTransform ComponentToWorld = driver::read<FTransform>(mesh + offsets::ComponentToWorld);
              D3DMATRIX Matrix;
 
              Matrix = MatrixMultiplication(bone.ToMatrixWithScale(), ComponentToWorld.ToMatrixWithScale());
 
              return FVector(Matrix._41, Matrix._42, Matrix._43);
          }
 
    }