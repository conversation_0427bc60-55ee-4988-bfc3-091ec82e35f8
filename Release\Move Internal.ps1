#Requires -Version 5.1

param(
    [switch]$ShowPathSelection
)

<#
.SYNOPSIS
Automates the process of applying a mod DLL to Marvel Rivals, including backup, copy, and manifest update. Saves user preferences.

.DESCRIPTION
This script performs the following actions:
1. Loads/Saves user configuration (language, up to 3 custom game paths) to a JSON file.
2. If language is not saved, prompts the user for their preferred language (EN/PT).
3. Automatically uses the last saved path without prompting, unless the -ShowPathSelection parameter is used.
4. After confirming the game path, automatically closes the Marvel-Win64-Shipping.exe process if it's running.
5. Locates the source mod DLL (JocastaProtocol.vmp.dll).
6. Backs up the original game DLL (AkSilenceGenerator.dll) by renaming it.
7. Copies the mod DLL into the game directory, renaming it to replace the original.
8. Verifies and updates the game's version.json manifest with the new DLL's size and hash.
9. Optionally removes the amd_fidelityfx_dx12.dll file.
10. The "cooldown" (Start-Sleep) calls have been removed.
11. Na etapa 0, se existir um "último caminho utilizado", ele será usado automaticamente sem perguntar ao usuário.
12. Interface melhorada com cores, formatação e destaque visual para o último caminho utilizado.
13. Exibição clara do endereço de trabalho atual em destaque.
14. Contagem regressiva visual durante o timeout para seleção de caminho (quando mostrada).
15. Correção para evitar caminhos corrompidos no arquivo de configuração.
16. Ao encerrar o script, a tecla SPACEBAR reinicia o script automaticamente, e a tecla N reinicia com a tela de seleção de caminho.
17. Uso de caracteres normais para melhor compatibilidade em diferentes ambientes.

.NOTES
Author: [Starliedge/JocastaProtocol]
Version: 3.1
Date: 2024-08-01
Requires PowerShell 7.0 or later.
Ensure the script has write permissions in its own directory (for config) and the game directory.
Run as Administrator if you encounter permission errors.
User settings (language, up to 3 game paths) are saved in MR_Config.json next to the script.
#>

#region Script Configuration and Initialization

# --- Emojis ---
$emojiStep = "➡️"; $emojiSearch = "🔍"; $emojiSuccess = "✅"; $emojiWarning = "⚠️"; $emojiError = "❌"; $emojiInfo = "ℹ️"; $emojiQuestion = "❓"; $emojiInput = "👉"; $emojiFile = "📄"; $emojiFolder = "📁"; $emojiCopy = "✨"; $emojiRename = "🏷️"; $emojiDelete = "🗑️"; $emojiSave = "💾"; $emojiConfig = "⚙️"; $emojiSkip = "⏩"; $emojiBackup = "📦"; $emojiVerify = "🛡️"; $emojiLang = "🌐"; $emojiPlatform = "🎮"; $emojiConfirm = "👍"; $emojiFlagUS = "🇺🇸"; $emojiFlagBR = "🇧🇷"; $emojiWait = "⏳"; $emojiRocket = "🚀"

# --- Core File Names ---
$sourceDllBaseName = "JocastaProtocol.vmp.dll"
$dllOriginalName = "AkSilenceGenerator.dll"
$finalDllName = "AkSilenceGenerator.dll"

# --- Configuration File ---
if ($PSScriptRoot) {
    $scriptDir = $PSScriptRoot
}
else {
    try {
        $scriptDir = Split-Path $MyInvocation.MyCommand.Path -Parent -ErrorAction Stop
    }
    catch {
        Write-Host "❌ FATAL: Cannot determine script directory." -ForegroundColor Red
        Read-Host "Press Enter to exit..."
        exit
    }
}
$configFileName = "MR_Config.json"
$configFilePath = Join-Path $scriptDir $configFileName
$config = @{} # Initialize empty config hashtable

# --- Dynamic Path Variables ---
$jsonPath = $null
$dllOriginalPath = $null
$dllTargetPath = $null
$gameBaseDir = $null
$targetDllPath = $null
$amdDllPath = $null

# --- State Variables ---
$dllSourcePath = $null
$sourceDllFound = $false
$copySuccessful = $false

#endregion

#region Language Strings and Selection Functionality

# --- Language String Definitions ---
$langStrings_EN = @{
    ScriptTitle = "JocastaProtocol Installer"
    SelectLanguagePrompt = "Select Language / Selecione o Idioma:"
    LanguageSelected = "English selected."
    LanguageLoaded = "Language loaded from config: English"
    LoadConfig = "Loading configuration from '$configFileName'..."
    ConfigLoaded = "Configuration loaded."
    ConfigNotFound = "Configuration file not found. Will create one."
    SaveConfig = "Saving configuration..."
    ConfigSaved = "Configuration saved to '$configFileName'."
    ConfigSaveError = "ERROR saving configuration to '{0}'."

    SelectPlatformTitle = "Step 0: Confirm / Select Game Installation Path"
    SelectPlatformInfo = "Checking saved game paths..."
    PlatformSteam = "Steam"
    PlatformEpic = "Epic Games"
    PlatformCustom = "Create/Enter a NEW custom path"
    PlatformPrompt = "Choose a path number (or press ENTER for last path):"
    InvalidPlatform = "Invalid selection. Please enter a valid number."
    UsingPathFrom = "Using path from {0}:"
    CustomPathPrompt = "Please enter the FULL path to the Marvel Rivals BASE directory:"
    ValidatingPath = "Validating path..."
    InvalidCustomPath = "Path not found or is not a directory. Please try again."
    CustomPathConfirmed = "Custom path set."
    PathNotFound = "Default path not found."

    PathsDefinedTitle = "Paths Defined"
    PathsInfo = "The following paths will be used:"
    GameBasePath = "Game Base Path: {0}"
    JsonFile = "JSON File: {0}"
    DllTargetDir = "Game DLL Directory: {0}"
    DllOriginalFile = "Original DLL (for backup): {0}"
    DllFinalFile = "Final DLL (in game): {0}"
    AmdDllFile = "Optional AMD DLL: {0}"

    Step1Title = "Step 1: Locate Your Modded DLL ($sourceDllBaseName)"
    Step1Info = "Let's find the '$sourceDllBaseName' file you want to put into the game."
    SearchScriptDir = "Searching in this script's folder..."
    FoundInScriptDir = "Found here: {0}"
    NotFoundInScriptDir = "Not found in the script's folder."
    CannotFindScriptDir = "Couldn't determine the script's folder automatically."
    SearchCustomDir = "Searching in a specific folder..."
    PromptCustomDir = "Enter the FOLDER path where '$sourceDllBaseName' is located (or Enter to skip):"
    SkipDirSearch = "Folder search skipped."
    FoundInCustomDir = "Found in: {0}"
    DirExistsButNoFile = "'$sourceDllBaseName' is not in this folder."
    InvalidDir = "Invalid folder path."
    SearchFullPath = "Searching by full path..."
    PromptFullPath = "Enter the FULL PATH to the '$sourceDllBaseName' file (or Enter to skip):"
    SkipFileSearch = "File search skipped."
    FoundFullPath = "Found at: {0}"
    FileNameMismatch = "File name does not match '$sourceDllBaseName'."
    InvalidFilePath = "Invalid path / not a file."
    SourceDllNotFound = "Could not locate the modded DLL '$sourceDllBaseName'!"
    SourceDllNotFoundContinue = "Continue anyway? (Y/N/Enter = Yes) - Automatic copy will be skipped."
    SourceDllLocated = "Modded DLL ('$sourceDllBaseName') located at: {0}"

    Step2Title = "Step 2: Backup Original Game DLL ($dllOriginalName)"
    Step2Info1 = "Before modifying, let's safely backup the original game DLL."
    Step2Info2 = "This allows easy restoration if something goes wrong."
    SearchOriginalDll = "Looking for the original DLL '{0}' in the game folder..."
    OriginalDllFound = "Original DLL found!"
    CheckBackupExists = "Checking if a backup ('{0}') already exists..."
    CreatingBackup = "Creating backup: Renaming '{0}' to '{1}'..."
    BackupCreated = "Backup of the original DLL created successfully!"
    BackupExists = "A backup ('{0}') already exists. Skipping rename."
    BackupFailed = "Failed to create backup (rename) of original DLL: '{0}'. Continue anyway? (Y/N/Enter=Yes)"
    OriginalDllNotFound = "Original DLL '{0}' not found in the game folder."
    FinalDllExistsMaybeBackedUp = "The final DLL '{0}' already exists. Backup/replacement might have occurred previously."
    OriginalNotFoundOK = "Seems the original DLL was already removed/backed up. No backup needed now."

    Step3Title = "Step 3: Copy Your Modded DLL to the Game Folder"
    Step3Info1 = "Now, copying the file '{0}' that we found into the game folder,"
    Step3Info2 = "placing it with the final name '{0}'."
    CopyingFromTo = "Copying from '{0}'{1}to '{2}'..."
    CreateTargetDir = "Creating destination folder '{0}'..."
    CopySuccess = "Modded DLL copied successfully into the game!"
    CopyFailed = "Failed to copy the modded DLL to '{0}': {1}. Continue anyway? (Y/N/Enter=Yes)"
    CopyConsistencyError = "Source DLL path ('{0}') is now invalid. Continue anyway? (Y/N/Enter=Yes)"
    SkipCopySourceNotFound = "Skipping Copy: The modded DLL was not located."
    ManualCopyHint = "Ensure you have copied the file manually to '{0}' if needed."
    TargetExistsManual = "Detected that '{0}' exists. Will try to verify/update JSON next."
    TargetMissingManual = "The file '{0}' was also not found at the destination. JSON cannot be verified/updated."

    Step4Title = "Step 4: Verify and Update Manifest (version.json)"
    Step4Info1 = "This step ensures the game's 'version.json' file has the correct information"
    Step4Info2 = "(size and hash) for the DLL '{0}' that is NOW in the game folder."
    Step4Info3 = "Important so the game's launcher/patcher doesn't detect an error."
    CheckFinalDll = "Checking if the final DLL '{0}' exists..."
    FinalDllFoundCalcProps = "Final DLL found. Calculating its properties..."
    CalculatingProps = "Calculating size and hash..."
    PropsCalculated = "Size: {0} | Hash: {1} (Read from '{2}')"
    PropsFailed = "Failed to read properties of the final DLL '{0}': {1}. Continue anyway? (Y/N/Enter=Yes)"
    PropsFailedContinue = "Cannot read the DLL file in the destination. JSON update cancelled."
    ReadingJson = "Reading JSON file: '{0}'..."
    JsonReadSuccess = "JSON read successfully."
    JsonReadFailed = "Failed to read the JSON file '{0}'. Continue anyway? (Y/N/Enter=Yes)"
    SearchingJsonEntry = "Searching for entry for '{0}' in JSON..."
    JsonEntryFound = "Entry found in JSON."
    ComparingJson = "Comparing JSON values with actual file properties..."
    JsonCurrent = "JSON Current : Size={0}, Hash={1}"
    ActualFile = "Actual File : Size={0}, Hash={1}"
    JsonMismatch = "Values differ! The JSON needs to be updated."
    JsonMatch = "Values in JSON are already correct. No update needed."
    JsonEntryNotFound = "Entry '{0}' not found in the version.json file. Continue anyway? (Y/N/Enter=Yes)"
    JsonEntryError = "Error searching/comparing JSON entry: {0}. Continue anyway? (Y/N/Enter=Yes)"
    SavingJson = "Saving changes to version.json..."
    CreatingJsonBackup = "Creating backup of original JSON at '{0}'..."
    JsonSaveSuccess = "version.json updated and saved successfully!"
    JsonSaveFailed = "CRITICAL FAILURE saving the JSON file! Continue anyway? (Y/N/Enter=Yes)"
    JsonSaveFailedError = "Error saving '{0}': {1}. The JSON might be corrupt! Check backup '{2}'."

    SkipJsonTargetMissing = "Skipping JSON verification/update because the final DLL '{0}' was not found."

    Step5Title = "Step 5: Remove Optional AMD DLL (amd_fidelityfx_dx12.dll)"
    Step5Info1 = "This step removes the 'amd_fidelityfx_dx12.dll' file, recommended by some mods."
    Step5Info2 = "It can prevent conflicts, but is optional."
    CheckAmdDll = "Checking if the AMD file '{0}' exists..."
    AmdDllFound = "AMD file found! Removing..."
    AmdDllRemoved = "File removed successfully."
    AmdDllNotFound = "AMD file not found. No removal needed."
    AmdDllRemoveError = "Error trying to remove '{0}': {1}. Continue anyway? (Y/N/Enter=Yes)"

    Step6Title = "Step 6: Completion"
    ScriptFinished = "--- Script Finished ---"
    CompletionInfo1 = "Modification process completed (or attempted)."
    CompletionInfo2 = "Please review messages for any errors/warnings."
    ExitPrompt = "Press Enter to close this window..."
    Error = "Error"
}
$langStrings_PT = @{
    ScriptTitle = "Script Auxiliar de Mod para Marvel Rivals"
    SelectLanguagePrompt = "Select Language / Selecione o Idioma:"
    LanguageSelected = "Português selecionado."
    LanguageLoaded = "Idioma carregado da configuração: Português"
    LoadConfig = "Carregando configuração de '$configFileName'..."
    ConfigLoaded = "Configuração carregada."
    ConfigNotFound = "Arquivo de configuração não encontrado. Um novo será criado."
    SaveConfig = "Salvando configuração..."
    ConfigSaved = "Configuração salva em '$configFileName'."
    ConfigSaveError = "ERRO ao salvar configuração em '{0}'."

    SelectPlatformTitle = "Passo 0: Confirmar / Selecionar Caminho de Instalação"
    SelectPlatformInfo = "Verificando caminhos salvos..."
    PlatformSteam = "Steam"
    PlatformEpic = "Epic Games"
    PlatformCustom = "Criar/Digitar um NOVO caminho personalizado"
    PlatformPrompt = "Escolha um número de caminho (ou ENTER para último caminho):"
    InvalidPlatform = "Seleção inválida. Por favor, digite um número válido."
    UsingPathFrom = "Usando caminho de {0}:"
    CustomPathPrompt = "Por favor, digite o caminho COMPLETO para o diretório BASE do Marvel Rivals:"
    ValidatingPath = "Validando caminho..."
    InvalidCustomPath = "Caminho não encontrado ou não é um diretório. Tente novamente."
    CustomPathConfirmed = "Caminho personalizado definido."
    PathNotFound = "Caminho-padrão não encontrado."

    PathsDefinedTitle = "Caminhos Definidos"
    PathsInfo = "Os seguintes caminhos serão usados:"
    GameBasePath = "Pasta Base do Jogo: {0}"
    JsonFile = "Arquivo JSON: {0}"
    DllTargetDir = "Pasta de DLL do Jogo: {0}"
    DllOriginalFile = "DLL Original (p/ backup): {0}"
    DllFinalFile = "DLL Final (no jogo): {0}"
    AmdDllFile = "DLL AMD Opcional: {0}"

    Step1Title = "Passo 1: Localizar sua DLL Modificada ($sourceDllBaseName)"
    Step1Info = "Vamos encontrar o arquivo '$sourceDllBaseName' que você quer colocar no jogo."
    SearchScriptDir = "Procurando na pasta deste script..."
    FoundInScriptDir = "Encontrada aqui: {0}"
    NotFoundInScriptDir = "Não encontrada na pasta do script."
    CannotFindScriptDir = "Não foi possível determinar a pasta do script automaticamente."
    SearchCustomDir = "Procurando em uma pasta específica..."
    PromptCustomDir = "Digite o caminho da PASTA onde '$sourceDllBaseName' está (ou Enter p/ pular):"
    SkipDirSearch = "Busca por pasta pulada."
    FoundInCustomDir = "Encontrada em: {0}"
    DirExistsButNoFile = "'$sourceDllBaseName' não está nesta pasta."
    InvalidDir = "Caminho de pasta inválido."
    SearchFullPath = "Procurando por caminho completo..."
    PromptFullPath = "Digite o CAMINHO COMPLETO para o arquivo '$sourceDllBaseName' (ou Enter p/ pular):"
    SkipFileSearch = "Busca por arquivo pulada."
    FoundFullPath = "Encontrada em: {0}"
    FileNameMismatch = "Nome do arquivo não corresponde a '$sourceDllBaseName'."
    InvalidFilePath = "Caminho inválido / não é um arquivo."
    SourceDllNotFound = "Não foi possível localizar a DLL modificada '$sourceDllBaseName'!"
    SourceDllNotFoundContinue = "Deseja continuar mesmo assim? (S/N/Enter=Sim) - A cópia automática será pulada."
    SourceDllLocated = "DLL Modificada ('$sourceDllBaseName') localizada em: {0}"

    Step2Title = "Passo 2: Backup da DLL Original do Jogo ($dllOriginalName)"
    Step2Info1 = "Antes de modificar, vamos fazer um backup seguro da DLL original do jogo."
    Step2Info2 = "Isso permite restaurar o jogo facilmente se algo der errado."
    SearchOriginalDll = "Procurando pela DLL original '{0}' na pasta do jogo..."
    OriginalDllFound = "DLL Original encontrada!"
    CheckBackupExists = "Verificando se já existe um backup ('{0}')..."
    CreatingBackup = "Criando backup: Renomeando '{0}' para '{1}'..."
    BackupCreated = "Backup da DLL original criado com sucesso!"
    BackupExists = "Um backup ('{0}') já existe. Pulando renomeação."
    BackupFailed = "Falha ao criar backup (renomear) da DLL original: '{0}'. Deseja continuar assim mesmo? (S/N/Enter=Sim)"
    OriginalDllNotFound = "DLL Original '{0}' não encontrada na pasta do jogo."
    FinalDllExistsMaybeBackedUp = "A DLL final '{0}' já existe. O backup/substituição pode ter ocorrido antes."
    OriginalNotFoundOK = "Parece que a DLL original já foi removida/backupeada. Sem necessidade de backup agora."

    Step3Title = "Passo 3: Copiar sua DLL Modificada para a Pasta do Jogo"
    Step3Info1 = "Agora, vou copiar o arquivo '{0}' que encontramos para a pasta do jogo,"
    Step3Info2 = "colocando-o com o nome final '{0}'."
    CopyingFromTo = "Copiando de '{0}'{1}para '{2}'..."
    CreateTargetDir = "Criando pasta de destino '{0}'..."
    CopySuccess = "DLL Modificada copiada com sucesso para o jogo!"
    CopyFailed = "Falha ao copiar a DLL modificada para '{0}': {1}. Deseja continuar mesmo assim? (S/N/Enter=Sim)"
    CopyConsistencyError = "O caminho da DLL de origem ('{0}') agora é inválido. Deseja continuar mesmo assim? (S/N/Enter=Sim)"
    SkipCopySourceNotFound = "Pulando a cópia: A DLL modificada não foi localizada."
    ManualCopyHint = "Certifique-se de ter copiado o arquivo manualmente para '{0}' se necessário."
    TargetExistsManual = "Detectei que '{0}' existe. Tentarei verificar/atualizar o JSON em seguida."
    TargetMissingManual = "O arquivo '{0}' também não foi encontrado no destino. O JSON não poderá ser verificado/atualizado."

    Step4Title = "Passo 4: Verificar e Atualizar Manifesto (version.json)"
    Step4Info1 = "Este passo garante que o 'version.json' do jogo tenha as informações corretas"
    Step4Info2 = "(tamanho e hash) da DLL '{0}' que agora está na pasta do jogo."
    Step4Info3 = "Importante para o launcher/patcher do jogo não detectar erro."
    CheckFinalDll = "Verificando se a DLL final '{0}' existe..."
    FinalDllFoundCalcProps = "DLL final encontrada. Calculando suas propriedades..."
    CalculatingProps = "Calculando tamanho e hash..."
    PropsCalculated = "Tamanho: {0} | Hash: {1} (Lido de '{2}')"
    PropsFailed = "Falha ao ler propriedades da DLL final '{0}': {1}. Deseja continuar mesmo assim? (S/N/Enter=Sim)"
    PropsFailedContinue = "Não consigo ler o arquivo DLL no destino. Atualização do JSON cancelada."
    ReadingJson = "Lendo o arquivo JSON: '{0}'..."
    JsonReadSuccess = "JSON lido com sucesso."
    JsonReadFailed = "Falha ao ler o arquivo JSON '{0}'. Deseja continuar mesmo assim? (S/N/Enter=Sim)"
    SearchingJsonEntry = "Procurando entrada para '{0}' no JSON..."
    JsonEntryFound = "Entrada encontrada no JSON."
    ComparingJson = "Comparando valores do JSON com as propriedades reais do arquivo..."
    JsonCurrent = "JSON Atual : Tam={0}, Hash={1}"
    ActualFile = "Arquivo Real: Tam={0}, Hash={1}"
    JsonMismatch = "Valores diferentes! O JSON precisa ser atualizado."
    JsonMatch = "Valores no JSON já estão corretos. Nenhuma atualização necessária."
    JsonEntryNotFound = "Entrada '{0}' não encontrada no version.json. Deseja continuar mesmo assim? (S/N/Enter=Sim)"
    JsonEntryError = "Erro ao procurar/comparar entrada no JSON: {0}. Deseja continuar? (S/N/Enter=Sim)"
    SavingJson = "Salvando alterações no version.json..."
    CreatingJsonBackup = "Criando backup do JSON original em '{0}'..."
    JsonSaveSuccess = "version.json atualizado e salvo com sucesso!"
    JsonSaveFailed = "Falha CRÍTICA ao salvar o arquivo JSON! Deseja continuar? (S/N/Enter=Sim)"
    JsonSaveFailedError = "Erro ao salvar '{0}': {1}. O JSON pode estar corrompido! Verifique o backup '{2}'."

    SkipJsonTargetMissing = "Pulando verificação/atualização do JSON pois a DLL final '{0}' não foi encontrada."

    Step5Title = "Passo 5: Remover DLL Opcional da AMD (amd_fidelityfx_dx12.dll)"
    Step5Info1 = "Este passo remove 'amd_fidelityfx_dx12.dll', recomendado por alguns mods."
    Step5Info2 = "Pode evitar conflitos, mas é opcional."
    CheckAmdDll = "Verificando se o arquivo '{0}' existe..."
    AmdDllFound = "Arquivo da AMD encontrado! Removendo..."
    AmdDllRemoved = "Arquivo removido com sucesso."
    AmdDllNotFound = "Arquivo da AMD não encontrado. Nenhuma remoção necessária."
    AmdDllRemoveError = "Erro ao tentar remover '{0}': {1}. Deseja continuar mesmo assim? (S/N/Enter=Sim)"

    Step6Title = "Passo 6: Conclusão"
    ScriptFinished = "--- Script Finalizado ---"
    CompletionInfo1 = "O processo de modificação foi concluído (ou tentado)."
    CompletionInfo2 = "Por favor, revise as mensagens acima para possíveis erros/avisos."
    ExitPrompt = "Pressione Enter para fechar esta janela..."
    Error = "Erro"
}

# --- Global Language Variable ---
$i18n = $null # Will be set after loading config or prompting

# --- Helper Functions ---

# Get localized string
function Get-String {
    param(
        [string]$Key,
        [switch]$Raw
    )
    if ($null -ne $i18n -and $i18n.ContainsKey($Key)) {
        return $i18n[$Key]
    }
    else {
        if ($langStrings_EN.ContainsKey($Key)) {
            if (-not $Raw) { Write-Warning "Lang key '$Key' missing for '$($config.Language)', using EN." }
            return $langStrings_EN[$Key]
        }
        else {
            if (-not $Raw) { Write-Warning "Lang key '$Key' missing in EN!" }
            return "[$Key]"
        }
    }
}

# Display formatted messages using localized strings
function Write-FormattedMessage {
    param (
        [string]$Emoji = "",
        [string]$MessageKey,
        [string]$Color = "White",
        [int]$Indent = 0,
        [object[]]$FormatArgs = @()
    )
    $indentation = " " * ($Indent * 2)
    $localizedMessage = Get-String -Key $MessageKey
    if ($FormatArgs.Count -gt 0) {
        try {
            $formattedMessage = $localizedMessage -f $FormatArgs
        }
        catch {
            Write-Warning "Format error key '$MessageKey': $($_.Exception.Message)"
            $formattedMessage = $localizedMessage
        }
    }
    else {
        $formattedMessage = $localizedMessage
    }

    Write-Host "$indentation$Emoji $formattedMessage" -ForegroundColor $Color
}

# Pergunta de confirmação genérica
function Ask-Confirmation {
    param(
        [Parameter(Mandatory=$true)][string]$PromptKey,
        [object[]]$FormatArgs = @()
    )
    while ($true) {
        $promptText = (Get-String -Key $PromptKey)
        if ($FormatArgs.Count -gt 0) {
            try {
                $promptText = $promptText -f $FormatArgs
            }
            catch {
                Write-Warning "Format error prompt key '$PromptKey': $($_.Exception.Message)"
            }
        }
        $response = Read-Host "$($emojiQuestion) $promptText"
        if ([string]::IsNullOrWhiteSpace($response) -or $response -match '^[YySs]$') {
            return $true
        }
        elseif ($response -match '^[Nn]$') {
            return $false
        }
        else {
            Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidPlatform" -Color Yellow
        }
    }
}

# Pergunta de erro, continuação
function Ask-ToContinueOnError {
    param(
        [string]$ErrorMessage,
        [string]$PromptKey,
        [object[]]$FormatArgs = @()
    )
    Write-Host "$emojiError Error: $ErrorMessage" -ForegroundColor Red
    if (-not (Ask-Confirmation -PromptKey $PromptKey -FormatArgs $FormatArgs)) {
        exit
    }
}

# Lê input com timeout: aguarda o tempo especificado; se usuário não digitar nada, retorna vazio
# Mostra contagem regressiva visual
function Read-HostWithTimeout {
    param(
        [string]$Prompt = "Option:",
        [int]$TimeoutSeconds = 5
    )

    Write-Host "$Prompt " -NoNewline
    Write-Host "(Timeout: " -NoNewline -ForegroundColor DarkGray
    Write-Host "$TimeoutSeconds" -NoNewline -ForegroundColor Yellow
    Write-Host " segundos): " -NoNewline -ForegroundColor DarkGray

    $startTime = Get-Date
    $userInput = ""
    $lastSecond = $TimeoutSeconds

    while ($true) {
        # se já tem algo no buffer, lê
        if ($Host.UI.RawUI.KeyAvailable) {
            $key = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            if ($key.VirtualKeyCode -eq 13) {
                # Enter
                Write-Host ""  # Nova linha após Enter
                break
            }
            elseif ($key.VirtualKeyCode -eq 8 -and $userInput.Length -gt 0) {
                # Backspace
                $userInput = $userInput.Substring(0, $userInput.Length - 1)
                Write-Host "`b `b" -NoNewline  # Apaga o caractere na tela
            }
            elseif ($key.Character -ge 32 -and $key.Character -le 126) {
                # Caracteres imprimíveis
                $userInput += $key.Character
                Write-Host $key.Character -NoNewline
            }
        }
        else {
            $elapsed = (Get-Date) - $startTime
            $remainingSeconds = [Math]::Max(0, $TimeoutSeconds - [Math]::Floor($elapsed.TotalSeconds))

            # Atualiza o contador a cada segundo
            if ($remainingSeconds -ne $lastSecond) {
                $lastSecond = $remainingSeconds
                if ($remainingSeconds -gt 0) {
                    Write-Host "`r$Prompt " -NoNewline
                    Write-Host "(Timeout: " -NoNewline -ForegroundColor DarkGray
                    Write-Host "$remainingSeconds" -NoNewline -ForegroundColor Yellow
                    Write-Host " segundos): $userInput" -NoNewline -ForegroundColor DarkGray
                }
            }

            if ($elapsed.TotalSeconds -ge $TimeoutSeconds) {
                # tempo esgotado
                Write-Host ""  # Nova linha após timeout
                break
            }
            else {
                Start-Sleep -Milliseconds 50
            }
        }
    }
    # Retorna o que o usuário digitou (ou string vazia se não digitou nada)
    return $userInput
}

# Salva config
function Save-Configuration {
    try {
        Write-Host "$emojiSave $(Get-String -Key 'SaveConfig' -Raw)" -ForegroundColor Gray
        $config | ConvertTo-Json -Depth 5 | Set-Content -Path $configFilePath -Encoding UTF8 -Force -ErrorAction Stop
        Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "ConfigSaved" -FormatArgs $configFileName -Color Green -Indent 1
    }
    catch {
        Write-FormattedMessage -Emoji $emojiError -MessageKey "ConfigSaveError" -FormatArgs "$configFilePath`: $($_.Exception.Message)" -Color Red
    }
}

# Carrega config
function Import-Configuration {
    if (Test-Path $configFilePath) {
        try {
            Write-Host "$emojiConfig $(Get-String -Key 'LoadConfig' -Raw)" -ForegroundColor Gray
            $loadedConfig = Get-Content -Path $configFilePath -Raw -Encoding UTF8 -ErrorAction Stop | ConvertFrom-Json -ErrorAction Stop
            if ($loadedConfig -is [hashtable] -or $loadedConfig -is [pscustomobject]) {
                $loadedConfig.PSObject.Properties | ForEach-Object {
                    $config[$_.Name] = $_.Value
                }

                # Garantir que SavedBasePaths seja um array
                if ($config.ContainsKey("SavedBasePaths") -and $config.SavedBasePaths -isnot [array]) {
                    $config.SavedBasePaths = @($config.SavedBasePaths)
                }

                Write-Host "  $emojiSuccess $(Get-String -Key 'ConfigLoaded' -Raw)" -ForegroundColor Green

                # Verificar se há caminhos salvos
                if ($config.ContainsKey("SavedBasePaths") -and $config.SavedBasePaths.Count -gt 0) {
                    $lastPath = $config.SavedBasePaths[-1]
                    Write-Host "  $emojiDefault " -NoNewline -ForegroundColor Yellow
                    Write-Host "Último caminho utilizado: " -NoNewline -ForegroundColor Yellow
                    Write-Host "$lastPath" -ForegroundColor Cyan
                }
            }
            else {
                Write-Host "  $emojiWarning Invalid config format." -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "$emojiError Error loading '$configFileName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "$emojiInfo $(Get-String -Key 'ConfigNotFound' -Raw)" -ForegroundColor Gray
    }
}

# Alias para compatibilidade com o código existente
Set-Alias -Name Load-Configuration -Value Import-Configuration

# Barra de separação
function Write-Separator {
    Write-Host ("-" * 70)
}

#endregion

# --- Main Script Execution ---
Clear-Host
Write-Host "=== Marvel Rivals Mod Helper ==="

# 1. Load Config
Load-Configuration

# Se não existir array para MULTIPLOS caminhos, cria
if (-not $config.ContainsKey("SavedBasePaths")) {
    $config.SavedBasePaths = @()
}

# Se ainda existir config.GameBasePath (legado), migra p/ array
if ($config.ContainsKey("GameBasePath")) {
    $legacyPath = $config.GameBasePath
    if ($legacyPath -and -not $config.SavedBasePaths.Contains($legacyPath)) {
        $config.SavedBasePaths += $legacyPath
    }
    $config.Remove("GameBasePath")
    Save-Configuration
}

# 2. Determinar idioma
if ($config.Language -and ($config.Language -eq 'EN' -or $config.Language -eq 'PT')) {
    if ($config.Language -eq 'PT') {
        $i18n = $langStrings_PT
    }
    else {
        $i18n = $langStrings_EN
    }
    Write-FormattedMessage -Emoji $emojiLang -MessageKey "LanguageLoaded" -Color Green
}
else {
    Write-Host ""
    $langChoice = $null
    while ($langChoice -notin ('1','2')) {
        Write-Host "1: $emojiFlagUS English"
        Write-Host "2: $emojiFlagBR Português"
        $langChoice = Read-Host "$emojiLang $(Get-String -Key 'SelectLanguagePrompt')"
    }
    if ($langChoice -eq '1') {
        $config.Language = 'EN'
        $i18n = $langStrings_EN
    }
    else {
        $config.Language = 'PT'
        $i18n = $langStrings_PT
    }
    Write-FormattedMessage -Emoji $emojiLang -MessageKey "LanguageSelected" -Color Green
    Save-Configuration
}
if ($null -eq $i18n) { $i18n = $langStrings_EN }

# 3. Título
Write-FormattedMessage -Emoji $emojiConfig -MessageKey "ScriptTitle" -Color Magenta
Write-Separator

#region Step 0: Confirm / Select Game Installation Path
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "SelectPlatformTitle" -Color Cyan
Write-Separator

# Variável para controlar se devemos mostrar a tela de seleção de caminho
# Se o parâmetro -ShowPathSelection foi fornecido, usamos ele
$showPathSelection = $ShowPathSelection.IsPresent

# Defaults
$steamDefaultPath = "C:\Program Files (x86)\Steam\steamapps\common\MarvelRivals"
$epicDefaultPath = "C:\Program Files\Epic Games\MarvelRivalsjKtnW"

$finalGamePath = $null
$platformChosen = $false

# Exibe os caminhos salvos com formatação melhorada
function Show-SavedPaths {
    $index = 3
    foreach ($path in $config.SavedBasePaths) {
        $index++
        $isLastPath = ($path -eq $config.SavedBasePaths[-1])

        if ($isLastPath) {
            # Destaca o último caminho utilizado
            Write-Host "$($index): " -NoNewline
            Write-Host "* " -NoNewline -ForegroundColor Yellow
            Write-Host "$path" -ForegroundColor Cyan
        }
        else {
            Write-Host "$($index): $path" -ForegroundColor White
        }
    }
    return $index
}

# Identificar "último caminho utilizado" = o mais recente no array?
$lastPathUsed = $null
if ($config.SavedBasePaths.Count -gt 0) {
    $lastPathUsed = $config.SavedBasePaths[-1]  # pega o último
}

# Verificar se temos um caminho válido para usar automaticamente
if ($lastPathUsed -and (Test-Path $lastPathUsed -PathType Container) -and -not $showPathSelection) {
    # Usar automaticamente o último caminho
    $finalGamePath = $lastPathUsed
    $platformChosen = $true

    # Exibir informação sobre o caminho usado automaticamente
    Write-Host ""
    Write-Host "  * " -NoNewline -ForegroundColor Yellow
    Write-Host "USANDO AUTOMATICAMENTE O ÚLTIMO CAMINHO: " -NoNewline -ForegroundColor Yellow
    Write-Host "$lastPathUsed" -ForegroundColor Cyan
}
else {
    # Se não temos um caminho válido ou showPathSelection é true, mostrar a tela de seleção
    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "SelectPlatformInfo" -Color Gray

    # Exibição com formatação melhorada
    Write-Host ""
    Write-Host "  === $(Get-String -Key 'SelectPlatformTitle') ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "  P $(Get-String -Key 'PlatformPrompt')" -ForegroundColor White
    Write-Host ""

    # Opções principais com formatação
    Write-Host "  1: " -NoNewline
    Write-Host "Steam " -NoNewline -ForegroundColor White
    Write-Host "$(Get-String -Key 'PlatformSteam')" -ForegroundColor White

    Write-Host "  2: " -NoNewline
    Write-Host "Epic " -NoNewline -ForegroundColor White
    Write-Host "$(Get-String -Key 'PlatformEpic')" -ForegroundColor White

    Write-Host "  3: " -NoNewline
    Write-Host "Novo " -NoNewline -ForegroundColor White
    Write-Host "$(Get-String -Key 'PlatformCustom')" -ForegroundColor White

    # Exibir caminhos salvos
    $lastIndex = Show-SavedPaths

    # Exibir informação sobre o último caminho utilizado
    Write-Host ""
    if ($lastPathUsed) {
        Write-Host "  * " -NoNewline -ForegroundColor Yellow
        Write-Host "ULTIMO CAMINHO UTILIZADO: " -NoNewline -ForegroundColor Yellow
        Write-Host "$lastPathUsed" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "  > " -NoNewline -ForegroundColor DarkGray
        Write-Host "Pressione ENTER ou aguarde o timeout para usar o ultimo caminho" -ForegroundColor DarkGray
    }
    Write-Host ""

    # Fazemos a leitura com timeout
    $platformChoice = Read-HostWithTimeout -Prompt "$emojiInput " -TimeoutSeconds 5

    # Se nada digitado e existe lastPathUsed, usar esse
    if ([string]::IsNullOrWhiteSpace($platformChoice) -and $lastPathUsed) {
        $finalGamePath = $lastPathUsed
        $platformChosen = $true
    }
    else {
        # Caso digitado algo: interpretamos a escolha
        switch ($platformChoice) {
            '1' {
                # Steam (sem confirmar)
                if (Test-Path $steamDefaultPath -PathType Container) {
                    $finalGamePath = $steamDefaultPath
                    $platformChosen = $true
                }
                else {
                    Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidCustomPath" -FormatArgs (Get-String -Key 'PathNotFound') -Color Red
                }
            }
            '2' {
                # Epic
                if (Test-Path $epicDefaultPath -PathType Container) {
                    $finalGamePath = $epicDefaultPath
                    $platformChosen = $true
                }
                else {
                    Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidCustomPath" -FormatArgs (Get-String -Key 'PathNotFound') -Color Red
                }
            }
            '3' {
                # Novo custom
                $customPathValid = $false
                while (-not $customPathValid) {
                    $customPath = Read-Host "$emojiInput $(Get-String -Key 'CustomPathPrompt')"
                    Write-FormattedMessage -Emoji $emojiSearch -MessageKey "ValidatingPath" -Color Gray -Indent 1
                    if ([string]::IsNullOrWhiteSpace($customPath) -or -not (Test-Path $customPath -PathType Container)) {
                        Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidCustomPath" -Color Red -Indent 1
                    }
                    else {
                        $finalGamePath = $customPath
                        $customPathValid = $true
                        $platformChosen = $true
                        Write-FormattedMessage -Emoji $emojiConfirm -MessageKey "CustomPathConfirmed" -Color Green -Indent 1
                    }
                }
            }
            default {
                # Talvez digitou 4,5,6 etc (um caminho salvo)
                if ([int]::TryParse($platformChoice, [ref]$null)) {
                    [int]$choiceNumber = [int]$platformChoice
                    if ($choiceNumber -gt 3 -and $choiceNumber -le $lastIndex) {
                        $offset = $choiceNumber - 4
                        if ($offset -ge 0 -and $offset -lt $config.SavedBasePaths.Count) {
                            $candidatePath = $config.SavedBasePaths[$offset]
                            if (Test-Path $candidatePath -PathType Container) {
                                $finalGamePath = $candidatePath
                                $platformChosen = $true
                            }
                            else {
                                Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidCustomPath" -FormatArgs (Get-String -Key 'PathNotFound') -Color Red
                            }
                        }
                        else {
                            Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidPlatform" -Color Red
                        }
                    }
                    else {
                        Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidPlatform" -Color Red
                    }
                }
                else {
                    # Digitou algo mas não é válido
                    Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidPlatform" -Color Red
                }
            }
        }
    }
}

if (-not $platformChosen) {
    # Se ainda não definido, não encerramos script, mas paramos aqui
    Write-Host "No valid path selected. Exiting..."
    exit
}

# Se definimos finalGamePath, salvamos
if ($finalGamePath) {
    # Criar uma nova lista para os caminhos
    $newPaths = @()

    # Adicionar caminhos existentes (exceto o atual) à nova lista
    foreach ($path in $config.SavedBasePaths) {
        # Verificar se o caminho não é igual ao atual e não está vazio
        if ($path -ne $finalGamePath -and -not [string]::IsNullOrWhiteSpace($path)) {
            # Verificar se o caminho não contém o caminho atual (evitar caminhos corrompidos)
            if (-not $path.Contains($finalGamePath)) {
                $newPaths += $path
            }
        }
    }

    # Adicionar o caminho atual ao final da lista
    $newPaths += $finalGamePath

    # Atualizar a lista de caminhos
    $config.SavedBasePaths = $newPaths

    # Manter apenas os 3 caminhos mais recentes
    if ($config.SavedBasePaths.Count -gt 3) {
        $config.SavedBasePaths = $config.SavedBasePaths | Select-Object -Last 3
    }

    # Manter compatibilidade com código legado
    $config.GameBasePath = $finalGamePath

    # Salvar configuração
    Save-Configuration

    # Exibir confirmação
    Write-Host ""
    Write-Host "  * " -NoNewline -ForegroundColor Yellow
    Write-Host "Caminho definido como padrao: " -NoNewline -ForegroundColor Yellow
    Write-Host "$finalGamePath" -ForegroundColor Cyan
}

$gameBaseDir = $config.GameBasePath

# Paths adicionais
$jsonPath       = Join-Path $gameBaseDir "MarvelGame" "version.json"
$dllTargetPath  = Join-Path $gameBaseDir "MarvelGame" "Engine\Plugins\Wwise\ThirdParty\x64_vc170\Release\bin"
$dllOriginalPath= Join-Path $dllTargetPath $dllOriginalName
$targetDllPath  = Join-Path $dllTargetPath $finalDllName
$amdDllPath     = Join-Path $gameBaseDir "MarvelGame" "Marvel\Binaries\Win64\amd_fidelityfx_dx12.dll"

# Exibir informações de caminhos com destaque para o caminho de trabalho
Write-Host ""
Write-Separator
Write-Host "  # " -NoNewline -ForegroundColor Yellow
Write-Host "ENDERECO DE TRABALHO ATUAL" -ForegroundColor Yellow
Write-Host "  " -NoNewline
Write-Host $gameBaseDir -ForegroundColor Cyan -BackgroundColor DarkBlue
Write-Separator

Write-Host ""
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "PathsDefinedTitle" -Color White
Write-Host ""
Write-FormattedMessage -Emoji $emojiFolder -MessageKey "GameBasePath"     -FormatArgs $gameBaseDir     -Color Cyan -Indent 1
Write-FormattedMessage -Emoji $emojiFile   -MessageKey "JsonFile"         -FormatArgs $jsonPath        -Color Gray -Indent 1
Write-FormattedMessage -Emoji $emojiFolder -MessageKey "DllTargetDir"     -FormatArgs $dllTargetPath   -Color Gray -Indent 1
Write-FormattedMessage -Emoji $emojiFile   -MessageKey "DllOriginalFile"  -FormatArgs $dllOriginalPath -Color Gray -Indent 1
Write-FormattedMessage -Emoji $emojiFile   -MessageKey "DllFinalFile"     -FormatArgs $targetDllPath   -Color Gray -Indent 1
Write-FormattedMessage -Emoji $emojiFile   -MessageKey "AmdDllFile"       -FormatArgs $amdDllPath      -Color Gray -Indent 1
Write-Host ""

# Fechar o processo Marvel-Win64-Shipping.exe
Write-Host ""
Write-Separator
Write-Host "  # " -NoNewline -ForegroundColor Yellow
Write-Host "FECHANDO O PROCESSO DO JOGO" -ForegroundColor Yellow
Write-Separator

try {
    $marvelProcess = Get-Process -Name "Marvel-Win64-Shipping" -ErrorAction SilentlyContinue
    if ($marvelProcess) {
        Write-Host "  " -NoNewline
        Write-Host "Processo Marvel-Win64-Shipping.exe encontrado. Tentando fechar..." -ForegroundColor Cyan
        $marvelProcess | Stop-Process -Force
        Start-Sleep -Seconds 2
        $checkProcess = Get-Process -Name "Marvel-Win64-Shipping" -ErrorAction SilentlyContinue
        if (-not $checkProcess) {
            Write-Host "  " -NoNewline
            Write-Host "Processo fechado com sucesso!" -ForegroundColor Green
        } else {
            Write-Host "  " -NoNewline
            Write-Host "Não foi possível fechar o processo completamente. Tente fechar manualmente." -ForegroundColor Yellow
        }
    } else {
        Write-Host "  " -NoNewline
        Write-Host "Processo Marvel-Win64-Shipping.exe não está em execução." -ForegroundColor Gray
    }
} catch {
    Write-Host "  " -NoNewline
    Write-Host "Erro ao tentar fechar o processo: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
#endregion

#region Step 1: Locate Modded DLL ($sourceDllBaseName)
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "Step1Title" -Color Cyan
Write-Separator

Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step1Info" -Color Gray
Write-FormattedMessage -Emoji $emojiSearch -MessageKey "SearchScriptDir" -Color White

if ($scriptDir) {
    $potentialSourcePath = Join-Path $scriptDir $sourceDllBaseName
    if (Test-Path $potentialSourcePath -PathType Leaf) {
        $dllSourcePath = $potentialSourcePath
        $sourceDllFound = $true
        Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "FoundInScriptDir" -FormatArgs $dllSourcePath -Color Green -Indent 1
    }
    else {
        Write-FormattedMessage -Emoji $emojiWarning -MessageKey "NotFoundInScriptDir" -Color Yellow -Indent 1
    }
}
else {
    Write-FormattedMessage -Emoji $emojiWarning -MessageKey "CannotFindScriptDir" -Color Yellow -Indent 1
}

if (-not $sourceDllFound) {
    Write-Host ""
    Write-FormattedMessage -Emoji $emojiSearch -MessageKey "SearchCustomDir" -Color White
    while (-not $sourceDllFound) {
        $inputDir = Read-Host "$emojiInput $(Get-String -Key 'PromptCustomDir')"
        if ([string]::IsNullOrWhiteSpace($inputDir)) {
            Write-FormattedMessage -Emoji $emojiSkip -MessageKey "SkipDirSearch" -Color Yellow -Indent 1
            break
        }
        if (Test-Path $inputDir -PathType Container) {
            $potentialSourcePath = Join-Path $inputDir $sourceDllBaseName
            if (Test-Path $potentialSourcePath -PathType Leaf) {
                $dllSourcePath = $potentialSourcePath
                $sourceDllFound = $true
                Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "FoundInCustomDir" -FormatArgs $dllSourcePath -Color Green -Indent 1
                break
            }
            else {
                Write-FormattedMessage -Emoji $emojiError -MessageKey "DirExistsButNoFile" -Color Red -Indent 1
            }
        }
        else {
            Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidDir" -Color Red -Indent 1
        }
    }
}

if (-not $sourceDllFound) {
    Write-Host ""
    Write-FormattedMessage -Emoji $emojiSearch -MessageKey "SearchFullPath" -Color White
    while (-not $sourceDllFound) {
        $inputFile = Read-Host "$emojiInput $(Get-String -Key 'PromptFullPath')"
        if ([string]::IsNullOrWhiteSpace($inputFile)) {
            Write-FormattedMessage -Emoji $emojiSkip -MessageKey "SkipFileSearch" -Color Yellow -Indent 1
            break
        }
        if (Test-Path $inputFile -PathType Leaf) {
            if ((Get-Item $inputFile).Name -eq $sourceDllBaseName) {
                $dllSourcePath = $inputFile
                $sourceDllFound = $true
                Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "FoundFullPath" -FormatArgs $dllSourcePath -Color Green -Indent 1
                break
            }
            else {
                Write-FormattedMessage -Emoji $emojiError -MessageKey "FileNameMismatch" -Color Red -Indent 1
            }
        }
        else {
            Write-FormattedMessage -Emoji $emojiError -MessageKey "InvalidFilePath" -Color Red -Indent 1
        }
    }
}

Write-Host ""
if (-not $sourceDllFound) {
    Write-FormattedMessage -Emoji $emojiError -MessageKey "SourceDllNotFound" -Color Red
    if (-not (Ask-Confirmation -PromptKey "SourceDllNotFoundContinue")) {
        exit
    }
}
else {
    Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "SourceDllLocated" -FormatArgs $dllSourcePath -Color Green
}
Write-Host ""
#endregion

#region Step 2: Backup Original Game DLL ($dllOriginalName)
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "Step2Title" -Color Cyan
Write-Separator

Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step2Info1" -Color Gray
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step2Info2" -Color Gray

$originalDllRenamedPath = $dllOriginalPath + ".original"

Write-FormattedMessage -Emoji $emojiSearch -MessageKey "SearchOriginalDll" -FormatArgs $dllOriginalPath -Color White

if (Test-Path $dllOriginalPath -PathType Leaf) {
    Write-FormattedMessage -Emoji $emojiFile -MessageKey "OriginalDllFound" -Color White -Indent 1
    try {
        Write-FormattedMessage -Emoji $emojiSearch -MessageKey "CheckBackupExists" -FormatArgs $originalDllRenamedPath -Color White -Indent 1
        if (-not (Test-Path $originalDllRenamedPath)) {
            Write-FormattedMessage -Emoji $emojiBackup -MessageKey "CreatingBackup" -FormatArgs $dllOriginalName, $originalDllRenamedPath -Color Yellow -Indent 1
            Rename-Item -Path $dllOriginalPath -NewName ($dllOriginalName + ".original") -ErrorAction Stop
            Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "BackupCreated" -Color Green -Indent 1
        }
        else {
            Write-FormattedMessage -Emoji $emojiWarning -MessageKey "BackupExists" -FormatArgs $originalDllRenamedPath -Color Yellow -Indent 1
        }
    }
    catch {
        Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "BackupFailed" -FormatArgs $_.Exception.Message
    }
}
else {
    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "OriginalDllNotFound" -FormatArgs $dllOriginalPath -Color Gray -Indent 1
    if (Test-Path $targetDllPath -PathType Leaf) {
        Write-FormattedMessage -Emoji $emojiWarning -MessageKey "FinalDllExistsMaybeBackedUp" -FormatArgs $targetDllPath -Color Yellow -Indent 1
    }
    else {
        Write-FormattedMessage -Emoji $emojiInfo -MessageKey "OriginalNotFoundOK" -Color Gray -Indent 1
    }
}
Write-Host ""
#endregion

#region Step 3: Copy Modded DLL to Game
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "Step3Title" -Color Cyan
Write-Separator

if ($sourceDllFound) {
    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step3Info1" -FormatArgs $sourceDllBaseName -Color Gray
    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step3Info2" -FormatArgs $finalDllName -Color Gray

    if ($dllSourcePath -and (Test-Path $dllSourcePath -PathType Leaf)) {
        Write-FormattedMessage -Emoji $emojiCopy -MessageKey "CopyingFromTo" -FormatArgs $dllSourcePath, "`n                ", $targetDllPath -Color White
        try {
            if (-not (Test-Path $dllTargetPath -PathType Container)) {
                Write-FormattedMessage -Emoji $emojiFolder -MessageKey "CreateTargetDir" -FormatArgs $dllTargetPath -Color Yellow -Indent 1
                New-Item -ItemType Directory -Path $dllTargetPath -Force | Out-Null
            }
            Copy-Item -Path $dllSourcePath -Destination $targetDllPath -Force -ErrorAction Stop
            $copySuccessful = $true
            Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "CopySuccess" -Color Green -Indent 1
        }
        catch {
            $copySuccessful = $false
            Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "CopyFailed" -FormatArgs $targetDllPath, $_.Exception.Message
        }
    }
    else {
        Ask-ToContinueOnError -ErrorMessage "Source DLL path invalid" -PromptKey "CopyConsistencyError" -FormatArgs $dllSourcePath
        $copySuccessful = $false
    }
}
else {
    Write-FormattedMessage -Emoji $emojiSkip -MessageKey "SkipCopySourceNotFound" -Color Yellow
    Write-FormattedMessage -Emoji $emojiWarning -MessageKey "ManualCopyHint" -FormatArgs $targetDllPath -Color Yellow -Indent 1

    if (Test-Path $targetDllPath -PathType Leaf) {
        Write-FormattedMessage -Emoji $emojiInfo -MessageKey "TargetExistsManual" -FormatArgs $targetDllPath -Color Gray -Indent 1
    }
    else {
        Write-FormattedMessage -Emoji $emojiWarning -MessageKey "TargetMissingManual" -FormatArgs $targetDllPath -Color Yellow -Indent 1
    }
}
Write-Host ""
#endregion

#region Step 4: Verify and Update Manifest (version.json)
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "Step4Title" -Color Cyan
Write-Separator

Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step4Info1" -Color Gray
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step4Info2" -FormatArgs $finalDllName -Color Gray
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step4Info3" -Color Gray

Write-FormattedMessage -Emoji $emojiSearch -MessageKey "CheckFinalDll" -FormatArgs $targetDllPath -Color White

if (Test-Path $targetDllPath -PathType Leaf) {
    Write-FormattedMessage -Emoji $emojiFile -MessageKey "FinalDllFoundCalcProps" -Color White -Indent 1

    $fileSize = $null
    $newHash = $null
    $hashCalculated = $false

    try {
        Write-FormattedMessage -Emoji $emojiVerify -MessageKey "CalculatingProps" -Color Gray -Indent 2
        $fileInfo = Get-Item $targetDllPath -ErrorAction Stop
        $fileSize = $fileInfo.Length
        $fileHash = Get-FileHash -Path $targetDllPath -Algorithm MD5 -ErrorAction Stop
        $newHash = $fileHash.Hash.ToLower()
        $hashCalculated = $true
        Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "PropsCalculated" -FormatArgs $fileSize, $newHash, $targetDllPath -Color Green -Indent 2
    }
    catch {
        Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "PropsFailed" -FormatArgs $targetDllPath, $_.Exception.Message
        $hashCalculated = $false
    }

    if ($hashCalculated) {
        $jsonContent = $null
        $jsonReadSuccessfully = $false
        $jsonEntryFound = $false
        $jsonUpdateNeeded = $false

        Write-FormattedMessage -Emoji $emojiSearch -MessageKey "ReadingJson" -FormatArgs $jsonPath -Color White -Indent 1
        try {
            if (Test-Path $jsonPath -PathType Leaf) {
                $jsonContent = Get-Content $jsonPath -Raw -Encoding UTF8 -ErrorAction Stop | ConvertFrom-Json -ErrorAction Stop
                $jsonReadSuccessfully = $true
                Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "JsonReadSuccess" -Color Green -Indent 2
            }
            else {
                throw "JSON file '$jsonPath' not found."
            }
        }
        catch {
            Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "JsonReadFailed" -FormatArgs $jsonPath
            $jsonReadSuccessfully = $false
        }

        if ($jsonReadSuccessfully) {
            $targetJsonEntryPath = "Engine/Plugins/Wwise/ThirdParty/x64_vc170/Release/bin/$finalDllName"
            Write-FormattedMessage -Emoji $emojiSearch -MessageKey "SearchingJsonEntry" -FormatArgs $finalDllName -Color White -Indent 2

            try {
                $fileEntry = $jsonContent.NonUFSFiles | Where-Object { $_.filename -eq $targetJsonEntryPath }
                if ($null -ne $fileEntry) {
                    $jsonEntryFound = $true
                    Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "JsonEntryFound" -Color Green -Indent 3

                    $originalSize = $fileEntry.size
                    $originalHash = $fileEntry.hash.ToLower()

                    Write-FormattedMessage -Emoji $emojiVerify -MessageKey "ComparingJson" -Color Gray -Indent 3
                    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "JsonCurrent" -FormatArgs $originalSize, $originalHash -Color Gray -Indent 3
                    Write-FormattedMessage -Emoji $emojiInfo -MessageKey "ActualFile"  -FormatArgs $fileSize, $newHash -Color Gray -Indent 3

                    if ($originalSize -ne $fileSize -or $originalHash -ne $newHash) {
                        Write-FormattedMessage -Emoji $emojiWarning -MessageKey "JsonMismatch" -Color Yellow -Indent 3
                        $fileEntry.size = $fileSize
                        $fileEntry.hash = $newHash
                        $jsonUpdateNeeded = $true
                    }
                    else {
                        Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "JsonMatch" -Color Green -Indent 3
                    }
                }
                else {
                    Ask-ToContinueOnError -ErrorMessage (Get-String -Key "JsonEntryNotFound" -FormatArgs $targetJsonEntryPath) -PromptKey "JsonEntryError" -FormatArgs "Entry missing"
                }
            }
            catch {
                Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "JsonEntryError" -FormatArgs $_.Exception.Message
                $jsonUpdateNeeded = $false
            }

            if ($jsonEntryFound -and $jsonUpdateNeeded) {
                Write-FormattedMessage -Emoji $emojiSave -MessageKey "SavingJson" -Color Yellow -Indent 2
                try {
                    $backupJsonPath = $jsonPath + ".bak"
                    Write-FormattedMessage -Emoji $emojiBackup -MessageKey "CreatingJsonBackup" -FormatArgs $backupJsonPath -Color Gray -Indent 3
                    Copy-Item -Path $jsonPath -Destination $backupJsonPath -Force -ErrorAction SilentlyContinue

                    $jsonContent | ConvertTo-Json -Depth 10 | Set-Content -Path $jsonPath -Encoding UTF8 -Force -ErrorAction Stop
                    Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "JsonSaveSuccess" -Color Green -Indent 3
                }
                catch {
                    Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "JsonSaveFailedError" -FormatArgs $jsonPath, $_.Exception.Message, $backupJsonPath
                }
            }
        }
    }
}
else {
    Write-FormattedMessage -Emoji $emojiWarning -MessageKey "SkipJsonTargetMissing" -FormatArgs $targetDllPath -Color Yellow -Indent 1
}
Write-Host ""
#endregion

#region Step 5: Remove Optional AMD DLL
Write-Separator
Write-FormattedMessage -Emoji $emojiStep -MessageKey "Step5Title" -Color Cyan
Write-Separator

Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step5Info1" -Color Gray
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "Step5Info2" -Color Gray

Write-FormattedMessage -Emoji $emojiSearch -MessageKey "CheckAmdDll" -FormatArgs $amdDllPath -Color White

try {
    # Verificar se o caminho AMD não é nulo
    if (-not [string]::IsNullOrWhiteSpace($amdDllPath)) {
        if (Test-Path $amdDllPath -PathType Leaf) {
            Write-FormattedMessage -Emoji $emojiWarning -MessageKey "AmdDllFound" -Color Yellow -Indent 1
            Remove-Item -Path $amdDllPath -Force -ErrorAction Stop
            Write-FormattedMessage -Emoji $emojiSuccess -MessageKey "AmdDllRemoved" -Color Green -Indent 1
        }
        else {
            Write-FormattedMessage -Emoji $emojiInfo -MessageKey "AmdDllNotFound" -Color Gray -Indent 1
        }
    }
    else {
        Write-FormattedMessage -Emoji $emojiInfo -MessageKey "AmdDllNotFound" -Color Gray -Indent 1
    }
}
catch {
    Ask-ToContinueOnError -ErrorMessage $_.Exception.Message -PromptKey "AmdDllRemoveError" -FormatArgs (if ([string]::IsNullOrEmpty($amdDllPath)) { "" } else { $amdDllPath }), $_.Exception.Message
}
Write-Host ""
#endregion

#region Step 6: Completion
Write-Separator
Write-FormattedMessage -Emoji $emojiRocket -MessageKey "ScriptFinished" -Color Green
Write-Separator

Write-FormattedMessage -Emoji $emojiInfo -MessageKey "CompletionInfo1" -Color Gray
Write-FormattedMessage -Emoji $emojiInfo -MessageKey "CompletionInfo2" -Color Gray

# Perguntar se deseja reiniciar o script
Write-Host ""
Write-Host "  > " -NoNewline -ForegroundColor Yellow
Write-Host "Pressione SPACEBAR para reiniciar o script, N para reiniciar com seleção de caminho, ou qualquer outra tecla para sair" -ForegroundColor Cyan

$key = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
if ($key.VirtualKeyCode -eq 32) { # 32 = Spacebar
    # Reiniciar o script normalmente (usando último caminho)
    Write-Host "Reiniciando o script..."
    Start-Sleep -Seconds 1
    & $MyInvocation.MyCommand.Path
    exit
}
elseif ($key.Character -eq 'n' -or $key.Character -eq 'N') { # N = Mostrar seleção de caminho
    # Reiniciar o script com seleção de caminho
    Write-Host "Reiniciando o script com seleção de caminho..."
    Start-Sleep -Seconds 1
    & $MyInvocation.MyCommand.Path -ShowPathSelection
    exit
}
#endregion
