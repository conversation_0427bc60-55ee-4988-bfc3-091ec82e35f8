#pragma once

#include "CameraSystemUniversal.h"
#include "CameraAimbotIntegration.h"
#include <string>
#include <sstream>

//---------------------------------------------------------------------
// 🔧	SISTEMA DE DEBUG PARA CÂMERA UNIVERSAL
//---------------------------------------------------------------------
// Ferramentas de debug e monitoramento para o sistema de câmera
//---------------------------------------------------------------------

namespace CameraSystemDebug
{
    // Estrutura para estatísticas de debug
    struct FDebugStats
    {
        uint32_t TotalFramesAnalyzed = 0;
        uint32_t CameraTypeChanges = 0;
        uint32_t TransitionDetections = 0;
        uint32_t SpecialModeActivations = 0;
        uint64_t LastUpdateTime = 0;
        
        std::string CurrentCameraTypeName = "Unknown";
        std::string CurrentTransitionState = "Unknown";
        float CurrentFOV = 0.0f;
        float CurrentAdaptedFOV = 0.0f;
        bool bIsAimbotCompatible = false;
    };

    static FDebugStats DebugStats;
    static bool bDebugEnabled = false;
    static bool bVerboseLogging = false;

    //---------------------------------------------------------------------
    // 🔍	FUNÇÕES DE CONVERSÃO PARA DEBUG
    //---------------------------------------------------------------------

    // Converte tipo de câmera para string
    std::string CameraTypeToString(CameraSystemUniversal::ECameraType type)
    {
        switch (type)
        {
            case CameraSystemUniversal::ECameraType::Standard: return "Standard";
            case CameraSystemUniversal::ECameraType::ThirdPerson: return "ThirdPerson";
            case CameraSystemUniversal::ECameraType::FirstPerson: return "FirstPerson";
            case CameraSystemUniversal::ECameraType::Ability: return "Ability";
            case CameraSystemUniversal::ECameraType::Transformation: return "Transformation";
            case CameraSystemUniversal::ECameraType::ObjectPossession: return "ObjectPossession";
            case CameraSystemUniversal::ECameraType::Ultimate: return "Ultimate";
            case CameraSystemUniversal::ECameraType::Spectator: return "Spectator";
            case CameraSystemUniversal::ECameraType::Cinematic: return "Cinematic";
            case CameraSystemUniversal::ECameraType::Debug: return "Debug";
            case CameraSystemUniversal::ECameraType::Custom: return "Custom";
            default: return "Unknown";
        }
    }

    // Converte estado de transição para string
    std::string TransitionStateToString(CameraSystemUniversal::ECameraTransitionState state)
    {
        switch (state)
        {
            case CameraSystemUniversal::ECameraTransitionState::Stable: return "Stable";
            case CameraSystemUniversal::ECameraTransitionState::Transitioning: return "Transitioning";
            case CameraSystemUniversal::ECameraTransitionState::Blending: return "Blending";
            case CameraSystemUniversal::ECameraTransitionState::Pending: return "Pending";
            default: return "Unknown";
        }
    }

    //---------------------------------------------------------------------
    // 📊	FUNÇÕES DE MONITORAMENTO
    //---------------------------------------------------------------------

    // Atualiza estatísticas de debug
    void UpdateDebugStats()
    {
        if (!bDebugEnabled)
            return;

        auto currentInfo = CameraSystemUniversal::GetCurrentCameraInfo();
        auto currentConfig = CameraAimbotIntegration::GetCurrentConfig();

        // Detectar mudanças
        static CameraSystemUniversal::ECameraType lastCameraType = CameraSystemUniversal::ECameraType::Unknown;
        static CameraSystemUniversal::ECameraTransitionState lastTransitionState = CameraSystemUniversal::ECameraTransitionState::Stable;

        if (currentInfo.Type != lastCameraType)
        {
            DebugStats.CameraTypeChanges++;
            lastCameraType = currentInfo.Type;
            
            if (bVerboseLogging)
            {
                SafetySystem::LogInfo("CameraDebug",
                    ("Camera type changed to: " + CameraTypeToString(currentInfo.Type)).c_str());
            }
        }

        if (currentInfo.TransitionState != lastTransitionState)
        {
            DebugStats.TransitionDetections++;
            lastTransitionState = currentInfo.TransitionState;
            
            if (bVerboseLogging)
            {
                SafetySystem::LogInfo("CameraDebug",
                    ("Transition state changed to: " + TransitionStateToString(currentInfo.TransitionState)).c_str());
            }
        }

        // Detectar ativações de modos especiais
        if (currentInfo.Type == CameraSystemUniversal::ECameraType::Transformation ||
            currentInfo.Type == CameraSystemUniversal::ECameraType::ObjectPossession ||
            currentInfo.Type == CameraSystemUniversal::ECameraType::Ability ||
            currentInfo.Type == CameraSystemUniversal::ECameraType::Ultimate)
        {
            static bool bWasInSpecialMode = false;
            if (!bWasInSpecialMode)
            {
                DebugStats.SpecialModeActivations++;
                bWasInSpecialMode = true;
                
                if (bVerboseLogging)
                {
                    SafetySystem::LogInfo("CameraDebug",
                        ("Special mode activated: " + CameraTypeToString(currentInfo.Type)).c_str());
                }
            }
        }
        else
        {
            static bool bWasInSpecialMode = true;
            bWasInSpecialMode = false;
        }

        // Atualizar estatísticas
        DebugStats.TotalFramesAnalyzed++;
        DebugStats.CurrentCameraTypeName = CameraTypeToString(currentInfo.Type);
        DebugStats.CurrentTransitionState = TransitionStateToString(currentInfo.TransitionState);
        DebugStats.CurrentFOV = currentInfo.FOV;
        DebugStats.CurrentAdaptedFOV = CameraAimbotIntegration::GetAdaptedFOV(mods::fov);
        DebugStats.bIsAimbotCompatible = CameraSystemUniversal::IsCameraCompatibleWithAimbot();
        DebugStats.LastUpdateTime = GetTickCount64();
    }

    //---------------------------------------------------------------------
    // 🎯	FUNÇÕES DE TESTE
    //---------------------------------------------------------------------

    // Testa detecção de diferentes tipos de câmera
    void TestCameraDetection()
    {
        SafetySystem::LogInfo("CameraDebug", "=== TESTE DE DETECÇÃO DE CÂMERA ===");
        
        auto currentInfo = CameraSystemUniversal::GetCurrentCameraInfo();
        
        SafetySystem::LogInfo("CameraDebug", ("Tipo atual: " + CameraTypeToString(currentInfo.Type)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("Estado de transição: " + TransitionStateToString(currentInfo.TransitionState)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("FOV: " + std::to_string(currentInfo.FOV)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("Compatível com aimbot: " + std::string(currentInfo.bIsValid ? "Sim" : "Não")).c_str());
        
        if (currentInfo.ViewTarget)
        {
            SafetySystem::LogInfo("CameraDebug", "ViewTarget válido detectado");
        }
        else
        {
            SafetySystem::LogInfo("CameraDebug", "ViewTarget inválido");
        }
    }

    // Testa adaptação do aimbot
    void TestAimbotAdaptation()
    {
        SafetySystem::LogInfo("CameraDebug", "=== TESTE DE ADAPTAÇÃO DO AIMBOT ===");
        
        auto config = CameraAimbotIntegration::GetCurrentConfig();
        
        SafetySystem::LogInfo("CameraDebug", ("FOV Multiplier: " + std::to_string(config.FOVMultiplier)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("Sensitivity Multiplier: " + std::to_string(config.SensitivityMultiplier)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("Smoothing Multiplier: " + std::to_string(config.SmoothingMultiplier)).c_str());
        SafetySystem::LogInfo("CameraDebug", ("Requires Special Handling: " + std::string(config.bRequiresSpecialHandling ? "Sim" : "Não")).c_str());

        float baseFOV = mods::fov;
        float adaptedFOV = CameraAimbotIntegration::GetAdaptedFOV(baseFOV);
        SafetySystem::LogInfo("CameraDebug", ("FOV Base: " + std::to_string(baseFOV) + " -> Adaptado: " + std::to_string(adaptedFOV)).c_str());
    }

    //---------------------------------------------------------------------
    // 📋	FUNÇÕES DE RELATÓRIO
    //---------------------------------------------------------------------

    // Gera relatório de estatísticas
    std::string GenerateStatsReport()
    {
        std::stringstream report;
        
        report << "=== RELATÓRIO DO SISTEMA DE CÂMERA UNIVERSAL ===\n";
        report << "Frames Analisados: " << DebugStats.TotalFramesAnalyzed << "\n";
        report << "Mudanças de Tipo: " << DebugStats.CameraTypeChanges << "\n";
        report << "Transições Detectadas: " << DebugStats.TransitionDetections << "\n";
        report << "Ativações de Modo Especial: " << DebugStats.SpecialModeActivations << "\n";
        report << "\n";
        report << "Estado Atual:\n";
        report << "  Tipo de Câmera: " << DebugStats.CurrentCameraTypeName << "\n";
        report << "  Estado de Transição: " << DebugStats.CurrentTransitionState << "\n";
        report << "  FOV Atual: " << DebugStats.CurrentFOV << "\n";
        report << "  FOV Adaptado: " << DebugStats.CurrentAdaptedFOV << "\n";
        report << "  Compatível com Aimbot: " << (DebugStats.bIsAimbotCompatible ? "Sim" : "Não") << "\n";
        report << "\n";
        report << "Última Atualização: " << DebugStats.LastUpdateTime << "\n";
        
        return report.str();
    }

    // Salva relatório em arquivo
    void SaveStatsReport()
    {
        std::string report = GenerateStatsReport();
        
        std::ofstream file("camera_system_stats.txt");
        if (file.is_open())
        {
            file << report;
            file.close();
            SafetySystem::LogInfo("CameraDebug", "Relatório salvo em camera_system_stats.txt");
        }
        else
        {
            SafetySystem::LogError("CameraDebug", "Falha ao salvar relatório");
        }
    }

    //---------------------------------------------------------------------
    // 🔧	FUNÇÕES DE CONTROLE
    //---------------------------------------------------------------------

    // Ativa/desativa debug
    void SetDebugEnabled(bool enabled)
    {
        bDebugEnabled = enabled;
        if (enabled)
        {
            SafetySystem::LogInfo("CameraDebug", "Sistema de debug ativado");
            // Resetar estatísticas
            DebugStats = FDebugStats();
        }
        else
        {
            SafetySystem::LogInfo("CameraDebug", "Sistema de debug desativado");
        }
    }

    // Ativa/desativa logging verboso
    void SetVerboseLogging(bool enabled)
    {
        bVerboseLogging = enabled;
        SafetySystem::LogInfo("CameraDebug",
            (std::string("Logging verboso ") + (enabled ? "ativado" : "desativado")).c_str());
    }

    // Função principal de debug (chamada a cada frame se ativado)
    void Update()
    {
        if (!bDebugEnabled)
            return;

        UpdateDebugStats();
        
        // Log periódico (a cada 5 segundos)
        static uint64_t lastPeriodicLog = 0;
        uint64_t currentTime = GetTickCount64();
        if (currentTime - lastPeriodicLog > 5000)
        {
            lastPeriodicLog = currentTime;
            
            if (bVerboseLogging)
            {
                SafetySystem::LogInfo("CameraDebug",
                    ("Stats: Frames=" + std::to_string(DebugStats.TotalFramesAnalyzed) +
                    ", Type=" + DebugStats.CurrentCameraTypeName +
                    ", FOV=" + std::to_string(DebugStats.CurrentFOV)).c_str());
            }
        }
    }

    // Obtém estatísticas atuais
    const FDebugStats& GetStats()
    {
        return DebugStats;
    }

    // Executa todos os testes
    void RunAllTests()
    {
        SafetySystem::LogInfo("CameraDebug", "=== EXECUTANDO TODOS OS TESTES ===");
        TestCameraDetection();
        TestAimbotAdaptation();
        SafetySystem::LogInfo("CameraDebug", "=== TESTES CONCLUÍDOS ===");
    }
}
