#!/usr/bin/env python3
"""
Smart SDK Fixer - Aplica correções inteligentes baseadas na análise real do SDK
Em vez de apenas desabilitar static_assert, corrige os valores para os corretos
"""

import os
import re
import logging
from OffsetAnalyzer import OffsetAnalyzer

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartSDKFixer:
    def __init__(self):
        self.sdk_folder = os.path.join(os.getcwd(), "Game", "SDK", "SDK")
        self.analyzer = OffsetAnalyzer()
        
    def apply_smart_fixes(self):
        """Aplica correções inteligentes baseadas na análise do SDK"""
        logger.info("🧠 Iniciando correções inteligentes do SDK...")
        
        # Analisar estruturas
        analysis_results = {}
        
        files_to_fix = {
            "GameplayTags_structs.hpp": ["FGameplayTagTableRow", "FRestrictedGameplayTagTableRow"],
            "Engine_structs.hpp": ["FAnimNotifyEvent"]
        }
        
        for filename, structs in files_to_fix.items():
            logger.info(f"📝 Analisando {filename}...")
            file_results = self.analyzer.analyze_file(filename)
            analysis_results.update(file_results)
            
            # Aplicar correções no arquivo
            self.fix_file_with_analysis(filename, file_results)
        
        return len(analysis_results)
    
    def fix_file_with_analysis(self, filename: str, analysis_results: dict):
        """Corrige um arquivo baseado na análise"""
        filepath = os.path.join(self.sdk_folder, filename)
        
        if not os.path.exists(filepath):
            logger.error(f"Arquivo não encontrado: {filename}")
            return
        
        # Criar backup se não existir
        backup_path = filepath + '.smart_backup'
        if not os.path.exists(backup_path):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"💾 Backup criado: {filename}.smart_backup")
        
        # Ler arquivo atual
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = 0
        
        for struct_name, data in analysis_results.items():
            logger.info(f"🔧 Corrigindo {struct_name}...")
            
            # Corrigir static_assert de tamanho
            total_size = data['total_size']
            size_pattern = rf'//static_assert\(sizeof\({re.escape(struct_name)}\) == 0x[0-9A-Fa-f]+, "[^"]*"\);'
            size_replacement = f'static_assert(sizeof({struct_name}) == 0x{total_size:06X}, "Wrong size on {struct_name}");'
            
            if re.search(size_pattern, content):
                content = re.sub(size_pattern, size_replacement, content)
                fixes_applied += 1
                logger.info(f"  ✓ Tamanho corrigido: 0x{total_size:06X}")
            
            # Corrigir static_assert de offsets
            for member_name, offset in data['offsets'].items():
                # Padrão para encontrar offset assert comentado ou incorreto
                offset_pattern = rf'//static_assert\(offsetof\({re.escape(struct_name)}, {re.escape(member_name)}\) == 0x[0-9A-Fa-f]+, "[^"]*"\);[^\\n]*'
                offset_replacement = f'static_assert(offsetof({struct_name}, {member_name}) == 0x{offset:06X}, "Member \'{struct_name}::{member_name}\' has a wrong offset!");'
                
                if re.search(offset_pattern, content):
                    content = re.sub(offset_pattern, offset_replacement, content)
                    fixes_applied += 1
                    logger.info(f"  ✓ Offset {member_name} corrigido: 0x{offset:06X}")
        
        # Salvar arquivo corrigido se houve mudanças
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ {filename}: {fixes_applied} correções inteligentes aplicadas")
        else:
            logger.info(f"ℹ️  {filename}: Nenhuma correção necessária")
    
    def test_compilation(self):
        """Testa a compilação após as correções"""
        logger.info("🔍 Testando compilação...")
        
        import subprocess
        try:
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(self.sdk_folder)))
            result = subprocess.run([
                "MSBuild.exe", "JocastaProtocol.sln", 
                "/p:Configuration=Release", "/p:Platform=x64", "/m:4"
            ], capture_output=True, text=True, cwd=project_root)
            
            success = result.returncode == 0
            
            if success:
                logger.info("✅ Compilação bem-sucedida!")
            else:
                logger.error("❌ Falha na compilação")
                # Mostrar erros relacionados a static_assert
                errors = [line for line in result.stdout.split('\n') if 'static_assert' in line or 'error C2338' in line]
                for error in errors[:3]:
                    logger.error(f"  {error}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Erro ao testar compilação: {str(e)}")
            return False
    
    def restore_smart_backups(self):
        """Restaura arquivos dos backups inteligentes"""
        logger.info("🔄 Restaurando backups inteligentes...")
        
        restored = 0
        for filename in ["GameplayTags_structs.hpp", "Engine_structs.hpp", "PhysicsCore_classes.hpp"]:
            filepath = os.path.join(self.sdk_folder, filename)
            backup_path = filepath + '.smart_backup'
            
            if os.path.exists(backup_path):
                with open(backup_path, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
                
                logger.info(f"✅ Restaurado: {filename}")
                restored += 1
        
        logger.info(f"🔄 {restored} arquivos restaurados")
        return restored

def main():
    fixer = SmartSDKFixer()
    
    print("🧠 Smart SDK Fixer - Correções Inteligentes")
    print("=" * 50)
    print("1. Aplicar correções inteligentes")
    print("2. Restaurar backups")
    print("3. Apenas testar compilação")
    
    choice = input("\nEscolha uma opção (1-3): ").strip()
    
    if choice == "1":
        # Aplicar correções inteligentes
        fixes_applied = fixer.apply_smart_fixes()
        
        if fixes_applied > 0:
            logger.info(f"\n🎯 {fixes_applied} estruturas corrigidas")
            
            # Testar compilação
            success = fixer.test_compilation()
            
            if success:
                logger.info("\n✅ SDK corrigido com sucesso usando abordagem inteligente!")
                logger.info("💡 Os static_assert agora têm os valores corretos em vez de serem desabilitados")
            else:
                logger.warning("\n⚠️  Correções aplicadas, mas ainda há erros de compilação")
        else:
            logger.info("ℹ️  Nenhuma correção foi necessária")
    
    elif choice == "2":
        # Restaurar backups
        fixer.restore_smart_backups()
    
    elif choice == "3":
        # Apenas testar
        fixer.test_compilation()
    
    else:
        print("Opção inválida")

if __name__ == "__main__":
    main()
