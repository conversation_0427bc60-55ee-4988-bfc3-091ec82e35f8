#pragma once

#include "CameraSystemUniversal.h"
#include "CameraAimbotIntegration.h"

//---------------------------------------------------------------------
// ⚙️	CONFIGURAÇÃO DO SISTEMA UNIVERSAL DE CÂMERA
//---------------------------------------------------------------------
// Configurações avançadas e presets para o sistema de câmera
//---------------------------------------------------------------------

namespace CameraSystemConfig
{
    // Presets de configuração
    enum class EConfigPreset : uint8_t
    {
        Conservative = 0,       // Configuração conservadora (padrão)
        Aggressive = 1,         // Configuração agressiva
        Precision = 2,          // Foco em precisão
        Speed = 3,              // Foco em velocidade
        Custom = 4              // Configuração personalizada
    };

    //---------------------------------------------------------------------
    // 📋	PRESETS DE CONFIGURAÇÃO
    //---------------------------------------------------------------------

    // Preset Conservador - Seguro para todos os modos
    CameraAimbotIntegration::FAimbotCameraConfig GetConservativeConfig()
    {
        CameraAimbotIntegration::FAimbotCameraConfig config;
        config.FOVMultiplier = 1.0f;
        config.SensitivityMultiplier = 0.8f;
        config.SmoothingMultiplier = 1.5f;
        config.bRequiresSpecialHandling = false;
        config.bUseAlternativeProjection = false;
        return config;
    }

    // Preset Agressivo - Máxima responsividade
    CameraAimbotIntegration::FAimbotCameraConfig GetAggressiveConfig()
    {
        CameraAimbotIntegration::FAimbotCameraConfig config;
        config.FOVMultiplier = 1.2f;
        config.SensitivityMultiplier = 1.3f;
        config.SmoothingMultiplier = 0.7f;
        config.bRequiresSpecialHandling = true;
        config.bUseAlternativeProjection = false;
        return config;
    }

    // Preset Precisão - Foco em acurácia
    CameraAimbotIntegration::FAimbotCameraConfig GetPrecisionConfig()
    {
        CameraAimbotIntegration::FAimbotCameraConfig config;
        config.FOVMultiplier = 0.8f;
        config.SensitivityMultiplier = 0.6f;
        config.SmoothingMultiplier = 2.0f;
        config.bRequiresSpecialHandling = true;
        config.bUseAlternativeProjection = true;
        return config;
    }

    // Preset Velocidade - Resposta rápida
    CameraAimbotIntegration::FAimbotCameraConfig GetSpeedConfig()
    {
        CameraAimbotIntegration::FAimbotCameraConfig config;
        config.FOVMultiplier = 1.1f;
        config.SensitivityMultiplier = 1.5f;
        config.SmoothingMultiplier = 0.5f;
        config.bRequiresSpecialHandling = false;
        config.bUseAlternativeProjection = false;
        return config;
    }

    //---------------------------------------------------------------------
    // 🎯	CONFIGURAÇÕES ESPECÍFICAS POR PERSONAGEM
    //---------------------------------------------------------------------

    // Configurações para personagens com transformações
    void SetupTransformationCharacters()
    {
        // Hulk - Transformação dramática
        auto hulkConfig = GetPrecisionConfig();
        hulkConfig.FOVMultiplier = 0.5f;
        hulkConfig.SmoothingMultiplier = 2.5f;
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Transformation, hulkConfig);

        // Venom - Transformação fluida
        auto venomConfig = GetConservativeConfig();
        venomConfig.FOVMultiplier = 0.7f;
        venomConfig.SmoothingMultiplier = 1.8f;
        // Aplicar para modo de habilidade também
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Ability, venomConfig);
    }

    // Configurações para personagens com incorporação
    void SetupObjectPossessionCharacters()
    {
        // Loki - Incorporação de objetos
        auto lokiConfig = GetPrecisionConfig();
        lokiConfig.SensitivityMultiplier = 0.3f;
        lokiConfig.SmoothingMultiplier = 3.0f;
        lokiConfig.bUseAlternativeProjection = true;
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::ObjectPossession, lokiConfig);
    }

    // Configurações para ultimates especiais
    void SetupUltimateAbilities()
    {
        // Configuração geral para ultimates
        auto ultimateConfig = GetConservativeConfig();
        ultimateConfig.FOVMultiplier = 0.6f;
        ultimateConfig.SensitivityMultiplier = 0.5f;
        ultimateConfig.SmoothingMultiplier = 2.2f;
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Ultimate, ultimateConfig);
    }

    //---------------------------------------------------------------------
    // ⚙️	SISTEMA DE CONFIGURAÇÃO DINÂMICA
    //---------------------------------------------------------------------

    // Aplica preset para todos os modos
    void ApplyPresetToAllModes(EConfigPreset preset)
    {
        CameraAimbotIntegration::FAimbotCameraConfig config;
        
        switch (preset)
        {
            case EConfigPreset::Conservative:
                config = GetConservativeConfig();
                break;
            case EConfigPreset::Aggressive:
                config = GetAggressiveConfig();
                break;
            case EConfigPreset::Precision:
                config = GetPrecisionConfig();
                break;
            case EConfigPreset::Speed:
                config = GetSpeedConfig();
                break;
            default:
                config = GetConservativeConfig();
                break;
        }

        // Aplicar para todos os tipos de câmera
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Standard, config);
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::ThirdPerson, config);
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::FirstPerson, config);
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Custom, config);
    }

    // Aplica configurações específicas por modo
    void ApplyModeSpecificConfigurations()
    {
        SetupTransformationCharacters();
        SetupObjectPossessionCharacters();
        SetupUltimateAbilities();
    }

    //---------------------------------------------------------------------
    // 🔧	CONFIGURAÇÃO AVANÇADA
    //---------------------------------------------------------------------

    // Configuração avançada do sistema de detecção
    void SetupAdvancedDetection()
    {
        CameraSystemUniversal::FCameraAdaptationConfig config;
        
        // Configurações de detecção
        config.bEnableUniversalAdaptation = true;
        config.bDetectTransformations = true;
        config.bDetectObjectPossession = true;
        config.bDetectAbilityCameras = true;
        config.bDetectUltimateCameras = true;
        config.bAdaptToProjectionChanges = true;
        config.bAdaptToFOVChanges = true;
        
        // Thresholds de detecção
        config.MinFOVThreshold = 20.0f;
        config.MaxFOVThreshold = 140.0f;
        config.TransitionDetectionThreshold = 0.05f;
        config.StabilityFrameCount = 3;
        
        CameraSystemUniversal::SetAdaptationConfig(config);
    }

    // Configuração para performance otimizada
    void SetupPerformanceOptimized()
    {
        CameraSystemUniversal::FCameraAdaptationConfig config;
        
        // Reduzir detecções para melhor performance
        config.bEnableUniversalAdaptation = true;
        config.bDetectTransformations = true;
        config.bDetectObjectPossession = false; // Desabilitar se não usado
        config.bDetectAbilityCameras = false;   // Desabilitar se não usado
        config.bDetectUltimateCameras = true;
        config.bAdaptToProjectionChanges = false;
        config.bAdaptToFOVChanges = true;
        
        // Thresholds mais relaxados
        config.TransitionDetectionThreshold = 0.2f;
        config.StabilityFrameCount = 10;
        
        CameraSystemUniversal::SetAdaptationConfig(config);
    }

    //---------------------------------------------------------------------
    // 🎮	CONFIGURAÇÕES POR ESTILO DE JOGO
    //---------------------------------------------------------------------

    // Configuração para jogadores competitivos
    void SetupCompetitiveMode()
    {
        ApplyPresetToAllModes(EConfigPreset::Precision);
        SetupAdvancedDetection();
        
        // Configurações específicas para competitivo
        auto competitiveConfig = GetPrecisionConfig();
        competitiveConfig.FOVMultiplier = 0.9f;
        competitiveConfig.SensitivityMultiplier = 0.8f;
        competitiveConfig.SmoothingMultiplier = 1.2f;
        
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Standard, competitiveConfig);
    }

    // Configuração para jogadores casuais
    void SetupCasualMode()
    {
        ApplyPresetToAllModes(EConfigPreset::Conservative);
        SetupPerformanceOptimized();
        
        // Configurações mais forgiving
        auto casualConfig = GetConservativeConfig();
        casualConfig.FOVMultiplier = 1.1f;
        casualConfig.SensitivityMultiplier = 1.0f;
        casualConfig.SmoothingMultiplier = 1.0f;
        
        CameraAimbotIntegration::SetCameraConfig(CameraSystemUniversal::ECameraType::Standard, casualConfig);
    }

    //---------------------------------------------------------------------
    // 🚀	FUNÇÃO PRINCIPAL DE INICIALIZAÇÃO
    //---------------------------------------------------------------------

    // Inicializa todas as configurações
    void InitializeAllConfigurations()
    {
        // Configuração padrão conservadora
        ApplyPresetToAllModes(EConfigPreset::Conservative);
        
        // Aplicar configurações específicas por modo
        ApplyModeSpecificConfigurations();
        
        // Configuração avançada de detecção
        SetupAdvancedDetection();
        
        SafetySystem::LogInfo("CameraConfig", "Sistema de câmera configurado com sucesso");
    }

    // Inicialização rápida para testes
    void QuickSetup(EConfigPreset preset = EConfigPreset::Conservative)
    {
        ApplyPresetToAllModes(preset);
        SetupAdvancedDetection();
        
        SafetySystem::LogInfo("CameraConfig", ("Configuração rápida aplicada: " + std::to_string(static_cast<int>(preset))).c_str());
    }

    //---------------------------------------------------------------------
    // 📊	FUNÇÕES DE INFORMAÇÃO
    //---------------------------------------------------------------------

    // Obtém nome do preset
    std::string GetPresetName(EConfigPreset preset)
    {
        switch (preset)
        {
            case EConfigPreset::Conservative: return "Conservative";
            case EConfigPreset::Aggressive: return "Aggressive";
            case EConfigPreset::Precision: return "Precision";
            case EConfigPreset::Speed: return "Speed";
            case EConfigPreset::Custom: return "Custom";
            default: return "Unknown";
        }
    }

    // Lista todas as configurações atuais
    void LogCurrentConfigurations()
    {
        SafetySystem::LogInfo("CameraConfig", "=== CONFIGURAÇÕES ATUAIS ===");
        
        auto standardConfig = CameraAimbotIntegration::GetCurrentConfig();
        SafetySystem::LogInfo("CameraConfig", ("FOV Multiplier: " + std::to_string(standardConfig.FOVMultiplier)).c_str());
        SafetySystem::LogInfo("CameraConfig", ("Sensitivity Multiplier: " + std::to_string(standardConfig.SensitivityMultiplier)).c_str());
        SafetySystem::LogInfo("CameraConfig", ("Smoothing Multiplier: " + std::to_string(standardConfig.SmoothingMultiplier)).c_str());
        SafetySystem::LogInfo("CameraConfig", ("Special Handling: " + std::string(standardConfig.bRequiresSpecialHandling ? "Enabled" : "Disabled")).c_str());
        SafetySystem::LogInfo("CameraConfig", ("Alternative Projection: " + std::string(standardConfig.bUseAlternativeProjection ? "Enabled" : "Disabled")).c_str());
    }
}
